# 🔧 Technical Implementation Guide

## Architecture Overview

The Funding Rate & OI Checker is built with a modular architecture designed for reliability, performance, and extensibility.

## 📁 File Structure

```
funding_oi_checker/
├── run_dashboard.py          # Main launcher with dependency checks
├── trading_dashboard.py      # Main GUI application (FundingOIChecker class)
├── data_provider.py          # Bybit API data fetching and processing
├── alert_engine.py           # Alert logic and timing management
├── config_manager.py         # Configuration management
├── bot_listener.py           # Telegram bot integration
├── ui_theme.py              # UI styling and themes
├── config.py                # API keys and basic configuration
├── symbols.json             # List of monitored symbols (400+)
├── dashboard_config.json    # UI and system settings
├── alert_config.json        # Alert thresholds and rules
├── alerts.log              # Alert history log
├── requirements.txt         # Python dependencies
├── README.md               # User documentation
├── ALERT_TYPES.md          # Complete alert types reference
└── TECHNICAL_GUIDE.md      # This technical guide
```

## 🏗️ Core Components

### 1. **AlertTimingManager** (`alert_engine.py`)
Manages the critical alert timing logic:

```python
class AlertTimingManager:
    def __init__(self):
        self.baseline_data = {}          # Initial scan data
        self.previous_scan_data = {}     # Previous scan for comparison
        self.initial_scan_completed = False
        self.auto_refresh_enabled = False
        self.change_thresholds = {       # Minimum changes to trigger alerts
            'funding_rate': 0.001,       # 0.1%
            'oi_change': 0.02,          # 2%
            'cvd': 0.05,                # 5%
            'volume_ratio': 0.3,        # 30%
            'price': 0.005              # 0.5%
        }
```

**Key Methods:**
- `process_scan_data()`: Determines if alerts should be evaluated
- `_detect_significant_changes()`: Compares current vs previous data
- `should_evaluate_alerts()`: Checks if conditions are met for alerting

### 2. **EnhancedAlertEngine** (`alert_engine.py`)
Main alert processing engine with 20 alert types:

```python
class EnhancedAlertEngine:
    def __init__(self, config_manager):
        self.timing_manager = AlertTimingManager()
        self.memory = MultiTimeframeMemory()
        self.cooldown_manager = AlertCooldownManager()
        self.sentiment_scorer = SentimentScorer()
```

**Alert Processing Flow:**
1. `process_market_data()` - Entry point for new data
2. Timing manager checks if alerts should be evaluated
3. If approved, runs through all alert type checkers:
   - `_check_funding_rate_alerts()`
   - `_check_open_interest_alerts()`
   - `_check_squeeze_setups()`
   - `_check_reversal_patterns()`
   - `_check_cvd_alerts()`
   - `_check_multi_factor_alerts()`
   - `_check_volume_spikes()`
4. Applies cooldown filtering
5. Saves to history and sends notifications

### 3. **DataProvider** (`data_provider.py`)
Handles all Bybit API interactions:

```python
class DataProvider:
    def __init__(self):
        self.session = HTTP(api_key=api_key, api_secret=api_secret)
        self.cache = {}
        self.cache_duration = 30  # seconds
```

**Key Features:**
- **Batched Processing**: Handles 400+ symbols in batches of 50
- **Rate Limiting**: 0.1-0.2s delays between API calls
- **Caching**: 30-second cache for repeated requests
- **Error Handling**: Continues processing if individual symbols fail

### 4. **FundingOIChecker** (`trading_dashboard.py`)
Main GUI application:

```python
class FundingOIChecker(QMainWindow):
    def __init__(self):
        self.config_manager = ConfigManager()
        self.data_provider = DataProvider()
        self.alert_engine = EnhancedAlertEngine(self.config_manager)
        self.auto_refresh_enabled = False
```

## ⚡ Alert Timing Implementation

### **Phase 1: Initial Scan (Baseline Collection)**
```python
def manual_refresh(self):
    # Start manual scan - resets baseline
    self.alert_engine.start_manual_scan()
    
    # Process data as manual scan (no alerts)
    self.update_table_data(data, is_manual_scan=True)
    
    # Complete initial scan
    self.alert_engine.complete_initial_scan()
```

### **Phase 2: Auto-Refresh Activation**
```python
def toggle_auto_refresh(self, state):
    self.auto_refresh_enabled = state == 2
    
    # Update alert engine timing
    self.alert_engine.set_auto_refresh_enabled(self.auto_refresh_enabled)
    
    if self.auto_refresh_enabled:
        self.setup_data_updates()  # Start auto-refresh thread
```

### **Phase 3: Alert Evaluation**
```python
def process_market_data(self, data: MarketData, is_manual_scan: bool = False):
    # Always add to memory for trend analysis
    self.memory.add_snapshot(data.symbol, data)
    
    # Process through timing manager
    should_check_alerts = self.timing_manager.process_scan_data(data.symbol, data)
    
    # Skip alerts for manual scans
    if is_manual_scan:
        return []
    
    # Only proceed if significant changes detected and auto-refresh enabled
    if not should_check_alerts:
        return []
    
    # Run alert checks...
```

## 🔄 Data Flow

### **Manual Scan Flow**
```
User clicks "Refresh" 
→ AlertEngine.start_manual_scan() (resets baseline)
→ DataProvider.get_multiple_symbols_data() (batched API calls)
→ AlertEngine.process_market_data(is_manual_scan=True)
→ TimingManager.process_scan_data() (stores baseline, returns False)
→ No alerts generated
→ AlertEngine.complete_initial_scan()
```

### **Auto-Refresh Flow**
```
Auto-refresh timer triggers
→ DataProvider.get_multiple_symbols_data()
→ AlertEngine.process_market_data(is_manual_scan=False)
→ TimingManager.process_scan_data() (compares vs previous)
→ If changes detected: run alert checks
→ Apply cooldown filtering
→ Send Telegram notifications
→ Update UI with alerts
```

## 📊 Performance Optimizations

### **Batched API Processing**
```python
def get_multiple_symbols_data(self, symbols: List[str], batch_size: int = 50):
    # Process in batches to avoid rate limits
    for batch_start in range(0, len(symbols), batch_size):
        batch_symbols = symbols[batch_start:batch_end]
        batch_results = self._process_symbol_batch(batch_symbols)
        time.sleep(1.0)  # Pause between batches
```

### **Change Detection Optimization**
```python
def _detect_significant_changes(self, symbol: str, current_data: MarketData):
    # Only check alerts if meaningful changes occurred
    if symbol not in self.previous_scan_data:
        return True  # First scan after baseline
    
    # Compare each metric against thresholds
    previous_data = self.previous_scan_data[symbol]
    
    # Funding rate change
    fr_change = abs(current_data.funding_rate - previous_data.funding_rate)
    if fr_change >= self.change_thresholds['funding_rate']:
        return True
    
    # ... check other metrics
    return False
```

### **Memory Management**
```python
class MultiTimeframeMemory:
    def __init__(self):
        self.max_snapshots_per_timeframe = {
            '5m': 50,   # ~4 hours
            '15m': 40,  # ~10 hours  
            '30m': 48,  # ~24 hours
            '1h': 72,   # ~3 days
            '4h': 42,   # ~7 days
            '1d': 30    # ~30 days
        }
```

## 🔧 Configuration System

### **Hierarchical Configuration**
1. **Global Defaults** (`alert_config.json`)
2. **Per-Symbol Overrides** (`alert_config.json`)
3. **Runtime Settings** (UI controls)

### **Configuration Loading**
```python
def get_symbol_alert_config(self, symbol: str) -> Dict[str, Any]:
    symbol_config = self.alert_config.get("symbol_specific", {}).get(symbol, {})
    global_defaults = self.alert_config.get("global_defaults", {})
    return {**global_defaults, **symbol_config}  # Symbol overrides global
```

## 🚨 Error Handling

### **API Resilience**
- **Timeout Protection**: 10-second API timeouts
- **Retry Logic**: 3 retry attempts for failed requests
- **Graceful Degradation**: Continue processing other symbols if one fails
- **Rate Limit Respect**: Built-in delays and batch processing

### **Alert System Resilience**
- **Exception Isolation**: Alert processing errors don't crash the system
- **Logging**: Comprehensive logging for debugging
- **State Recovery**: System can recover from temporary failures

## 📈 Monitoring & Debugging

### **Status Indicators**
- **Connection Status**: API connectivity
- **Scan Status**: Current phase (baseline/alert mode)
- **Alert Status**: Alert system state
- **Update Count**: Successful data updates

### **Logging Levels**
- **INFO**: Major state changes, alert generation
- **DEBUG**: Detailed processing information
- **WARNING**: Non-critical issues
- **ERROR**: System errors requiring attention

### **Performance Metrics**
- **Scan Duration**: Time to process all symbols
- **Alert Generation Rate**: Alerts per hour
- **API Success Rate**: Successful vs failed requests
- **Memory Usage**: Historical data storage

This technical implementation ensures reliable, performant monitoring of 400+ crypto symbols with intelligent alert timing and comprehensive error handling.
