# -*- coding: utf-8 -*-
import dearpygui.dearpygui as dpg
import requests
import pandas as pd
import numpy as np
import ta
import re
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
import json
from pathlib import Path
import logging
from logging.handlers import RotatingFileHandler
from typing import Dict, Optional, Tuple, List, Any
from scipy.signal import argrelextrema
import datetime
from pybit.unified_trading import HTTP
import sys
import html # Import html for escaping
import queue # For thread-safe GUI updates
import time # For timestamps
import threading
import json
import os

# --- Logging Configuration ---
def configure_logging(log_file: str = "trading_bot_fibzone_debug.log") -> None:
    logger = logging.getLogger()
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
        handler.close()

    logger.setLevel(logging.DEBUG)
    formatter = logging.Formatter("%(asctime)s %(levelname)s: %(message)s")

    # Console handler
    ch = logging.StreamHandler(stream=sys.stdout)
    ch.setLevel(logging.DEBUG)
    ch.setFormatter(formatter)
    logger.addHandler(ch)
    logging.info("Console logging enabled.")

    # File handler
    try:
        log_path = Path(log_file).resolve()
        log_path.parent.mkdir(parents=True, exist_ok=True)
        fh = RotatingFileHandler(log_path, maxBytes=5 * 1024 * 1024, backupCount=2, encoding='utf-8')
        fh.setLevel(logging.DEBUG)
        fh.setFormatter(formatter)
        logger.addHandler(fh)
        logging.info("File logging configured successfully.")
    except Exception as e:
        logging.warning(f"Failed to configure file logging: {e}")

# --- TTL Cache ---
class TTLCache:
    def __init__(self, ttl: float):
        self.ttl = ttl
        self.cache: Dict[str, Tuple[float, Any]] = {}
        self.lock = threading.Lock()

    def get(self, key: str) -> Optional[Any]:
        with self.lock:
            entry = self.cache.get(key)
            if entry and time.time() - entry[0] < self.ttl:
                return entry[1]
            if entry: del self.cache[key]
        return None

    def set(self, key: str, value: Any) -> None:
        with self.lock:
            self.cache[key] = (time.time(), value)

    def clear(self) -> None:
        with self.lock:
            self.cache.clear()
            logging.debug("Cache cleared.")

# --- Trading Bot ---
class AdvancedTradingBot:
    def __init__(self):
        # --- REPLACE CREDENTIALS ---
        self.api_key = "aMKaaFNd57yeENDYF1"
        self.api_secret = "bKzraCkh1tqEXQBuX0wVobuwz3cyhQEBxnrQ"
        # --- END CREDENTIALS ---

        # Set position mode (one_way or hedge)
        self.position_mode = "one_way"  # Default to one-way mode

        # Store trade data for tracking entry prices and durations
        self.trade_data = {}

        # Load open trades from file if it exists
        try:
            trades_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "open_trades.json")
            if os.path.exists(trades_file):
                with open(trades_file, "r") as f:
                    self.trade_data = json.load(f)
                logging.info(f"Loaded {len(self.trade_data)} open trades from file")
        except Exception as e:
            logging.error(f"Error loading open trades file: {e}")

        # Try to load position mode from config
        try:
            import config
            if hasattr(config, 'default_position_mode'):
                self.position_mode = config.default_position_mode
        except ImportError:
            logging.warning("Config file not found. Using default position mode (one_way).")

        # Validate position mode
        if self.position_mode not in ["one_way", "hedge"]:
            logging.warning(f"Invalid position mode: {self.position_mode}. Using one_way mode.")
            self.position_mode = "one_way"

        logging.info(f"Using position mode: {self.position_mode}")

        # Initialize Bybit API clients
        self.session = None
        self.direct_client = None
        self.time_offset = 0

        try:
            # First try to initialize our direct Bybit client
            try:
                from bybit_direct import BybitDirect
                logging.info("Initializing direct Bybit client...")
                self.direct_client = BybitDirect(self.api_key, self.api_secret)
                self.time_offset = self.direct_client.sync_time()
                logging.info(f"Direct Bybit client initialized successfully with time offset: {self.time_offset} ms")
            except Exception as e:
                logging.warning(f"Could not initialize direct Bybit client: {e}. Falling back to pybit.")
                self.direct_client = None

            # Also initialize the standard pybit client as fallback
            from pybit.unified_trading import HTTP
            logging.info("Initializing pybit client as fallback...")

            # Initialize with a very large recv_window to handle time differences
            self.session = HTTP(
                api_key=self.api_key,
                api_secret=self.api_secret,
                recv_window=120000  # 2 minutes
            )

            logging.info(f"Pybit session initialized successfully as fallback.")
            logging.info(f"Using large recv_window (120 seconds) to handle potential time differences")
        except Exception as e:
            logging.error(f"Failed to initialize Bybit session: {e}", exc_info=True)
            self.session = None
            self.direct_client = None
            self.time_offset = 0

        self.cache = TTLCache(ttl=60)
        self.higher_timeframes = {'5m': '15m', '15m': '1h', '1h': '4h', '4h': '1d', '1d': '1w'}
        self.timeframe_to_interval = {'5m': '5', '15m': '15', '1h': '60', '4h': '240', '1d': 'D', '1w': 'W'}
        self.timeframe_to_oi_interval = {'5m': '5min', '15m': '15min', '1h': '1h', '4h': '4h', '1d': '1d'}
        self.atr_multipliers = {
            '5m': {'sl': 1.8, 'tp': 3.0}, '15m': {'sl': 2.0, 'tp': 3.5}, '1h': {'sl': 2.2, 'tp': 4.0},
            '4h': {'sl': 2.5, 'tp': 4.5}, '1d': {'sl': 3.0, 'tp': 5.0}, '1w': {'sl': 3.5, 'tp': 6.0}
        }
        self.tci_length = 20; self.tci_smooth_length = 5
        self.overbought_level = 60; self.oversold_level = -60
        self.rsi_length = 14; self.stoch_length = 14; self.stoch_smooth_k = 3; self.stoch_smooth_d = 3
        self.mfi_length = 14
        self.supertrend_atr_length = 10; self.supertrend_multiplier = 3.0

    # --- Data Fetching Methods ---
    def fetch_data(self, symbol: str, timeframe: str = '1h', limit: int = 70) -> pd.DataFrame:
        if not self.session and not self.direct_client:
             logging.error("Bybit clients not initialized. Cannot fetch data.")
             return pd.DataFrame(columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

        # Create a cache key for this request
        cache_key = f"{symbol}_{timeframe}_ohlcv_{limit}"

        # Check if we have a cached response
        if hasattr(self, 'cache_obj') and self.cache_obj.get(cache_key) is not None:
            return self.cache_obj.get(cache_key).copy()

        try:
            # Convert timeframe to interval
            interval = self.timeframe_to_interval(timeframe)
            if not interval:
                raise ValueError(f"Unsupported timeframe for OHLCV: {timeframe}")

            # Get klines using our optimized method
            response = self.get_klines(symbol=symbol, interval=interval, limit=limit, category="linear")
            if response['retCode'] == 0 and response['result'] and response['result']['list']:
                data = response['result']['list']
                df = pd.DataFrame(data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume', 'turnover'])
                df['timestamp'] = pd.to_datetime(df['timestamp'].astype(np.int64), unit='ms')
                numeric_cols = ['open', 'high', 'low', 'close', 'volume']
                df[numeric_cols] = df[numeric_cols].apply(pd.to_numeric, errors='coerce')
                df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']]
                df = df.sort_values('timestamp', ascending=True).reset_index(drop=True)
                df.dropna(inplace=True)
                if not df.empty:
                    self.cache_obj.set(cache_key, df)
                    return df.copy()
                else:
                     logging.warning(f"No valid data rows after processing for {symbol} on {timeframe}")
                     return pd.DataFrame(columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            else:
                ret_msg = response.get('retMsg', 'No message')
                if "symbol param invalid" in ret_msg: logging.warning(f"Invalid symbol for Bybit: {symbol}. Skipping.")
                else: logging.warning(f"No data returned for {symbol} on {timeframe}. RC: {response.get('retCode')}, Msg: {ret_msg}")
                return pd.DataFrame(columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        except ValueError as ve:
             logging.error(f"Value error fetching OHLCV for {symbol}: {ve}")
             return pd.DataFrame(columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        except Exception as e:
            logging.error(f"Generic error fetching OHLCV for {symbol} ({timeframe}): {type(e).__name__} - {e}", exc_info=False)
            return pd.DataFrame(columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

    def fetch_open_interest(self, symbol: str, timeframe: str) -> List[float]:
        if not self.session:
            logging.error(f"No API client available for fetching OI for {symbol}")
            return []

        # Create a cache key
        cache_key = f"{symbol}_{timeframe}_oi"

        # Check if we have cached data
        if hasattr(self, 'cache') and isinstance(self.cache, dict) and cache_key in self.cache:
            return self.cache[cache_key]
        elif hasattr(self, 'cache') and hasattr(self.cache, 'get'):
            cached = self.cache.get(cache_key)
            if cached is not None:
                return cached

        try:
            # Hardcode the interval time for now
            if timeframe == '5m': interval_time = '5min'
            elif timeframe == '15m': interval_time = '15min'
            elif timeframe == '1h': interval_time = '1h'
            elif timeframe == '4h': interval_time = '4h'
            elif timeframe == '1d': interval_time = '1d'
            else: interval_time = '1h'

            # Make the API call
            response = self.session.get_open_interest(category="linear", symbol=symbol, intervalTime=interval_time, limit=2)

            if response['retCode'] == 0 and response['result'] and response['result']['list']:
                data = response['result']['list']

                data.reverse()
                if len(data) >= 2:
                    oi_values = [float(item['openInterest']) for item in data]

                    # Store in cache - handle both dictionary and TTLCache
                    if hasattr(self, 'cache'):
                        if isinstance(self.cache, dict):
                            self.cache[cache_key] = oi_values
                        elif hasattr(self.cache, 'set'):
                            try:
                                self.cache.set(cache_key, oi_values)
                            except Exception as cache_err:
                                logging.error(f"Cache error for {symbol}: {cache_err}")

                    return oi_values
                else:
                    logging.warning(f"Not enough OI data points for {symbol}: {len(data)}")
            else:
                logging.warning(f"Invalid OI response for {symbol}: {response}")

            return []
        except Exception as e:
            logging.error(f"Error fetching OI for {symbol} ({timeframe}): {type(e).__name__} - {e}", exc_info=False)
            return []

    def fetch_funding_rate(self, symbol: str) -> Tuple[Optional[float], Optional[str]]:
        if not self.session:
            logging.error(f"No API client available for fetching funding rate for {symbol}")
            return None, None

        # Create a cache key
        cache_key = f"{symbol}_fr"

        # Check if we have cached data
        if hasattr(self, 'cache') and isinstance(self.cache, dict) and cache_key in self.cache:
            return self.cache[cache_key]
        elif hasattr(self, 'cache') and hasattr(self.cache, 'get'):
            cached = self.cache.get(cache_key)
            if cached is not None:
                return cached

        try:
            # Make the API call
            response = self.session.get_funding_rate_history(category="linear", symbol=symbol, limit=1)

            if response['retCode'] == 0 and response['result'] and response['result']['list']:
                data = response['result']['list']

                if data:
                    latest = data[0]
                    fr = float(latest['fundingRate'])
                    ts_ms = int(latest['fundingRateTimestamp'])
                    ts_str = datetime.datetime.fromtimestamp(ts_ms / 1000, tz=datetime.timezone.utc).strftime('%H:%M UTC')

                    # Store in cache - handle both dictionary and TTLCache
                    if hasattr(self, 'cache'):
                        if isinstance(self.cache, dict):
                            self.cache[cache_key] = (fr, ts_str)
                        elif hasattr(self.cache, 'set'):
                            try:
                                self.cache.set(cache_key, (fr, ts_str))
                            except Exception as cache_err:
                                logging.error(f"Cache error for {symbol}: {cache_err}")

                    return fr, ts_str
                else:
                    logging.warning(f"Empty funding rate data list for {symbol}")
            else:
                logging.warning(f"Invalid funding rate response for {symbol}: {response}")

            return None, None
        except Exception as e:
            logging.error(f"Error fetching funding rate for {symbol}: {type(e).__name__} - {e}", exc_info=False)
            return None, None

    # --- Indicator Calculations ---
    def calculate_supertrend(self, df: pd.DataFrame, atr_length: int = 10, multiplier: float = 3.0) -> pd.DataFrame:
        df = df.copy()
        if not all(col in df.columns for col in ['high', 'low', 'close']):
            logging.warning("Missing HLC columns for SuperTrend calculation."); df['supertrend'] = np.nan; df['supertrend_signal'] = 0; return df
        try:
            st_atr = ta.volatility.average_true_range(df['high'], df['low'], df['close'], window=atr_length, fillna=True)
            upper_basic = (df['high'] + df['low']) / 2 + multiplier * st_atr
            lower_basic = (df['high'] + df['low']) / 2 - multiplier * st_atr
            upper_final = upper_basic.copy(); lower_final = lower_basic.copy()
            supertrend = pd.Series(np.nan, index=df.index); signal = pd.Series(0, index=df.index)
            close = df['close']; prev_close = close.shift(1)
            prev_upper_final = upper_final.shift(1); prev_lower_final = lower_final.shift(1)
            prev_supertrend = supertrend.shift(1)
            for i in range(1, len(df)):
                if (upper_basic.iloc[i] < prev_upper_final.iloc[i]) or (prev_close.iloc[i] > prev_upper_final.iloc[i]): upper_final.iloc[i] = upper_basic.iloc[i]
                else: upper_final.iloc[i] = prev_upper_final.iloc[i]
                if (lower_basic.iloc[i] > prev_lower_final.iloc[i]) or (prev_close.iloc[i] < prev_lower_final.iloc[i]): lower_final.iloc[i] = lower_basic.iloc[i]
                else: lower_final.iloc[i] = prev_lower_final.iloc[i]
                if prev_supertrend.iloc[i] == prev_upper_final.iloc[i]:
                    if close.iloc[i] <= upper_final.iloc[i]: supertrend.iloc[i] = upper_final.iloc[i]; signal.iloc[i] = -1
                    else: supertrend.iloc[i] = lower_final.iloc[i]; signal.iloc[i] = 1
                elif prev_supertrend.iloc[i] == prev_lower_final.iloc[i]:
                    if close.iloc[i] >= lower_final.iloc[i]: supertrend.iloc[i] = lower_final.iloc[i]; signal.iloc[i] = 1
                    else: supertrend.iloc[i] = upper_final.iloc[i]; signal.iloc[i] = -1
                else:
                    if close.iloc[i] >= lower_final.iloc[i]: supertrend.iloc[i] = lower_final.iloc[i]; signal.iloc[i] = 1
                    else: supertrend.iloc[i] = upper_final.iloc[i]; signal.iloc[i] = -1
            df['supertrend'] = supertrend; df['supertrend_signal'] = signal.fillna(0).astype(int)
            if len(df) > 1 and df['supertrend_signal'].iloc[0] == 0: df.loc[df.index[0], 'supertrend_signal'] = df['supertrend_signal'].iloc[1]
        except Exception as e:
            logging.error(f"SuperTrend calc error: {type(e).__name__} - {e}", exc_info=False)
            if 'supertrend' not in df.columns: df['supertrend'] = np.nan
            if 'supertrend_signal' not in df.columns: df['supertrend_signal'] = 0
        return df

    def find_divergences(self, df: pd.DataFrame, lookback: int = 5) -> pd.DataFrame:
        """
        Find bullish and bearish divergences in the data
        """
        df = df.copy()
        indicators = {'rsi': 'rsi', 'stoch': 'stoch_rsi_k', 'wt1': 'tci', 'wt2': 'wt2'}
        for k in indicators:
            df[f'bullish_div_{k}'] = False
            df[f'bearish_div_{k}'] = False

        if len(df) < lookback * 2 + 1:
            return df

        symbol_name = df.name if hasattr(df, 'name') else 'N/A' # Get symbol name for logging

        try:
            n = lookback
            low_min = df['low'].rolling(window=n, center=True, min_periods=1).min()
            high_max = df['high'].rolling(window=n, center=True, min_periods=1).max()
            is_trough = (df['low'] == low_min)
            is_peak = (df['high'] == high_max)

            for ind_key, ind_col in indicators.items():
                if ind_col not in df.columns or df[ind_col].isnull().all():
                    continue

                ind_min = df[ind_col].rolling(window=n, center=True, min_periods=1).min()
                ind_max = df[ind_col].rolling(window=n, center=True, min_periods=1).max()
                is_ind_trough = (df[ind_col] == ind_min)
                is_ind_peak = (df[ind_col] == ind_max)
                potential_bull_divs = df[is_trough & is_ind_trough].index
                potential_bear_divs = df[is_peak & is_ind_peak].index

                # Check Bullish Divergence
                for current_idx in potential_bull_divs:
                    prev_troughs = potential_bull_divs[potential_bull_divs < current_idx]
                    if len(prev_troughs) > 0: # Check if list is NOT empty
                        prev_idx = prev_troughs[-1] # Assign only if prev_troughs exists
                        # Check divergence conditions ONLY if prev_idx was assigned
                        if df.loc[current_idx, 'low'] < df.loc[prev_idx, 'low'] and df.loc[current_idx, ind_col] > df.loc[prev_idx, ind_col]:
                            df.loc[current_idx, f'bullish_div_{ind_key}'] = True

                # Check Bearish Divergence
                for current_idx in potential_bear_divs:
                    prev_peaks = potential_bear_divs[potential_bear_divs < current_idx]
                    if len(prev_peaks) > 0: # Check if list is NOT empty
                        prev_idx = prev_peaks[-1] # Assign only if prev_peaks exists
                        # Check divergence conditions ONLY if prev_idx was assigned
                        if df.loc[current_idx, 'high'] > df.loc[prev_idx, 'high'] and df.loc[current_idx, ind_col] < df.loc[prev_idx, ind_col]:
                            df.loc[current_idx, f'bearish_div_{ind_key}'] = True

        except Exception as e:
            logging.error(f"({symbol_name}) Divergence detection error: {type(e).__name__} - {e}", exc_info=True) # Log full traceback

        return df

    def calculate_fibonacci_levels(self, df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """
        Calculate Fibonacci retracement and extension levels
        """
        df = df.copy()
        lookback_period = 20
        fib_cols = ['fib_0', 'fib_236', 'fib_382', 'fib_618', 'fib_1', 'fib_1618', 'fib_2', 'fib_neg618']
        for col in fib_cols:
            df[col] = np.nan

        symbol_name = df.name if hasattr(df, 'name') else 'N/A'
        if len(df) < lookback_period:
            return df

        try:
            recent = df.iloc[-lookback_period:]
            swing_high = recent['high'].max()
            swing_low = recent['low'].min()

            if pd.isna(swing_high) or pd.isna(swing_low):
                logging.warning(f"({symbol_name}) NaN swing H/L for {timeframe}. Cannot calc Fibs.")
                return df

            diff = swing_high - swing_low
            # Handle potential NaN in ATR or close for min_diff calculation
            last_close = df['close'].iloc[-1]
            last_atr = df['atr'].iloc[-1] if 'atr' in df.columns and not pd.isna(df['atr'].iloc[-1]) else np.nan
            if pd.isna(last_atr) and not pd.isna(last_close) and last_close > 0:
                last_atr = last_close * 0.01

            min_diff = 0.01 # Default small value
            if not pd.isna(last_close) and last_close > 0 and not pd.isna(last_atr) and last_atr > 0:
                min_diff = max(last_atr * 3, last_close * 0.005)
            min_diff = max(min_diff, 1e-8)

            if diff < min_diff and diff >= 0:
                mid = (swing_high + swing_low) / 2
                swing_high = mid + min_diff / 2
                swing_low = mid - min_diff / 2
                diff = min_diff
            elif diff < 0:
                logging.warning(f"({symbol_name}) Invalid swing range for {timeframe}. Diff={diff:.4f}.")
                return df

            last_idx = df.index[-1]
            df.loc[last_idx, 'fib_0'] = swing_low
            df.loc[last_idx, 'fib_236'] = swing_low + 0.236 * diff
            df.loc[last_idx, 'fib_382'] = swing_low + 0.382 * diff
            df.loc[last_idx, 'fib_618'] = swing_low + 0.618 * diff
            df.loc[last_idx, 'fib_1'] = swing_high
            df.loc[last_idx, 'fib_1618'] = swing_high + 0.618 * diff
            df.loc[last_idx, 'fib_2'] = swing_high + 1.0 * diff
            df.loc[last_idx, 'fib_neg618'] = swing_low - 0.618 * diff

        except Exception as e:
            logging.error(f"({symbol_name}) Fibonacci calc error ({timeframe}): {type(e).__name__} - {e}", exc_info=False)

        return df

    # This duplicate method has been removed

    def calculate_indicators(self, df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        required_length = 50; symbol_name = df.name if hasattr(df,'name') else 'N/A'
        if df.empty or len(df) < required_length:
            logging.warning(f"({symbol_name}) Insufficient data ({len(df)}) for indicators on {timeframe}. Need {required_length}.")
            return self._create_empty_indicators(df)
        df = df.copy()
        try:
            df['ema_fast'] = ta.trend.ema_indicator(df['close'], window=9, fillna=True)
            df['ema_slow'] = ta.trend.ema_indicator(df['close'], window=50, fillna=True)
            df['rsi'] = ta.momentum.rsi(df['close'], window=self.rsi_length, fillna=True)
            macd = ta.trend.MACD(df['close'], window_slow=26, window_fast=12, window_sign=9, fillna=True)
            df['macd'] = macd.macd(); df['macd_signal'] = macd.macd_signal(); df['macd_hist'] = macd.macd_diff()
            df['volume_ma'] = df['volume'].rolling(window=20, min_periods=1).mean()
            df['volume_spike'] = df['volume'] > (df['volume_ma'] * 1.5)
            df['atr'] = ta.volatility.average_true_range(df['high'], df['low'], df['close'], window=14, fillna=True)
            df['adx'] = ta.trend.adx(df['high'], df['low'], df['close'], window=14, fillna=True)
            df['hlc3'] = (df['high'] + df['low'] + df['close']) / 3
            esa = ta.trend.ema_indicator(df['hlc3'], window=self.tci_length, fillna=True)
            de = ta.trend.ema_indicator(abs(df['hlc3'] - esa), window=self.tci_length, fillna=True)
            ci = (df['hlc3'] - esa) / (0.015 * de + 1e-9)
            df['tci'] = ta.trend.ema_indicator(ci, window=self.tci_smooth_length, fillna=True)
            df['wt2'] = ta.trend.sma_indicator(df['tci'], window=4, fillna=True)
            stoch_rsi = ta.momentum.StochRSIIndicator(df['rsi'], self.stoch_length, self.stoch_smooth_k, self.stoch_smooth_d, fillna=True)
            df['stoch_rsi_k'] = stoch_rsi.stochrsi_k(); df['stoch_rsi_d'] = stoch_rsi.stochrsi_d()
            df['mfi'] = ta.volume.money_flow_index(df['high'], df['low'], df['close'], df['volume'], window=self.mfi_length, fillna=True)
            df = self.calculate_supertrend(df, self.supertrend_atr_length, self.supertrend_multiplier)

            # Call find_divergences here - Ensure it's called correctly
            df = self.find_divergences(df, lookback=5) # This is the line that caused the error

            df = self.calculate_fibonacci_levels(df, timeframe)
            df = df.drop(columns=['hlc3'], errors='ignore')
            return df
        except Exception as e:
            # Log the error with full traceback
            logging.error(f"({symbol_name}) Indicator calc error on {timeframe}: {type(e).__name__} - {e}", exc_info=True)
            return self._create_empty_indicators(df)

    def _create_empty_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        df = df.copy()
        defaults = {
            'ema_fast': np.nan, 'ema_slow': np.nan, 'rsi': np.nan, 'macd': np.nan, 'macd_signal': np.nan, 'macd_hist': np.nan,
            'volume_ma': np.nan, 'volume_spike': False, 'atr': np.nan, 'adx': np.nan, 'tci': np.nan, 'wt2': np.nan,
            'stoch_rsi_k': np.nan, 'stoch_rsi_d': np.nan, 'mfi': np.nan, 'supertrend': np.nan, 'supertrend_signal': 0,
            'bullish_div_rsi': False, 'bearish_div_rsi': False, 'bullish_div_stoch': False, 'bearish_div_stoch': False,
            'bullish_div_wt1': False, 'bearish_div_wt1': False, 'bullish_div_wt2': False, 'bearish_div_wt2': False,
            'fib_0': np.nan, 'fib_236': np.nan, 'fib_382': np.nan, 'fib_618': np.nan, 'fib_1': np.nan,
            'fib_1618': np.nan, 'fib_2': np.nan, 'fib_neg618': np.nan,
        }
        for col, default in defaults.items():
            if col not in df.columns: df[col] = default
        return df

    # --- UPDATED Trade Plan Calculation with DEBUG logging ---
    def calculate_trade_plan(self, df: pd.DataFrame, signal: str, timeframe: str) -> Optional[Dict[str, Any]]:
        if signal == 'HOLD': return None
        last = df.iloc[-1]; symbol_name = df.name if hasattr(df,'name') else 'N/A'
        logging.debug(f"--- Calculating Trade Plan for {symbol_name} {timeframe} {signal} ---")

        atr = last['atr']; entry_price_for_atr = last['close']
        if pd.isna(entry_price_for_atr) or entry_price_for_atr <= 0:
             logging.warning(f"({symbol_name}) Invalid last close price: {entry_price_for_atr}. Cannot calculate trade plan.")
             logging.debug(f"--- End Trade Plan Calculation for {symbol_name} (FAILED - Invalid Close) ---")
             return None

        min_atr_val = entry_price_for_atr * 0.005
        valid_atr = max(atr, min_atr_val) if not pd.isna(atr) and atr > 0 else min_atr_val
        valid_atr = max(valid_atr, 1e-9)
        logging.debug(f"({symbol_name}) Last Close: {entry_price_for_atr:.4f}, Raw ATR: {atr if not pd.isna(atr) else 'NaN':.4f}, Valid ATR: {valid_atr:.4f}")

        fib_levels = {k: last.get(k, np.nan) for k in ['fib_0', 'fib_236', 'fib_382', 'fib_618', 'fib_1', 'fib_1618', 'fib_2', 'fib_neg618']}
        fibs_available = not any(pd.isna(v) for v in fib_levels.values())
        logging.debug(f"({symbol_name}) Fibs Available: {fibs_available}")
        if fibs_available:
             fib_neg618_val = fib_levels.get('fib_neg618')
             fib_neg618_str = f"{fib_neg618_val:.4f}" if not pd.isna(fib_neg618_val) else 'NaN'
             logging.debug(f"({symbol_name}) Fib Levels: 0%={fib_levels['fib_0']:.4f}, 38.2%={fib_levels['fib_382']:.4f}, "
                           f"61.8%={fib_levels['fib_618']:.4f}, 100%={fib_levels['fib_1']:.4f}, "
                           f"161.8%={fib_levels['fib_1618']:.4f}, -61.8%={fib_neg618_str}")

        entry_zone = (np.nan, np.nan); stop_loss, take_profit = np.nan, np.nan
        method = "N/A"; sl_buffer_atr_fraction = 0.25

        if fibs_available:
            try:
                if signal == 'BUY':
                    zone = (fib_levels['fib_382'], fib_levels['fib_618'])
                    sl = fib_levels['fib_0'] - valid_atr * sl_buffer_atr_fraction
                    tp = fib_levels['fib_1618']
                elif signal == 'SELL':
                    zone = (fib_levels['fib_618'], fib_levels['fib_382'])
                    sl = fib_levels['fib_1'] + valid_atr * sl_buffer_atr_fraction
                    tp_val = fib_levels.get('fib_neg618')
                    tp = tp_val if not pd.isna(tp_val) else fib_levels['fib_0'] - valid_atr * 3 # Fallback TP if neg618 is NaN
                else:
                     logging.debug(f"--- End Trade Plan Calculation for {symbol_name} (FAILED - Invalid Signal '{signal}') ---")
                     return None

                logging.debug(f"({symbol_name}) Attempting Fib Plan. Signal: {signal}, Zone Tuple: {zone}, SL: {sl:.4f}, TP: {tp:.4f}")
                entry_zone = (min(zone), max(zone)); stop_loss = sl; take_profit = tp

                if pd.isna(stop_loss) or pd.isna(take_profit) or pd.isna(entry_zone[0]) or pd.isna(entry_zone[1]) or \
                   (signal == 'BUY' and stop_loss >= take_profit) or \
                   (signal == 'SELL' and stop_loss <= take_profit) or \
                   entry_zone[0] < 0 or stop_loss < 0:
                    logging.warning(f"({symbol_name}) Invalid Fib trade plan values. Falling back. "
                                    f"Zone:{entry_zone}, SL:{stop_loss:.4f}, TP:{take_profit:.4f}")
                    fibs_available = False
                else:
                    method = 'Fibonacci Zone'
                    logging.debug(f"({symbol_name}) Fib plan validated. Method: {method}")
            except Exception as e:
                logging.error(f"({symbol_name}) Error in Fib plan calculation block: {e}. Falling back.")
                fibs_available = False

        if not fibs_available:
            method = 'ATR Fallback'; logging.debug(f"({symbol_name}) Using {method}.")
            atr_mult = self.atr_multipliers.get(timeframe, self.atr_multipliers['1h'])
            sl_multiplier = atr_mult['sl']; tp_multiplier = atr_mult['tp']
            entry = entry_price_for_atr; entry_zone = (entry - valid_atr * 0.5, entry + valid_atr * 0.5)

            if signal == 'BUY': stop_loss = entry - valid_atr * sl_multiplier; take_profit = entry + valid_atr * tp_multiplier
            elif signal == 'SELL': stop_loss = entry + valid_atr * sl_multiplier; take_profit = entry - valid_atr * tp_multiplier
            entry_zone = (min(entry_zone), max(entry_zone))

            if pd.isna(stop_loss) or pd.isna(take_profit) or pd.isna(entry_zone[0]) or pd.isna(entry_zone[1]) or \
               (signal == 'BUY' and stop_loss >= take_profit) or \
               (signal == 'SELL' and stop_loss <= take_profit) or \
                entry_zone[0] < 0 or stop_loss < 0:
                logging.error(f"({symbol_name}) Failed to calculate valid ATR fallback plan. Zone:{entry_zone}, SL:{stop_loss:.4f}, TP:{take_profit:.4f}")
                logging.debug(f"--- End Trade Plan Calculation for {symbol_name} (FAILED - ATR Fallback Invalid) ---")
                return None

        logging.debug(f"({symbol_name}) Final Plan Output: Method={method}, Zone={entry_zone}, SL={stop_loss:.4f}, TP={take_profit:.4f}")
        logging.debug(f"--- End Trade Plan Calculation for {symbol_name} (Success) ---")
        return {'entry_zone': entry_zone, 'stop_loss': stop_loss, 'take_profit': take_profit, 'method': method}

    # --- UPDATED Signal Generation with DEBUG logging & TEST ---
    def generate_signal(self, df: pd.DataFrame, timeframe: str) -> Tuple[str, float, Optional[Dict[str, Any]]]:
        required_length = 50; symbol_name = df.name if hasattr(df,'name') else 'N/A'
        if df.empty or len(df) < required_length: return 'HOLD', 0.0, None
        if 'stoch_rsi_k' not in df.columns or 'supertrend_signal' not in df.columns or 'fib_0' not in df.columns:
            df = self.calculate_indicators(df, timeframe) # Ensure indicators, including fibs, are calculated
        essential_cols = ['close', 'ema_fast', 'ema_slow', 'rsi', 'macd_hist', 'atr', 'tci', 'wt2', 'stoch_rsi_k', 'supertrend_signal', 'fib_0']
        last = df.iloc[-1]
        if last[essential_cols].isnull().any():
             nan_indicators = last[essential_cols][last[essential_cols].isnull()].index.tolist()
             logging.warning(f"({symbol_name}) Signal gen skipped due to NaN in essentials: {nan_indicators}")
             return 'HOLD', 0.0, None

        try:
            prev = df.iloc[-2] if len(df) > 1 else last
            ema_fast=last['ema_fast']; ema_slow=last['ema_slow']; trend_up=ema_fast>ema_slow
            macd_hist=last['macd_hist']; momentum_up=macd_hist>0
            tci=last['tci']; wt2=last['wt2']; wave_cross_bull=tci>wt2 and prev['tci']<=prev['wt2']; wave_cross_bear=tci<wt2 and prev['tci']>=prev['wt2']
            wt_oversold=tci<self.oversold_level; wt_overbought=tci>self.overbought_level
            stoch_k=last['stoch_rsi_k']; stoch_d=last['stoch_rsi_d']; stoch_cross_bull=not pd.isna(stoch_d) and stoch_k>stoch_d and prev['stoch_rsi_k']<=prev['stoch_rsi_d']; stoch_cross_bear=not pd.isna(stoch_d) and stoch_k<stoch_d and prev['stoch_rsi_k']>=prev['stoch_rsi_d']
            stoch_oversold=stoch_k<20; stoch_overbought=stoch_k>80
            rsi=last['rsi']; rsi_oversold=rsi<35; rsi_overbought=rsi>65
            volume_spike=last['volume_spike']; atr=last['atr']
            adx=last['adx'] if not pd.isna(last['adx']) else 0; is_trending=adx>20
            st_signal=last['supertrend_signal']; st_up=st_signal==1; st_down=st_signal==-1
            any_bull_div=last.get('bullish_div_rsi',False) or last.get('bullish_div_stoch',False) or last.get('bullish_div_wt1',False) or last.get('bullish_div_wt2',False)
            any_bear_div=last.get('bearish_div_rsi',False) or last.get('bearish_div_stoch',False) or last.get('bearish_div_wt1',False) or last.get('bearish_div_wt2',False)

            buy_score=0; sell_score=0; signal_threshold=3.5; max_possible_score=9.0
            if trend_up and momentum_up and st_up: buy_score+=3.5
            elif not trend_up and not momentum_up and st_down: sell_score+=3.5
            else:
                if st_up: buy_score+=1.5
                if st_down: sell_score+=1.5
                if trend_up and momentum_up: buy_score+=1.0
                if not trend_up and not momentum_up: sell_score+=1.0
            if is_trending:
                if trend_up or st_up: buy_score+=0.5
                if not trend_up or st_down: sell_score+=0.5
            if wave_cross_bull and wt_oversold: buy_score+=1.5
            elif wave_cross_bull: buy_score+=0.5
            if wave_cross_bear and wt_overbought: sell_score+=1.5
            elif wave_cross_bear: sell_score+=0.5
            if stoch_cross_bull and stoch_oversold: buy_score+=1.0
            elif stoch_cross_bull: buy_score+=0.3
            if stoch_cross_bear and stoch_overbought: sell_score+=1.0
            elif stoch_cross_bear: sell_score+=0.3
            if any_bull_div: buy_score+=1.0
            if any_bear_div: sell_score+=1.0

            initial_signal = 'HOLD'; raw_confidence = 0
            if buy_score >= signal_threshold and buy_score > sell_score: initial_signal = 'BUY'; raw_confidence = buy_score
            elif sell_score >= signal_threshold and sell_score > buy_score: initial_signal = 'SELL'; raw_confidence = sell_score
            else: raw_confidence = max(buy_score, sell_score)

            # <<< ADDED LOGGING HERE >>>
            logging.debug(f"({symbol_name}) Post-Score Check | "
                          f"Initial Signal: {initial_signal} | "
                          f"Score B/S: {buy_score:.2f}/{sell_score:.2f} vs Threshold: {signal_threshold}")
            # <<< END ADDED LOGGING >>>

            trade_plan = None; zone_note = ""; final_signal = initial_signal
            # <<< TEST: Force penalty to zero >>>
            outside_zone_penalty = 0

            if final_signal != 'HOLD':
                # <<< ADDED DEBUG >>>
                logging.debug(f"({symbol_name}) Initial signal is {final_signal}. Attempting trade plan calculation...")
                trade_plan = self.calculate_trade_plan(df, final_signal, timeframe)

                # <<< MODIFIED Check >>>
                if trade_plan:
                    logging.debug(f"({symbol_name}) Trade plan calculation SUCCEEDED. Method: {trade_plan.get('method', 'N/A')}")
                    last_close = last['close']
                    entry_lower, entry_upper = trade_plan.get('entry_zone', (np.nan, np.nan))

                    if not pd.isna(last_close) and not pd.isna(entry_lower) and not pd.isna(entry_upper):
                        is_outside = (final_signal == 'BUY' and last_close > entry_upper) or \
                                     (final_signal == 'SELL' and last_close < entry_lower)
                        if is_outside:
                             # Penalty currently 0 for testing
                             zone_note = "(Pullback Suggested)"
                             if 'note' not in trade_plan: trade_plan['note'] = zone_note
                             logging.info(f"{symbol_name} {timeframe}: {final_signal} signal, but price ({last_close:.4f}) " # INFO Log
                                          f"is outside entry zone ({entry_lower:.4f}-{entry_upper:.4f}). Penalty currently disabled for test.")
                        else:
                             logging.debug(f"({symbol_name}) Price ({last_close:.4f}) is INSIDE entry zone ({entry_lower:.4f}-{entry_upper:.4f}).") # Added
                    else:
                        logging.warning(f"({symbol_name}) Cannot validate price vs zone due to NaN values.")

                else: # Trade plan calculation failed
                    # <<< THIS IS LIKELY WHERE THE SIGNAL IS REVERTED >>>
                    logging.warning(f"{symbol_name} {timeframe}: Trade plan calculation FAILED for {final_signal} signal. Reverting to HOLD.") # Warning Log
                    final_signal = 'HOLD'

            confidence = min(100, max(0, (raw_confidence / max_possible_score) * 100))
            confidence = max(0, confidence - outside_zone_penalty) # Apply penalty (0 for test)
            if final_signal == 'HOLD': confidence = min(confidence, 40)

            # <<< Final Check Log >>>
            logging.debug(f"{symbol_name} {timeframe}: Final Check | "
                          f"Initial Signal: {initial_signal}, Final Signal: {final_signal} | "
                          f"Raw Score: {raw_confidence:.2f}/{max_possible_score:.1f} | "
                          f"Final Confidence: {confidence:.1f}% | "
                          f"Trade Plan Exists: {trade_plan is not None} | "
                          f"Zone Note: '{zone_note}'")

            return final_signal, confidence, trade_plan

        except Exception as e:
            logging.error(f"({symbol_name}) Signal generation error on {timeframe}: {type(e).__name__} - {e}", exc_info=True) # Log full traceback
            return 'HOLD', 0.0, None

    # --- Helper Methods ---
    def check_current_divergence(self, df: pd.DataFrame) -> Tuple[bool, bool]:
        # This is a simplified check and less reliable than the main divergence logic
        if len(df) < 2: return False, False
        last=df.iloc[-1]; prev=df.iloc[-2]
        req=['low','high','rsi','stoch_rsi_k','tci','wt2']
        if last[req].isnull().any() or prev[req].isnull().any(): return False, False
        try:
             bull=(last['low']<prev['low']) and ((last['rsi']>prev['rsi']) or (last['stoch_rsi_k']>prev['stoch_rsi_k']) or (last['tci']>prev['tci']) or (last['wt2']>prev['wt2']))
             bear=(last['high']>prev['high']) and ((last['rsi']<prev['rsi']) or (last['stoch_rsi_k']<prev['stoch_rsi_k']) or (last['tci']<prev['tci']) or (last['wt2']<prev['wt2']))
             return bull, bear
        except Exception as e: logging.error(f"Error in check_current_divergence: {e}", exc_info=False); return False, False

    def analyze_higher_timeframe(self, symbol: str, timeframe: str) -> str:
        higher_tf = self.higher_timeframes.get(timeframe)
        if not higher_tf: return 'N/A'
        df_htf = self.fetch_data(symbol, higher_tf, limit=60)
        if df_htf.empty or len(df_htf) < 50: return 'N/A'
        df_htf = self.calculate_indicators(df_htf, higher_tf)
        if df_htf.empty or not all(c in df_htf.columns for c in ['ema_fast','ema_slow','supertrend_signal']) or \
           df_htf[['ema_fast','ema_slow','supertrend_signal']].iloc[-1].isnull().any(): return 'N/A'
        last=df_htf.iloc[-1]; htf_ema_up=last['ema_fast']>last['ema_slow']; htf_st_up=last['supertrend_signal']==1
        if htf_ema_up and htf_st_up: return '🔼 Up'
        if not htf_ema_up and not htf_st_up: return '🔽 Down'
        if htf_ema_up and not htf_st_up: return ' Konfl (EMA Up / ST Down)'
        if not htf_ema_up and htf_st_up: return ' Konfl (EMA Down / ST Up)'
        return '❓ Unclear'

    def determine_market_type(self, df: pd.DataFrame) -> str:
        if len(df)<20 or not all(c in df.columns for c in ['adx','ema_fast','ema_slow']): return 'Uncertain'
        last=df.iloc[-1]; adx=last['adx'] if not pd.isna(last['adx']) else 0
        ema_fast=last['ema_fast']; ema_slow=last['ema_slow']
        if pd.isna(ema_fast) or pd.isna(ema_slow): return 'Uncertain'
        if adx>25: return '📈 Trend Up' if ema_fast>ema_slow else '📉 Trend Down'
        elif adx<20: return '횡보 Range'
        else: return '⏳ Developing'

    def get_position_mode(self, symbol: str) -> str:
        """
        Get the current position mode for a symbol from the Bybit API

        Args:
            symbol: Trading symbol (e.g., BTCUSDT)

        Returns:
            Position mode: "hedge_mode" or "one_way_mode"
        """
        try:
            # Try to get the position mode by checking positions
            if self.session:
                try:
                    # Get positions for the symbol
                    response = self.session.get_positions(category="linear", symbol=symbol)
                    if response['retCode'] == 0 and response['result'] and response['result']['list']:
                        positions = response['result']['list']

                        # Check if there are multiple positions for the same symbol
                        # In hedge mode, you can have both long and short positions
                        if len(positions) > 1:
                            logging.info(f"Detected hedge mode for {symbol} (multiple positions)")
                            return "hedge_mode"

                        # Check positionIdx - in hedge mode, it's 1 for Buy and 2 for Sell
                        # In one-way mode, it's always 0
                        if positions and 'positionIdx' in positions[0]:
                            position_idx = positions[0]['positionIdx']
                            if position_idx in [1, 2]:
                                logging.info(f"Detected hedge mode for {symbol} (positionIdx={position_idx})")
                                return "hedge_mode"
                            elif position_idx == 0:
                                logging.info(f"Detected one-way mode for {symbol} (positionIdx=0)")
                                return "one_way_mode"
                except Exception as e:
                    logging.warning(f"Error checking positions for mode detection: {e}")

            # If we couldn't determine from positions, try to get position mode settings
            if self.session:
                try:
                    # Try to get position mode settings
                    response = self.session.get_user_leverage(category="linear", symbol=symbol)
                    if response['retCode'] == 0 and response['result']:
                        # Check if there are separate leverage settings for long and short
                        result = response['result']
                        if 'buyLeverage' in result and 'sellLeverage' in result and result['buyLeverage'] != result['sellLeverage']:
                            logging.info(f"Detected hedge mode for {symbol} (different leverages)")
                            return "hedge_mode"
                except Exception as e:
                    logging.warning(f"Error checking leverage for mode detection: {e}")

            # If we still couldn't determine, use a safer approach - try both modes
            # First try with hedge mode (positionIdx=1 for Buy)
            try_hedge = False
            if self.session:
                try:
                    # Try to place a tiny order with hedge mode
                    order_params = {
                        "category": "linear",
                        "symbol": symbol,
                        "side": "Buy",
                        "orderType": "Limit",
                        "qty": "0.001",  # Tiny amount that won't execute
                        "price": "0.00000001",  # Extremely low price that won't execute
                        "timeInForce": "PostOnly",  # Ensures it won't execute
                        "positionIdx": 1,  # Hedge mode - Buy side
                        "reduceOnly": False
                    }

                    # We don't actually place the order, just check if the parameters are valid
                    # This is just to determine if hedge mode is supported
                    try_hedge = True
                except Exception as e:
                    logging.warning(f"Error preparing hedge mode test: {e}")

            # If we couldn't determine, default to one-way mode as it's more commonly used
            logging.warning(f"Could not determine position mode for {symbol}. Using one-way mode.")
            return "one_way_mode"
        except Exception as e:
            logging.error(f"Error in get_position_mode: {e}")
            # Default to one-way mode as it's safer
            return "one_way_mode"

    # --- Trading Execution Methods ---
    def place_order(self, symbol: str, side: str, entry_price: float, stop_loss: float, take_profit: float,
                   leverage: int = 10, amount_usd: float = 100.0) -> Dict[str, Any]:
        """
        Place an order with Bybit with specified leverage, amount, and automatic TP/SL using direct API calls

        Args:
            symbol: Trading symbol (e.g., BTCUSDT)
            side: Trade direction ('Buy' or 'Sell')
            entry_price: Entry price for the order
            stop_loss: Stop loss price
            take_profit: Take profit price
            leverage: Leverage multiplier (default: 10x)
            amount_usd: Amount in USD to trade (default: $100)

        Returns:
            Dictionary with order result details
        """
        if not self.session:
            logging.error(f"Cannot place order: Bybit session not initialized")
            return {"success": False, "message": "Bybit session not initialized"}

        try:
            # Try to use our direct client first if available
            if self.direct_client:
                logging.info("Using direct Bybit client for order placement")
                return self._place_order_direct(symbol, side, entry_price, stop_loss, take_profit, leverage, amount_usd)
            elif not self.session:
                logging.error("No Bybit client available for order placement")
                return {"success": False, "message": "No Bybit client available"}

            # Try to set leverage explicitly
            try:
                if self.direct_client:
                    # Set leverage for the symbol
                    leverage_result = self.direct_client.set_leverage(
                        category="linear",
                        symbol=symbol,
                        buyLeverage=str(leverage),
                        sellLeverage=str(leverage)
                    )
                    if leverage_result.get('retCode') == 0:
                        logging.info(f"Successfully set leverage to {leverage}x for {symbol}")
                    else:
                        logging.warning(f"Failed to set leverage for {symbol}: {leverage_result.get('retMsg')}")
                else:
                    logging.info(f"Using leverage {leverage}x for {symbol} (persistent setting on Bybit)")
            except Exception as e:
                logging.warning(f"Error setting leverage for {symbol}: {e}")
                logging.info(f"Using leverage {leverage}x for {symbol} (persistent setting on Bybit)")

            # Calculate quantity based on amount and leverage
            # For a $100 position with 10x leverage, we're actually using $10
            actual_amount = amount_usd / leverage

            # Get current price if needed
            if entry_price <= 0:
                try:
                    ticker_result = self.session.get_tickers(category="linear", symbol=symbol)
                    if ticker_result['retCode'] != 0:
                        return {"success": False, "message": f"Failed to get ticker: {ticker_result['retMsg']}"}
                    entry_price = float(ticker_result['result']['list'][0]['lastPrice'])
                    logging.info(f"Got current price for {symbol}: {entry_price}")
                except Exception as e:
                    logging.error(f"Error getting ticker: {e}")
                    return {"success": False, "message": f"Error getting current price: {str(e)}"}

            # Calculate quantity in base currency
            qty = actual_amount / entry_price

            # Get the lot size filter from the instrument info to format quantity correctly
            try:
                instrument_info = self.session.get_instruments_info(category="linear", symbol=symbol)

                if instrument_info['retCode'] == 0 and len(instrument_info['result']['list']) > 0:
                    contract_info = instrument_info['result']['list'][0]

                    # Get the lot size filter
                    if 'lotSizeFilter' in contract_info:
                        min_qty = float(contract_info['lotSizeFilter'].get('minOrderQty', '0.0001'))
                        qty_step = float(contract_info['lotSizeFilter'].get('qtyStep', '0.0001'))

                        # Round the quantity to the nearest step size
                        qty = round(qty / qty_step) * qty_step

                        # Ensure the quantity is at least the minimum
                        qty = max(qty, min_qty)

                        # Format with enough decimal places to handle the step size
                        decimal_places = len(str(qty_step).split('.')[-1])
                        qty_str = f"{qty:.{decimal_places}f}"

                        logging.info(f"Formatted quantity: {qty_str} (min: {min_qty}, step: {qty_step})")
                    else:
                        # Fallback to default formatting
                        qty_str = f"{qty:.4f}"
                else:
                    # Fallback to default formatting
                    qty_str = f"{qty:.4f}"
            except Exception as e:
                logging.warning(f"Error formatting quantity: {e}. Using default formatting.")
                # Fallback to default formatting
                qty_str = f"{qty:.4f}"

            logging.info(f"Placing {side} order for {symbol}: {qty_str} @ market price with TP: {take_profit}, SL: {stop_loss}")

            # Place the order
            try:
                # For futures markets, we'll use linear USDT perpetual contracts
                # These are the most common and liquid futures markets on Bybit

                # Verify that the symbol exists in the linear category
                try:
                    instrument_info = self.session.get_instruments_info(
                        category="linear",
                        symbol=symbol
                    )

                    if instrument_info['retCode'] == 0 and len(instrument_info['result']['list']) > 0:
                        # Symbol exists in linear category
                        contract_info = instrument_info['result']['list'][0]
                        logging.info(f"Symbol {symbol} found in linear futures category")

                        # Log some useful information about the contract
                        if 'lotSizeFilter' in contract_info:
                            min_qty = contract_info['lotSizeFilter'].get('minOrderQty', 'N/A')
                            logging.info(f"Minimum order quantity for {symbol}: {min_qty}")

                        if 'priceFilter' in contract_info:
                            tick_size = contract_info['priceFilter'].get('tickSize', 'N/A')
                            logging.info(f"Price tick size for {symbol}: {tick_size}")
                    else:
                        logging.warning(f"Symbol {symbol} not found in linear futures category. Order may fail.")
                except Exception as e:
                    logging.warning(f"Error checking symbol {symbol} in linear futures category: {e}")

                # Log the category being used
                logging.info(f"Using category 'linear' for {symbol} (USDT perpetual futures)")

                # Prepare order parameters for futures trading
                order_params = {
                    "category": "linear",  # Always linear for USDT perpetual futures
                    "symbol": symbol,
                    "side": side,
                    "orderType": "Market",  # Using market order for simplicity
                    "qty": qty_str,
                    "takeProfit": str(take_profit),
                    "stopLoss": str(stop_loss),
                    "timeInForce": "GTC",
                    "reduceOnly": False  # Not reducing position, creating a new one
                }

                # Set positionIdx based on position mode
                if self.position_mode == "hedge":
                    if side == "Buy":
                        order_params["positionIdx"] = 1  # Hedge mode - Buy side
                    else:  # Sell
                        order_params["positionIdx"] = 2  # Hedge mode - Sell side
                else:  # one_way mode
                    order_params["positionIdx"] = 0  # One-way mode

                logging.info(f"Set positionIdx={order_params['positionIdx']} for {self.position_mode} mode")
                logging.info(f"Order parameters: {order_params}")

                # Use direct client if available, otherwise fall back to session
                if self.direct_client:
                    order_result = self.direct_client.place_order(**order_params)
                else:
                    order_result = self.session.place_order(**order_params)

                if order_result['retCode'] == 0:
                    logging.info(f"Order placed successfully for {symbol}: {order_result['result']}")
                    return {
                        "success": True,
                        "order_id": order_result['result']['orderId'],
                        "symbol": symbol,
                        "side": side,
                        "amount": amount_usd,
                        "leverage": leverage,
                        "entry": entry_price,
                        "stop_loss": stop_loss,
                        "take_profit": take_profit
                    }
                else:
                    error_msg = f"Failed to place order: {order_result['retMsg']} (Code: {order_result['retCode']})"
                    logging.error(error_msg)
                    return {"success": False, "message": error_msg}
            except Exception as e:
                logging.error(f"Exception placing order: {e}", exc_info=True)
                return {"success": False, "message": f"Error placing order: {str(e)}"}

        except Exception as e:
            logging.error(f"Error in place_order for {symbol}: {e}", exc_info=True)
            return {"success": False, "message": f"Error: {str(e)}"}

    def _place_order_direct(self, symbol: str, side: str, entry_price: float, stop_loss: float, take_profit: float,
                         leverage: int = 10, amount_usd: float = None) -> Dict[str, Any]:
        """
        Place an order with Bybit using our direct client with proper timestamp synchronization
        """
        try:
            # Skip setting leverage - it's persistent on Bybit and often causes errors
            # Just log that we're using the specified leverage
            logging.info(f"Using leverage {leverage}x for {symbol} (persistent setting on Bybit)")

            # Use position size from settings if amount_usd is not provided
            if amount_usd is None:
                amount_usd = getattr(self, 'position_size', 100.0)  # Default to 100 if not set
                logging.info(f"Using position size from settings: ${amount_usd}")

            # Calculate quantity based on amount
            # We want to use the full amount specified (e.g., $100 for a $100 position)
            actual_amount = amount_usd

            # Get current price if needed
            if entry_price <= 0:
                try:
                    ticker_data = self.direct_client.get_tickers(category="linear", symbol=symbol)
                    if ticker_data['retCode'] != 0:
                        return {"success": False, "message": f"Failed to get ticker: {ticker_data['retMsg']}"}
                    entry_price = float(ticker_data['result']['list'][0]['lastPrice'])
                    logging.info(f"Got current price for {symbol}: {entry_price}")
                except Exception as e:
                    logging.error(f"Error getting ticker: {e}")
                    return {"success": False, "message": f"Error getting current price: {str(e)}"}

            # Calculate quantity in base currency
            qty = actual_amount / entry_price

            # Get the lot size filter from the instrument info to format quantity correctly
            try:
                instrument_data = self.direct_client.get_instruments_info(category="linear", symbol=symbol)

                if instrument_data['retCode'] == 0 and len(instrument_data['result']['list']) > 0:
                    contract_info = instrument_data['result']['list'][0]

                    # Get the lot size filter
                    if 'lotSizeFilter' in contract_info:
                        min_qty = float(contract_info['lotSizeFilter'].get('minOrderQty', '0.0001'))
                        qty_step = float(contract_info['lotSizeFilter'].get('qtyStep', '0.0001'))

                        # Round the quantity to the nearest step size
                        qty = round(qty / qty_step) * qty_step

                        # Ensure the quantity is at least the minimum
                        qty = max(qty, min_qty)

                        # Format with enough decimal places to handle the step size
                        decimal_places = len(str(qty_step).split('.')[-1])
                        qty_str = f"{qty:.{decimal_places}f}"

                        logging.info(f"Formatted quantity: {qty_str} (min: {min_qty}, step: {qty_step})")
                    else:
                        # Fallback to default formatting
                        qty_str = f"{qty:.4f}"
                else:
                    # Fallback to default formatting
                    qty_str = f"{qty:.4f}"
            except Exception as e:
                logging.warning(f"Error formatting quantity: {e}. Using default formatting.")
                # Fallback to default formatting
                qty_str = f"{qty:.4f}"

            logging.info(f"Placing {side} order for {symbol}: {qty_str} @ market price with TP: {take_profit}, SL: {stop_loss}")

            # Verify that the symbol exists in the linear category
            try:
                instrument_data = self.direct_client.get_instruments_info(category="linear", symbol=symbol)

                if instrument_data['retCode'] == 0 and len(instrument_data['result']['list']) > 0:
                    # Symbol exists in linear category
                    contract_info = instrument_data['result']['list'][0]
                    logging.info(f"Symbol {symbol} found in linear futures category")

                    # Log some useful information about the contract
                    if 'lotSizeFilter' in contract_info:
                        min_qty = contract_info['lotSizeFilter'].get('minOrderQty', 'N/A')
                        logging.info(f"Minimum order quantity for {symbol}: {min_qty}")

                    if 'priceFilter' in contract_info:
                        tick_size = contract_info['priceFilter'].get('tickSize', 'N/A')
                        logging.info(f"Price tick size for {symbol}: {tick_size}")
                else:
                    logging.warning(f"Symbol {symbol} not found in linear futures category. Order may fail.")
            except Exception as e:
                logging.warning(f"Error checking symbol {symbol} in linear futures category: {e}")

            # Log the category being used
            logging.info(f"Using category 'linear' for {symbol} (USDT perpetual futures)")

            # Prepare order parameters for futures trading
            order_params = {
                "category": "linear",  # Always linear for USDT perpetual futures
                "symbol": symbol,
                "side": side,
                "orderType": "Market",  # Using market order for simplicity
                "qty": qty_str,
                "takeProfit": str(take_profit),
                "stopLoss": str(stop_loss),
                "timeInForce": "GTC",
                "reduceOnly": False  # Not reducing position, creating a new one
            }

            # Don't include positionIdx parameter at all - let the API determine it
            # This is more reliable than trying to detect the position mode
            logging.info(f"Using default position mode for {symbol} (letting API determine positionIdx)")

            logging.info(f"Order parameters: {order_params}")

            # Place the order using our direct client with proper timestamp synchronization
            order_result = self.direct_client.place_order(**order_params)

            if order_result['retCode'] == 0:
                logging.info(f"Order placed successfully for {symbol}: {order_result['result']}")

                # Store trade data for later use when closing the position
                current_time = datetime.datetime.now()

                # Get more detailed market data for the symbol
                market_data = {}
                try:
                    if self.direct_client:
                        ticker_info = self.direct_client.get_tickers(category="linear", symbol=symbol)
                        if ticker_info.get('retCode') == 0 and ticker_info.get('result', {}).get('list'):
                            ticker = ticker_info.get('result', {}).get('list', [{}])[0]
                            market_data = {
                                "last_price": ticker.get('lastPrice', str(entry_price)),
                                "mark_price": ticker.get('markPrice', str(entry_price)),
                                "index_price": ticker.get('indexPrice', str(entry_price)),
                                "high_price_24h": ticker.get('highPrice24h', '0'),
                                "low_price_24h": ticker.get('lowPrice24h', '0'),
                                "volume_24h": ticker.get('volume24h', '0'),
                                "turnover_24h": ticker.get('turnover24h', '0'),
                                "price_change_percent_24h": ticker.get('price24hPcnt', '0')
                            }
                except Exception as e:
                    logging.error(f"Error getting market data for {symbol}: {e}")

                # Calculate risk metrics
                risk_amount = float(amount_usd) / float(leverage)  # Actual capital at risk
                risk_percent = (risk_amount / 100) * 100  # Assuming $100 is the account size for now

                self.trade_data[symbol] = {
                    # Basic trade info
                    "entry_price": str(entry_price),
                    "size": str(qty),
                    "side": side,
                    "leverage": str(leverage),
                    "amount_usd": str(amount_usd),
                    "risk_amount": str(risk_amount),
                    "risk_percent": str(risk_percent),

                    # Take profit and stop loss
                    "take_profit": str(take_profit),
                    "stop_loss": str(stop_loss),
                    "tp_percent": str(round((float(take_profit) - float(entry_price)) / float(entry_price) * 100 * float(leverage), 2)) if side == "Buy" else str(round((float(entry_price) - float(take_profit)) / float(entry_price) * 100 * float(leverage), 2)),
                    "sl_percent": str(round((float(entry_price) - float(stop_loss)) / float(entry_price) * 100 * float(leverage), 2)) if side == "Buy" else str(round((float(stop_loss) - float(entry_price)) / float(entry_price) * 100 * float(leverage), 2)),

                    # Timing info
                    "created_time": current_time.timestamp(),
                    "created_time_str": current_time.strftime("%Y-%m-%d %H:%M:%S"),
                    "day_of_week": current_time.strftime("%A"),
                    "hour_of_day": current_time.strftime("%H"),

                    # Order info
                    "order_id": order_result['result']['orderId'],
                    "order_link_id": order_result['result'].get('orderLinkId', ''),

                    # Market data at entry
                    "market_data": market_data
                }

                # Also save to a file for persistence between app sessions
                try:
                    trades_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "open_trades.json")

                    # Load existing data if file exists
                    existing_data = {}
                    if os.path.exists(trades_file):
                        try:
                            with open(trades_file, "r") as f:
                                existing_data = json.load(f)
                        except Exception as e:
                            logging.error(f"Error loading open trades file: {e}")

                    # Update with new trade
                    existing_data[symbol] = self.trade_data[symbol]

                    # Save back to file
                    with open(trades_file, "w") as f:
                        json.dump(existing_data, f, indent=2, default=str)

                    logging.info(f"Saved trade data to file for {symbol}")
                except Exception as e:
                    logging.error(f"Error saving trade data to file: {e}")

                logging.info(f"Stored trade data for {symbol}: {self.trade_data[symbol]}")

                return {
                    "success": True,
                    "order_id": order_result['result']['orderId'],
                    "symbol": symbol,
                    "side": side,
                    "amount": amount_usd,
                    "leverage": leverage,
                    "entry": entry_price,
                    "stop_loss": stop_loss,
                    "take_profit": take_profit
                }
            else:
                error_msg = f"Failed to place order: {order_result['retMsg']} (Code: {order_result['retCode']})"
                logging.error(error_msg)
                return {"success": False, "message": error_msg}
        except Exception as e:
            logging.error(f"Error in _place_order_direct for {symbol}: {e}", exc_info=True)
            return {"success": False, "message": f"Error: {str(e)}"}

    def __init__(self):
        # --- REPLACE CREDENTIALS ---
        self.api_key = "aMKaaFNd57yeENDYF1"
        self.api_secret = "bKzraCkh1tqEXQBuX0wVobuwz3cyhQEBxnrQ"
        # --- END CREDENTIALS ---

        # Set position mode (one_way or hedge)
        self.position_mode = "one_way"  # Default to one-way mode

        # Store trade data for tracking entry prices and durations
        self.trade_data = {}

        # Load open trades from file if it exists
        try:
            trades_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "open_trades.json")
            if os.path.exists(trades_file):
                with open(trades_file, "r") as f:
                    self.trade_data = json.load(f)
                logging.info(f"Loaded {len(self.trade_data)} open trades from file")
        except Exception as e:
            logging.error(f"Error loading open trades file: {e}")

        # Cache for API responses
        self.cache = {}
        self.cache_timestamps = {}
        self.cache_ttl = 5  # Cache TTL in seconds

        # Add a cache class for compatibility with fetch_data
        class Cache:
            def __init__(self, parent):
                self.parent = parent

            def get(self, key):
                current_time = time.time()
                if key in self.parent.cache and (current_time - self.parent.cache_timestamps.get(key, 0)) < self.parent.cache_ttl:
                    return self.parent.cache[key]
                return None

            def set(self, key, value):
                self.parent.cache[key] = value
                self.parent.cache_timestamps[key] = time.time()

        self.cache_obj = Cache(self)

        # Initialize API clients
        self.session = None
        self.direct_client = None
        self.time_offset = 0

        # Initialize technical analysis parameters
        self.rsi_length = 14
        self.macd_fast = 12
        self.macd_slow = 26
        self.macd_signal = 9
        self.atr_length = 14
        self.tci_length = 20  # Updated to match bot_logic.py
        self.tci_smooth_length = 5  # Added from bot_logic.py
        self.wt_channel_length = 10
        self.wt_average_length = 21
        self.wt_over_bought_1 = 60
        self.wt_over_sold_1 = -60
        self.wt_over_bought_2 = 53
        self.wt_over_sold_2 = -53
        self.stoch_length = 14
        self.stoch_smooth_k = 3  # Renamed from stoch_k
        self.stoch_smooth_d = 3  # Renamed from stoch_d
        self.mfi_length = 14  # Added from bot_logic.py
        self.supertrend_atr_length = 10  # Added from bot_logic.py
        self.supertrend_multiplier = 3.0  # Added from bot_logic.py
        self.overbought_level = 60
        self.oversold_level = -60

        # Define higher timeframes mapping
        self.higher_timeframes = {
            "1m": "5m",
            "5m": "15m",
            "15m": "1h",
            "30m": "4h",
            "1h": "4h",
            "4h": "1d",
            "1d": "1w"
        }

        # Define ATR multipliers for different timeframes
        self.atr_multipliers = {
            '5m': {'sl': 1.8, 'tp': 3.0},
            '15m': {'sl': 2.0, 'tp': 3.5},
            '1h': {'sl': 2.2, 'tp': 4.0},
            '4h': {'sl': 2.5, 'tp': 4.5},
            '1d': {'sl': 3.0, 'tp': 5.0},
            '1w': {'sl': 3.5, 'tp': 6.0}
        }

        # Initialize Bybit API clients
        self.initialize_api_clients()

    def get_klines(self, symbol, interval, limit=100, category="linear"):
        """
        Get klines (candlestick data) for a symbol
        """
        try:
            # Try to use direct client first
            if self.direct_client:
                try:
                    # Get klines using direct client
                    klines_response = self.direct_client.get_kline(
                        category=category,
                        symbol=symbol,
                        interval=interval,
                        limit=limit
                    )
                    if klines_response.get('retCode') == 0:
                        return klines_response
                    else:
                        logging.error(f"Error getting klines from direct client: {klines_response.get('retMsg')}")
                except Exception as e:
                    logging.error(f"Error using direct client for klines: {e}")

            # Fall back to pybit client
            if self.session:
                try:
                    # Try to use the pybit method
                    klines_response = self.session.get_kline(
                        category=category,
                        symbol=symbol,
                        interval=interval,
                        limit=limit
                    )
                    if klines_response.get('retCode') == 0:
                        return klines_response
                    else:
                        logging.error(f"Error getting klines from pybit: {klines_response.get('retMsg')}")
                except Exception as e:
                    logging.error(f"Error using pybit client for klines: {e}")

            # If all else fails, return an empty response
            return {"retCode": -1, "retMsg": "Failed to get klines", "result": {"list": []}}
        except Exception as e:
            logging.error(f"Error getting klines: {e}", exc_info=True)
            return {"retCode": -1, "retMsg": f"Exception: {str(e)}", "result": {"list": []}}

    def timeframe_to_interval(self, timeframe):
        """
        Convert a timeframe string to a Bybit interval string
        """
        # Map timeframes to Bybit intervals
        timeframe_map = {
            "1m": 1,
            "3m": 3,
            "5m": 5,
            "15m": 15,
            "30m": 30,
            "1h": 60,
            "2h": 120,
            "4h": 240,
            "6h": 360,
            "12h": 720,
            "1d": "D",
            "1w": "W",
            "1M": "M"
        }

        return timeframe_map.get(timeframe, 60)  # Default to 1h (60) if not found

    def timeframe_to_oi_interval(self, timeframe):
        """
        Convert a timeframe string to a Bybit open interest interval string
        """
        # Map timeframes to Bybit open interest intervals
        timeframe_map = {
            "1m": "5min",
            "3m": "5min",
            "5m": "5min",
            "15m": "15min",
            "30m": "30min",
            "1h": "1h",
            "2h": "4h",
            "4h": "4h",
            "6h": "4h",
            "12h": "4h",
            "1d": "1d",
            "1w": "1w",
            "1M": "1M"
        }

        interval = timeframe_map.get(timeframe, "1h")  # Default to 1h if not found
        logging.debug(f"Converted timeframe {timeframe} to OI interval {interval}")
        return interval

    def initialize_api_clients(self):
        """
        Initialize the Bybit API clients
        """
        try:
            # First try to initialize our direct Bybit client
            try:
                from bybit_direct import BybitDirect
                logging.info("Initializing direct Bybit client...")
                self.direct_client = BybitDirect(self.api_key, self.api_secret)
                self.time_offset = self.direct_client.sync_time()
                logging.info(f"Direct Bybit client initialized successfully with time offset: {self.time_offset} ms")
            except Exception as e:
                logging.warning(f"Could not initialize direct Bybit client: {e}. Falling back to pybit.")
                self.direct_client = None

            # Also initialize the standard pybit client as fallback
            from pybit.unified_trading import HTTP
            logging.info("Initializing pybit client as fallback...")

            # Initialize with a very large recv_window to handle time differences
            self.session = HTTP(
                api_key=self.api_key,
                api_secret=self.api_secret,
                recv_window=120000  # 120 seconds
            )

            # Monkey patch the _auth method to use our time offset
            original_auth = self.session._auth

            def patched_auth(payload):
                return original_auth(payload)

            self.session._auth = patched_auth

            # Test the connection
            server_time = self.session.get_server_time()
            logging.info(f"Pybit client initialized successfully. Server time: {server_time}")

        except Exception as e:
            logging.error(f"Failed to initialize Bybit API clients: {e}")
            self.session = None
            self.direct_client = None

    def get_positions(self):
        """
        Get all open positions from Bybit with caching to reduce API calls
        """
        try:
            # Check if we have a cached response that's still valid
            cache_key = "positions"
            current_time = time.time()

            if cache_key in self.cache and (current_time - self.cache_timestamps.get(cache_key, 0)) < self.cache_ttl:
                logging.debug(f"Using cached positions data (age: {current_time - self.cache_timestamps.get(cache_key, 0):.1f}s)")
                return self.cache[cache_key]

            # No valid cache, make the API call
            logging.debug("Fetching fresh positions data from API")
            positions = []

            # Try to use direct client first
            if self.direct_client:
                try:
                    # Get positions using direct client
                    positions_response = self.direct_client.get_positions(category="linear", settle_coin="USDT")
                    if positions_response.get('retCode') == 0:
                        positions = positions_response.get('result', {}).get('list', [])
                    else:
                        logging.error(f"Error getting positions from direct client: {positions_response.get('retMsg')}")
                except Exception as e:
                    logging.error(f"Error using direct client: {e}")

            # Fall back to pybit client if needed
            if not positions and self.session:
                try:
                    # Try to use the monkey-patched method
                    positions_response = self.session.get_positions(category="linear")
                    if positions_response.get('retCode') == 0:
                        positions = positions_response.get('result', {}).get('list', [])
                    else:
                        logging.error(f"Error getting positions from pybit: {positions_response.get('retMsg')}")
                except Exception as e:
                    logging.error(f"Error using pybit client: {e}")

            # Cache the result
            self.cache[cache_key] = positions
            self.cache_timestamps[cache_key] = current_time

            return positions
        except Exception as e:
            logging.error(f"Error getting positions: {e}", exc_info=True)
            return []

    def get_open_orders(self, symbol=None, limit=50):
        """
        Get open and recent closed orders from Bybit with caching to reduce API calls

        Args:
            symbol: Symbol name (optional)
            limit: Limit for data size per page. [1, 50]. Default: 50

        Returns:
            List of orders
        """
        try:
            # Create a cache key based on the parameters
            cache_key = f"orders_{symbol}_{limit}"
            current_time = time.time()

            # Check if we have a cached response that's still valid
            if cache_key in self.cache and (current_time - self.cache_timestamps.get(cache_key, 0)) < self.cache_ttl:
                logging.debug(f"Using cached orders data for {symbol} (age: {current_time - self.cache_timestamps.get(cache_key, 0):.1f}s)")
                return self.cache[cache_key]

            # No valid cache, make the API call
            logging.debug(f"Fetching fresh orders data for {symbol} from API")
            orders = []

            # Try to use direct client first
            if self.direct_client:
                try:
                    # Get orders using direct client
                    orders_response = self.direct_client.get_open_orders(category="linear", symbol=symbol, limit=limit)
                    if orders_response.get('retCode') == 0:
                        orders = orders_response.get('result', {}).get('list', [])
                    else:
                        logging.error(f"Error getting orders from direct client: {orders_response.get('retMsg')}")
                except Exception as e:
                    logging.error(f"Error using direct client for orders: {e}")

            # Fall back to pybit client if needed
            if not orders and self.session:
                try:
                    # Try to use the pybit method
                    orders_response = self.session.get_open_orders(category="linear", symbol=symbol, limit=limit)
                    if orders_response.get('retCode') == 0:
                        orders = orders_response.get('result', {}).get('list', [])
                    else:
                        logging.error(f"Error getting orders from pybit: {orders_response.get('retMsg')}")
                except Exception as e:
                    logging.error(f"Error using pybit client for orders: {e}")

            # Cache the result
            self.cache[cache_key] = orders
            self.cache_timestamps[cache_key] = current_time

            return orders
        except Exception as e:
            logging.error(f"Error getting open orders: {e}", exc_info=True)
            return []

    def modify_tp_sl(self, symbol, take_profit=None, stop_loss=None, position_idx=0):
        """
        Modify take profit and stop loss for an existing position
        """
        try:
            # Get position details first to verify it exists
            positions = self.get_positions()
            position = None

            for pos in positions:
                if pos.get('symbol') == symbol and int(pos.get('positionIdx', 0)) == position_idx:
                    position = pos
                    break

            if not position:
                return {"success": False, "message": f"No position found for {symbol}"}

            # Check if position size is zero or position is not open
            size = float(position.get('size', 0))
            if size <= 0:
                return {"success": False, "message": f"Position size is zero for {symbol}"}

            # Check if position is in the correct state
            if position.get('side') == "None" or position.get('positionStatus') != "Normal":
                return {"success": False, "message": f"Position is not in a valid state for {symbol}"}

            # Try to use direct client first
            if self.direct_client:
                params = {
                    "category": "linear",
                    "symbol": symbol,
                    "positionIdx": position_idx
                }

                if take_profit is not None:
                    params["takeProfit"] = str(take_profit)

                if stop_loss is not None:
                    params["stopLoss"] = str(stop_loss)

                response = self.direct_client.set_trading_stop(**params)
                if response.get('retCode') == 0:
                    logging.info(f"Successfully modified TP/SL for {symbol}")
                    return {"success": True, "message": f"Successfully modified TP/SL for {symbol}"}
                else:
                    error_msg = f"Error modifying TP/SL: {response.get('retMsg')}"
                    logging.error(error_msg)
                    return {"success": False, "message": error_msg}

            # Fall back to pybit client
            if self.session:
                params = {
                    "category": "linear",
                    "symbol": symbol,
                    "positionIdx": position_idx
                }

                if take_profit is not None:
                    params["takeProfit"] = str(take_profit)

                if stop_loss is not None:
                    params["stopLoss"] = str(stop_loss)

                response = self.session.set_trading_stop(**params)
                if response.get('retCode') == 0:
                    logging.info(f"Successfully modified TP/SL for {symbol}")
                    return {"success": True, "message": f"Successfully modified TP/SL for {symbol}"}
                else:
                    error_msg = f"Error modifying TP/SL: {response.get('retMsg')}"
                    logging.error(error_msg)
                    return {"success": False, "message": error_msg}

            return {"success": False, "message": "No API client available"}
        except Exception as e:
            logging.error(f"Error modifying TP/SL: {e}", exc_info=True)
            return {"success": False, "message": f"Error: {str(e)}"}

    def close_position(self, symbol, position_idx=0):
        """
        Close an existing position
        """
        try:
            # Get position details first to determine size and side
            positions = self.get_positions()
            position = None

            for pos in positions:
                if pos.get('symbol') == symbol and int(pos.get('positionIdx', 0)) == position_idx:
                    position = pos
                    break

            if not position:
                return {"success": False, "message": f"No position found for {symbol}"}

            # Check if position size is zero or position is not open
            size = float(position.get('size', 0))
            if size <= 0:
                return {"success": False, "message": f"Position size is zero for {symbol}"}

            # Check if position is in the correct state
            if position.get('side') == "None" or position.get('positionStatus') != "Normal":
                return {"success": False, "message": f"Position is not in a valid state for {symbol}"}

            # Determine the opposite side for closing
            side = "Sell" if position.get('side') == "Buy" else "Buy"
            qty = position.get('size')

            # Use a non-reduce-only order to close the position
            order_params = {
                "category": "linear",
                "symbol": symbol,
                "side": side,
                "orderType": "Market",
                "qty": qty,
                "reduceOnly": True,  # Set to True to ensure it only closes the position
                "timeInForce": "GTC",
                "position_mode": self.position_mode  # Pass the position mode
            }

            # Set positionIdx based on position mode
            if self.position_mode == "hedge":
                order_params["positionIdx"] = position_idx
            else:
                order_params["positionIdx"] = 0  # One-way mode

            # Try to use direct client first
            if self.direct_client:
                response = self.direct_client.place_order(**order_params)
                if response.get('retCode') == 0:
                    logging.info(f"Successfully closed position for {symbol}")

                    # Get position data for trade history
                    # Use stored trade data if available, otherwise use position data
                    try:
                        # Get current time for exit timestamp
                        exit_time = datetime.datetime.now()

                        # Get more detailed market data for the symbol at exit
                        exit_market_data = {}
                        try:
                            if self.direct_client:
                                ticker_info = self.direct_client.get_tickers(category="linear", symbol=symbol)
                                if ticker_info.get('retCode') == 0 and ticker_info.get('result', {}).get('list'):
                                    ticker = ticker_info.get('result', {}).get('list', [{}])[0]
                                    exit_market_data = {
                                        "last_price": ticker.get('lastPrice', '0.0'),
                                        "mark_price": ticker.get('markPrice', '0.0'),
                                        "index_price": ticker.get('indexPrice', '0.0'),
                                        "high_price_24h": ticker.get('highPrice24h', '0'),
                                        "low_price_24h": ticker.get('lowPrice24h', '0'),
                                        "volume_24h": ticker.get('volume24h', '0'),
                                        "turnover_24h": ticker.get('turnover24h', '0'),
                                        "price_change_percent_24h": ticker.get('price24hPcnt', '0')
                                    }
                        except Exception as e:
                            logging.error(f"Error getting market data for {symbol} at exit: {e}")

                        # Get current mark price for exit price
                        exit_price = position.get('markPrice', '0.0')
                        if exit_price is None or exit_price == '':
                            # Try to get from exit market data
                            exit_price = exit_market_data.get('mark_price', '0.0')
                            if exit_price is None or exit_price == '':
                                exit_price = '0.0'
                        else:
                            # Make sure it's a string
                            exit_price = str(exit_price)

                        # Get PnL
                        pnl = position.get('unrealisedPnl', '0.0')
                        if pnl is None or pnl == '':
                            pnl = '0.0'
                        else:
                            # Make sure it's a string
                            pnl = str(pnl)

                        # Check if we have stored trade data for this symbol
                        if symbol in self.trade_data:
                            stored_data = self.trade_data[symbol]
                            logging.info(f"Using stored trade data for {symbol}: {stored_data}")

                            # Calculate duration
                            created_time = stored_data.get('created_time')
                            if created_time is None:
                                created_time = datetime.datetime.now().timestamp() - 3600  # Default to 1 hour ago

                            # Calculate PnL percentage
                            pnl_percent = 0.0
                            try:
                                entry_price_float = float(stored_data.get('entry_price', 0))
                                exit_price_float = float(exit_price)
                                side = stored_data.get('side')
                                leverage = float(stored_data.get('leverage', 10))

                                if entry_price_float > 0:
                                    if side == "Buy":
                                        pnl_percent = (exit_price_float - entry_price_float) / entry_price_float * 100 * leverage
                                    else:  # Sell
                                        pnl_percent = (entry_price_float - exit_price_float) / entry_price_float * 100 * leverage
                            except Exception as e:
                                logging.error(f"Error calculating PnL percentage: {e}")

                            # Calculate duration
                            duration_str = "N/A"
                            duration_seconds = 0
                            try:
                                exit_timestamp = exit_time.timestamp()
                                duration_seconds = exit_timestamp - created_time

                                # Format duration
                                days, remainder = divmod(duration_seconds, 86400)
                                hours, remainder = divmod(remainder, 3600)
                                minutes, seconds = divmod(remainder, 60)

                                if days > 0:
                                    duration_str = f"{int(days)}d {int(hours)}h {int(minutes)}m"
                                elif hours > 0:
                                    duration_str = f"{int(hours)}h {int(minutes)}m"
                                else:
                                    duration_str = f"{int(minutes)}m {int(seconds)}s"
                            except Exception as e:
                                logging.error(f"Error calculating duration: {e}")

                            position_data = {
                                # Basic trade info
                                "symbol": symbol,
                                "side": stored_data.get('side'),
                                "entry_price": stored_data.get('entry_price', '0.0'),
                                "exit_price": exit_price,
                                "size": stored_data.get('size', '0.0'),
                                "amount_usd": stored_data.get('amount_usd', '0.0'),
                                "leverage": stored_data.get('leverage', '10'),

                                # PnL info
                                "pnl": pnl,
                                "pnl_percent": str(round(pnl_percent, 2)),

                                # Take profit and stop loss
                                "take_profit": stored_data.get('take_profit', '0.0'),
                                "stop_loss": stored_data.get('stop_loss', '0.0'),
                                "tp_percent": stored_data.get('tp_percent', '0.0'),
                                "sl_percent": stored_data.get('sl_percent', '0.0'),

                                # Timing info
                                "created_time": created_time,
                                "created_time_str": stored_data.get('created_time_str', 'Unknown'),
                                "exit_time": exit_time.timestamp(),
                                "exit_time_str": exit_time.strftime("%Y-%m-%d %H:%M:%S"),
                                "duration": duration_str,
                                "duration_seconds": str(duration_seconds),
                                "day_of_week": stored_data.get('day_of_week', exit_time.strftime("%A")),
                                "hour_of_day": stored_data.get('hour_of_day', exit_time.strftime("%H")),

                                # Market data
                                "entry_market_data": stored_data.get('market_data', {}),
                                "exit_market_data": exit_market_data,

                                # Order info
                                "order_id": stored_data.get('order_id', ''),
                                "exit_type": "Manual Close"
                            }
                        else:
                            # Fallback to position data
                            logging.warning(f"No stored trade data for {symbol}, using position data")

                            entry_price = position.get('entryPrice', '0.0')
                            if entry_price is None or entry_price == '':
                                entry_price = '0.0'
                            else:
                                entry_price = str(entry_price)

                            size = position.get('size', '0.0')
                            if size is None or size == '':
                                size = '0.0'
                            else:
                                size = str(size)

                            # Default to 1 hour ago for created_time
                            created_time = datetime.datetime.now().timestamp() - 3600

                            position_data = {
                                "symbol": symbol,
                                "side": position.get('side'),
                                "entry_price": entry_price,
                                "exit_price": exit_price,
                                "size": size,
                                "pnl": pnl,
                                "leverage": position.get('leverage', '10'),
                                "created_time": created_time
                            }

                        # Remove the trade data after using it
                        if symbol in self.trade_data:
                            del self.trade_data[symbol]
                            logging.info(f"Removed trade data for {symbol} after closing position")

                            # Also remove from the file
                            try:
                                trades_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "open_trades.json")
                                if os.path.exists(trades_file):
                                    # Load existing data
                                    existing_data = {}
                                    try:
                                        with open(trades_file, "r") as f:
                                            existing_data = json.load(f)
                                    except Exception as e:
                                        logging.error(f"Error loading open trades file: {e}")

                                    # Remove the symbol
                                    if symbol in existing_data:
                                        del existing_data[symbol]

                                    # Save back to file
                                    with open(trades_file, "w") as f:
                                        json.dump(existing_data, f, indent=2, default=str)

                                    logging.info(f"Removed trade data from file for {symbol}")
                            except Exception as e:
                                logging.error(f"Error updating open trades file: {e}")
                    except Exception as e:
                        logging.error(f"Error preparing position data: {e}")
                        position_data = {
                            "symbol": symbol,
                            "side": "Unknown",
                            "entry_price": "0.0",
                            "exit_price": "0.0",
                            "size": "0.0",
                            "pnl": "0.0",
                            "leverage": "10",
                            "created_time": datetime.datetime.now().timestamp() - 3600
                        }

                    # Log the position data for debugging
                    logging.info(f"Position data: {position_data}")

                    return {
                        "success": True,
                        "message": f"Successfully closed position for {symbol}",
                        "position_data": position_data
                    }
                else:
                    error_msg = f"Error closing position: {response.get('retMsg')}"
                    logging.error(error_msg)
                    return {"success": False, "message": error_msg}

            # Fall back to pybit client
            if self.session:
                response = self.session.place_order(**order_params)
                if response.get('retCode') == 0:
                    logging.info(f"Successfully closed position for {symbol}")

                    # Use stored trade data if available, otherwise use position data
                    try:
                        # Get current mark price for exit price
                        exit_price = position.get('markPrice', '0.0')
                        if exit_price is None or exit_price == '':
                            exit_price = '0.0'
                        else:
                            # Make sure it's a string
                            exit_price = str(exit_price)

                        # Get PnL
                        pnl = position.get('unrealisedPnl', '0.0')
                        if pnl is None or pnl == '':
                            pnl = '0.0'
                        else:
                            # Make sure it's a string
                            pnl = str(pnl)

                        # Check if we have stored trade data for this symbol
                        if symbol in self.trade_data:
                            stored_data = self.trade_data[symbol]
                            logging.info(f"Using stored trade data for {symbol}: {stored_data}")

                            # Calculate duration
                            created_time = stored_data.get('created_time')
                            if created_time is None:
                                created_time = datetime.datetime.now().timestamp() - 3600  # Default to 1 hour ago

                            position_data = {
                                "symbol": symbol,
                                "side": stored_data.get('side'),
                                "entry_price": stored_data.get('entry_price', '0.0'),
                                "exit_price": exit_price,
                                "size": stored_data.get('size', '0.0'),
                                "pnl": pnl,
                                "leverage": stored_data.get('leverage', '10'),
                                "created_time": created_time
                            }
                        else:
                            # Fallback to position data
                            logging.warning(f"No stored trade data for {symbol}, using position data")

                            entry_price = position.get('entryPrice', '0.0')
                            if entry_price is None or entry_price == '':
                                entry_price = '0.0'
                            else:
                                entry_price = str(entry_price)

                            size = position.get('size', '0.0')
                            if size is None or size == '':
                                size = '0.0'
                            else:
                                size = str(size)

                            # Default to 1 hour ago for created_time
                            created_time = datetime.datetime.now().timestamp() - 3600

                            position_data = {
                                "symbol": symbol,
                                "side": position.get('side'),
                                "entry_price": entry_price,
                                "exit_price": exit_price,
                                "size": size,
                                "pnl": pnl,
                                "leverage": position.get('leverage', '10'),
                                "created_time": created_time
                            }

                        # Remove the trade data after using it
                        if symbol in self.trade_data:
                            del self.trade_data[symbol]
                            logging.info(f"Removed trade data for {symbol} after closing position")
                    except Exception as e:
                        logging.error(f"Error preparing position data: {e}")
                        position_data = {
                            "symbol": symbol,
                            "side": "Unknown",
                            "entry_price": "0.0",
                            "exit_price": "0.0",
                            "size": "0.0",
                            "pnl": "0.0",
                            "leverage": "10",
                            "created_time": datetime.datetime.now().timestamp() - 3600
                        }

                    # Log the position data for debugging
                    logging.info(f"Position data: {position_data}")

                    return {
                        "success": True,
                        "message": f"Successfully closed position for {symbol}",
                        "position_data": position_data
                    }
                else:
                    error_msg = f"Error closing position: {response.get('retMsg')}"
                    logging.error(error_msg)
                    return {"success": False, "message": error_msg}

            return {"success": False, "message": "No API client available"}
        except Exception as e:
            logging.error(f"Error closing position: {e}", exc_info=True)
            return {"success": False, "message": f"Error: {str(e)}"}

# --- END OF AdvancedTradingBot CLASS ---
# --- GUI with Dear PyGui ---
class AdvancedTradingGUI:
    def __init__(self, bot: AdvancedTradingBot):
        self.bot = bot
        logging.info("AdvancedTradingGUI init started.")
        self.gui_queue = queue.Queue() # Queue for thread-safe GUI updates
        self.custom_symbols: List[str] = []
        self.live_running = False
        self.all_live_results: Dict[str, Dict[str, Any]] = {}
        self.live_scan = False
        self.live_scan_thread = None
        self.analysis_thread = None
        self.positions_thread = None
        self.auto_refresh_positions = False
        self.positions_data = []
        self.currently_displayed_symbols = []
        self.stop_event = threading.Event()

        # Trade history and performance tracking
        self.trade_history = []
        self.history_timeframe = "All Time"
        self.filtered_history = []
        self.equity_curve = [0]  # Start with initial balance

        # Theme management
        self.current_theme = "dark"  # Default theme
        self.themes = {
            "dark": {
                'background': (32, 32, 32),
                'panel': (45, 45, 48),
                'foreground': (255, 255, 255),
                'accent': (0, 120, 215),
                'success': (0, 200, 0),
                'warning': (255, 165, 0),
                'error': (255, 0, 0),
                'muted': (150, 150, 150),
                'highlight': (70, 70, 80),
                'border': (60, 60, 70)
            },
            "light": {
                'background': (240, 240, 240),
                'panel': (225, 225, 225),
                'foreground': (20, 20, 20),
                'accent': (0, 120, 215),
                'success': (0, 150, 0),
                'warning': (200, 120, 0),
                'error': (200, 0, 0),
                'muted': (100, 100, 100),
                'highlight': (210, 210, 220),
                'border': (180, 180, 190)
            }
        }
        self.theme = self.themes[self.current_theme]  # Active theme

        # Load trade history from file if exists
        self.load_trade_history()
        # --- REPLACE CREDENTIALS ---
        self.TELEGRAM_TOKEN = "**********************************************"
        self.TELEGRAM_CHAT_ID = "650827211"
        # --- END CREDENTIALS ---
        self.theme = {
            'background': (30,30,30,255), 'foreground': (232,232,232,255), 'accent': (52,152,219,255),
            'success': (46,204,113,255), 'error': (231,76,60,255), 'warning': (241,196,15,255),
            'tree_header': (45,45,45,255), 'tree_row': (37,37,37,255)
        }
        self.timeframe = '1h'
        self.filter_vars = {
            'BUY': False, 'SELL': False, 'CONF>60': False, 'VOL_SPIKE': False, 'HTF_ALIGN': False,
            'SENT_CONF': False, 'WAVE_X': False, 'DIV_CONF': False, 'ST_ALIGN': False
        }
        self.currently_displayed_symbols: List[str] = []
        self.columns_config = [ # Updated Entry Zone label/width
            ('Symbol', 140), ('Signal', 80), ('Conf.', 70), ('Price', 110), ('Entry Zone', 180),
            ('Stop Loss', 110), ('Take Profit', 110), ('Chg.', 70), ('Trend', 80), ('STrend', 80),
            ('Momentum', 80), ('Volume', 100), ('Div.', 70), ('Market', 100), ('HTF', 90),
            ('Funding', 110), ('OI Trend', 90), ('Sent.', 70), ('Wave', 80), ('TG', 60), ('Buy', 60)
        ]
        self.load_symbols()
        # Log file name updated
        # configure_logging("trading_bot_fibzone_debug.log") # Ensure logging is configured elsewhere
        # Do not call setup_gui here - it will be called from run()

    # --- GUI Setup, Symbol List Mgmt, Analysis Control (No changes from previous full code) ---
    def setup_gui(self):
        logging.info("Setting up GUI...")
        dpg.create_context()
        dpg.create_viewport(title="⚡ Quantum Trading Suite Pro - FibZone Scan v1.4 DEBUG", width=1870, height=700, resizable=True)
        dpg.setup_dearpygui()

        try: # Font loading
            with dpg.font_registry(): default_font = dpg.add_font("C:/Windows/Fonts/Arial.ttf", 14)
            dpg.bind_font(default_font); logging.info("Arial font bound.")
        except Exception as e: logging.error(f"Failed to load font: {e}.")
        with dpg.theme() as global_theme: # Theme setup
            with dpg.theme_component(dpg.mvAll):
                dpg.add_theme_color(dpg.mvThemeCol_WindowBg, self.theme['background']); dpg.add_theme_color(dpg.mvThemeCol_ChildBg, self.theme['tree_row'])
                dpg.add_theme_color(dpg.mvThemeCol_FrameBg, (45,45,45,255)); dpg.add_theme_color(dpg.mvThemeCol_Button, self.theme['accent'])
                dpg.add_theme_color(dpg.mvThemeCol_Header, self.theme['tree_header']); dpg.add_theme_color(dpg.mvThemeCol_HeaderHovered, self.theme['accent'])
                dpg.add_theme_color(dpg.mvThemeCol_HeaderActive, self.theme['accent']); dpg.add_theme_color(dpg.mvThemeCol_Text, self.theme['foreground'])
                dpg.add_theme_style(dpg.mvStyleVar_FramePadding, 6, 4); dpg.add_theme_style(dpg.mvStyleVar_CellPadding, 4, 2); dpg.add_theme_style(dpg.mvStyleVar_ItemSpacing, 8, 6)
        dpg.bind_theme(global_theme)
        with dpg.window(tag="main_window", no_close=True, no_title_bar=True): # Main Window Layout
            # Create tabs for different sections
            with dpg.tab_bar(tag="main_tab_bar"):
                # Signals Tab
                with dpg.tab(label="Signals", tag="signals_tab"):
                    # Settings section
                    with dpg.group(horizontal=True):
                        dpg.add_text("Position Size ($): ")
                        dpg.add_input_float(tag="position_size_input", default_value=100.0, min_value=1.0, max_value=10000.0, width=100, format="%.2f")
                        dpg.add_button(label="Save", callback=self.save_settings, width=60)
                        dpg.add_text("", tag="settings_status_text")

                    dpg.add_spacer(height=5)

                    with dpg.group(horizontal=True): # Top Bar
                        default_sym = ",".join(self.custom_symbols) if self.custom_symbols else "BTCUSDT,ETHUSDT"
                        dpg.add_text("GUI Loaded")
                        dpg.add_input_text(tag="symbol_input", default_value=default_sym, hint="Symbols (comma-separated)", width=400)
                        dpg.add_button(label="Load List", callback=self.add_symbols, width=80); dpg.add_button(label="Clear List", callback=self.clear_symbols, width=80)
                        dpg.add_spacer(width=15); dpg.add_button(label="Refresh", tag="refresh_btn", callback=self.refresh_live_analysis, width=80)
                        dpg.add_combo(['5m', '15m', '1h', '4h', '1d', '1w'], default_value=self.timeframe, callback=self.set_timeframe, width=70)
                        dpg.add_spacer(width=15); dpg.add_button(label="Start Scan", tag="scan_btn", callback=self.toggle_live_analysis, width=100)
                        dpg.add_spacer(width=15); dpg.add_checkbox(label="Auto", tag="auto_refresh_cb", default_value=self.live_scan, callback=self.toggle_auto_refresh)
                        dpg.add_spacer(width=15); dpg.add_input_int(tag="refresh_interval", default_value=60, min_value=10, max_value=3600, width=100, step=10, label=" Sec")
                        dpg.add_spacer(width=30); dpg.add_text("Status: Ready", tag="status_text")
                    dpg.add_spacer(height=10)
                    with dpg.group(horizontal=True): # Filters Bar
                        dpg.add_text("Filters:"); dpg.add_spacer(width=10)
                        keys_labels=[('BUY','BUY'),('SELL','SELL'),('CONF>60','Conf>60'),('VOL_SPIKE','VolSpk'),('HTF_ALIGN','HTF'),('SENT_CONF','Sent'),('WAVE_X','WaveX'),('DIV_CONF','Div'),('ST_ALIGN','ST Align')]
                        for key, label in keys_labels:
                            if key in self.filter_vars: dpg.add_checkbox(label=label, callback=self.apply_live_filters_gui, user_data=key, tag=f'filter_{key}'); dpg.add_spacer(width=15)
                    dpg.add_spacer(height=10)
                    with dpg.table(tag="live_table", header_row=True, resizable=True, policy=dpg.mvTable_SizingStretchProp, # Results Table
                                   row_background=True, borders_outerH=True, borders_innerV=True, borders_innerH=True, borders_outerV=True,
                                   height=-1, width=-1, scrollY=True): pass # Columns added by _clear_table

                # Positions Tab
                with dpg.tab(label="Positions", tag="positions_tab"):
                    with dpg.group(horizontal=True): # Top Bar for Positions
                        dpg.add_button(label="Refresh Positions", tag="refresh_positions_btn", callback=self.refresh_positions, width=150)
                        dpg.add_spacer(width=15)
                        dpg.add_checkbox(label="Auto Refresh", tag="auto_refresh_positions_cb", default_value=False, callback=self.toggle_auto_refresh_positions)
                        dpg.add_spacer(width=15)
                        dpg.add_input_int(tag="positions_refresh_interval", default_value=30, min_value=5, max_value=300, width=100, step=5, label=" Sec")
                        dpg.add_spacer(width=30)
                        dpg.add_text("Positions Status: Ready", tag="positions_status_text")
                    dpg.add_spacer(height=10)
                    with dpg.table(tag="positions_table", header_row=True, resizable=True, policy=dpg.mvTable_SizingStretchProp,
                                   row_background=True, borders_outerH=True, borders_innerV=True, borders_innerH=True, borders_outerV=True,
                                   height=-1, width=-1, scrollY=True):
                        # Add columns for positions table
                        dpg.add_table_column(label="Symbol", width_fixed=True, width=120)
                        dpg.add_table_column(label="Side", width_fixed=True, width=80)
                        dpg.add_table_column(label="Size", width_fixed=True, width=100)
                        dpg.add_table_column(label="Entry Price", width_fixed=True, width=120)
                        dpg.add_table_column(label="Current Price", width_fixed=True, width=120)
                        dpg.add_table_column(label="P&L", width_fixed=True, width=100)
                        dpg.add_table_column(label="P&L %", width_fixed=True, width=100)
                        dpg.add_table_column(label="Take Profit", width_fixed=True, width=120)
                        dpg.add_table_column(label="Stop Loss", width_fixed=True, width=120)
                        dpg.add_table_column(label="Modify TP", width_fixed=True, width=100)
                        dpg.add_table_column(label="Modify SL", width_fixed=True, width=100)
                        dpg.add_table_column(label="Close", width_fixed=True, width=80)


        dpg.set_primary_window("main_window", True); self._clear_table(); dpg.show_viewport()

    def set_timeframe(self, sender, app_data):
        self.timeframe = app_data; logging.info(f"Timeframe changed to: {self.timeframe}")
        self.all_live_results.clear(); self.currently_displayed_symbols.clear()
        self._clear_table();
        if dpg.does_item_exist("status_text"): dpg.set_value("status_text", f"Status: Timeframe set to {self.timeframe}. Press Refresh.")

    def _schedule_apply_filters(self):
            """Helper function run via queue to schedule apply_live_filters on the next frame."""
            if dpg.is_dearpygui_running():
                current_frame = dpg.get_frame_count()
                if current_frame >= 0:
                    logging.debug(f"Scheduling apply_live_filters for frame {current_frame + 1}")
                    dpg.set_frame_callback(current_frame + 1, callback=self.apply_live_filters)
                else:
                     logging.error("Cannot schedule apply_live_filters: Invalid frame count.")
            else:
                 logging.warning("_schedule_apply_filters called but DPG not running.")

    def _clear_table(self):
        if dpg.does_item_exist("live_table"):
            dpg.delete_item("live_table", children_only=True)
            for col_label, width_weight in self.columns_config:
                dpg.add_table_column(label=col_label, width_fixed=False, init_width_or_weight=float(width_weight), parent="live_table")
        else: logging.error("Cannot clear table: 'live_table' does not exist.")

    def apply_live_filters_gui(self, sender, app_data, user_data):
         if user_data in self.filter_vars: self.filter_vars[user_data] = app_data; logging.debug(f"Filter '{user_data}' set to {app_data}"); self.apply_live_filters()

    # --- UPDATED apply_live_filters ---
    def apply_live_filters(self):
        """Filters and redraws the table based on self.all_live_results and self.filter_vars."""
        logging.debug("apply_live_filters CALLED") # Add log to see when it runs
        if not dpg.is_dearpygui_running() or not dpg.does_item_exist("live_table"):
            logging.warning("apply_live_filters called but GUI/Table not ready.")
            return

        self._clear_table() # Clear existing rows and recreate headers/columns
        displayed_symbols_this_run = []

        # Filter out None values before sorting
        valid_results = [res for res in self.all_live_results.values() if isinstance(res, dict)]
        sorted_results = sorted(
            valid_results,
            key=lambda item: (-item.get('confidence', 0), item.get('symbol', ''))
        )
        logging.debug(f"apply_live_filters: Found {len(valid_results)} valid results, {len(sorted_results)} after sorting.") # Log count

        active_filters = {k for k, v in self.filter_vars.items() if v}

        if not sorted_results:
             logging.debug("apply_live_filters: No results to display.")

        for item in sorted_results:
            # This check is slightly redundant due to filtering above, but very safe
            if not isinstance(item, dict):
                 logging.warning(f"Skipping invalid item type in apply_live_filters loop: {type(item)}")
                 continue

            # --- Filter gathering and application logic (as before) ---
            signal=item.get('signal','HOLD'); conf=item.get('confidence',0.0); vol_spike=item.get('volume_spike',False)
            higher_signal=item.get('higher_signal','N/A'); st_align=item.get('st_align',False); sent_confirm=item.get('sentiment_confirm',False)
            wave_cross=item.get('wave_cross',False); div_confirm=item.get('div_confirm',False)
            htf_align=False
            if signal != 'HOLD' and ' ' in higher_signal:
                htf_dir = higher_signal.split()[1]
                if (signal=='BUY' and htf_dir=='Up') or (signal=='SELL' and htf_dir=='Down'): htf_align=True
            passes = all(((not self.filter_vars['BUY'] or signal=='BUY'), (not self.filter_vars['SELL'] or signal=='SELL'),
                          (not self.filter_vars['CONF>60'] or conf>=60), (not self.filter_vars['VOL_SPIKE'] or vol_spike),
                          (not self.filter_vars['HTF_ALIGN'] or htf_align), (not self.filter_vars['SENT_CONF'] or sent_confirm),
                          (not self.filter_vars['WAVE_X'] or wave_cross), (not self.filter_vars['DIV_CONF'] or div_confirm),
                          (not self.filter_vars['ST_ALIGN'] or st_align)))
            if not passes: continue
            # --- End Filter Logic ---

            symbol=item.get('symbol','N/A'); displayed_symbols_this_run.append(symbol)
            values_tuple=item.get('values',())
            logging.debug(f"Adding row for {symbol}") # Log row addition

            # Add row with appropriate cell coloring/widgets
            with dpg.table_row(parent="live_table"):
                # ... (cell population logic as before) ...
                 if len(values_tuple) != len(self.columns_config):
                     logging.error(f"Data mismatch for {symbol}: Cols {len(self.columns_config)}, Vals {len(values_tuple)}.")
                     with dpg.table_cell(): dpg.add_text(f"Data Error for {symbol}", color=self.theme['error'], wrap=0)
                     for _ in range(len(self.columns_config)-1):
                         with dpg.table_cell(): dpg.add_text("ERR",color=self.theme['error'])
                     continue
                 for i, value in enumerate(values_tuple):
                    col_label, _ = self.columns_config[i]; cell_value = str(value)
                    color = self.theme['foreground']; widget_type = 'text'
                    # Customize based on column label
                    if col_label=="TG": widget_type='button'
                    elif col_label=="Buy": widget_type='button'
                    elif col_label=="Signal": color = self.theme['success'] if cell_value=="BUY" else (self.theme['error'] if cell_value=="SELL" else color)
                    elif col_label=="Conf.":
                        conf_val = item.get('confidence',0.0)
                        if conf_val >= 80: color = self.theme['success']
                        elif conf_val >= 60: color = self.theme['warning']
                        trade_plan_data = item.get('trade_plan')
                        if isinstance(trade_plan_data, dict) and trade_plan_data.get('note'): color = (255, 165, 0, 255)
                    elif col_label=="Chg.":
                         try: change_val = float(cell_value.replace('%','')); color = self.theme['success'] if change_val>0.1 else (self.theme['error'] if change_val<-0.1 else color)
                         except: pass
                    elif col_label=="STrend": color = self.theme['success'] if "Up" in cell_value else (self.theme['error'] if "Down" in cell_value else color)
                    elif col_label=="HTF": color = self.theme['warning'] if "Konfl" in cell_value else (self.theme['success'] if "Up" in cell_value else (self.theme['error'] if "Down" in cell_value else color))
                    # Add widget
                    with dpg.table_cell():
                        if widget_type == 'button':
                            if col_label == "TG":
                                dpg.add_button(label=cell_value, callback=self.on_send_button_click, user_data=symbol, width=-1)
                            elif col_label == "Buy":
                                dpg.add_button(label=cell_value, callback=self.on_buy_button_click, user_data=symbol, width=-1)
                        else:
                            dpg.add_text(cell_value, color=color, wrap=0)

        self.currently_displayed_symbols = displayed_symbols_this_run
        filter_count = len(active_filters); status_msg = f"Status: Displaying {len(self.currently_displayed_symbols)} assets"
        if filter_count > 0: status_msg += f" ({filter_count} filter{'s' if filter_count > 1 else ''} active)"
        if dpg.does_item_exist("status_text"): dpg.set_value("status_text", status_msg)

        logging.debug("apply_live_filters FINISHED") # Add log to see when it finishes

    def add_symbols(self):
        sym_str = dpg.get_value("symbol_input").strip()
        if not sym_str: dpg.set_value("status_text", "Status: Input empty."); return
        new_syms = sorted(list(set(s.strip().upper() for s in sym_str.split(',') if s.strip() and re.match(r'^[A-Z0-9]+USDT$', s.strip().upper()))))
        if not new_syms: dpg.set_value("status_text", "Status: No valid symbols (e.g., BTCUSDT)."); return
        self.custom_symbols = new_syms; self.save_symbols()
        dpg.set_value("status_text", f"Status: Loaded {len(self.custom_symbols)} symbols.")
        self.all_live_results.clear(); self.currently_displayed_symbols.clear(); self._clear_table()

    def clear_symbols(self):
        self.custom_symbols = []; self.save_symbols(); dpg.set_value("symbol_input", "")
        dpg.set_value("status_text", "Status: Watchlist cleared.")
        self.all_live_results.clear(); self.currently_displayed_symbols.clear(); self._clear_table()

    def save_symbols(self):
        try:
            with Path('watchlist.json').open('w', encoding='utf-8') as f: json.dump(self.custom_symbols, f, indent=2)
        except Exception as e: logging.error(f"Error saving watchlist: {e}")

    def load_symbols(self):
        path = Path('watchlist.json'); defaults = ['BTCUSDT', 'ETHUSDT']
        if path.exists():
            try:
                with path.open('r', encoding='utf-8') as f: loaded = json.load(f)
                if isinstance(loaded, list):
                    self.custom_symbols = sorted(list(set(s.strip().upper() for s in loaded if isinstance(s, str) and s.strip())))
                    logging.info(f"Loaded {len(self.custom_symbols)} symbols from watchlist.json"); return
                else: logging.warning("watchlist.json not a valid list.")
            except Exception as e: logging.error(f"Error loading watchlist: {e}. Using defaults.")
        else: logging.info("watchlist.json not found. Using defaults.")
        self.custom_symbols = defaults; self.save_symbols()

    def toggle_live_analysis(self, sender=None, app_data=None):
        if not self.live_running:
            self.live_running = True
            self.stop_event.clear()
            dpg.configure_item("scan_btn", label="Stop Scan")
            dpg.configure_item("refresh_btn", enabled=False)
            self.bot.cache.clear()
            logging.info("Starting full manual analysis...")
            self.analysis_thread = threading.Thread(target=self.run_live_analysis, args=(self.custom_symbols,), daemon=True)
            self.analysis_thread.start()
        else:
            logging.info("Requesting manual analysis stop...")
            self.stop_event.set()  # Signal thread to stop
            dpg.configure_item("scan_btn", label="Stopping...", enabled=False)  # Immediate feedback

            # Forcefully stop the thread if it’s still alive
            if self.analysis_thread and self.analysis_thread.is_alive():
                try:
                    self.analysis_thread.join(timeout=1.0)  # Wait 1 second for clean exit
                    if self.analysis_thread.is_alive():
                        logging.warning("Thread did not stop gracefully. Forcing termination.")
                        # Python doesn’t natively kill threads, but daemon status ensures it dies when main thread exits
                        # We’ll rely on resetting state instead
                except Exception as e:
                    logging.error(f"Error joining thread: {e}")

            # Reset state immediately (no queue delay)
            self.live_running = False
            dpg.configure_item("scan_btn", label="Start Scan", enabled=True)
            dpg.configure_item("refresh_btn", enabled=True)
            dpg.set_value("status_text", "Status: Stopped (Forced)")
            logging.info("Analysis forcefully stopped and GUI reset.")

    def run_live_analysis(self, symbols_to_analyze: List[str]):
        if not symbols_to_analyze:
            if dpg.is_dearpygui_running() and dpg.does_item_exist("status_text"):
                dpg.set_value("status_text", "Status: No symbols.")
            self.live_running = False  # Reset immediately
            return
        total = len(symbols_to_analyze)
        if dpg.is_dearpygui_running() and dpg.does_item_exist("status_text"):
            dpg.set_value("status_text", f"Status: Starting analysis for {total}...")
        processed = 0
        max_workers = min(10, total)
        try:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = {executor.submit(self.analyze_symbol, symbol): symbol for symbol in symbols_to_analyze}
                for i, future in enumerate(as_completed(futures), 1):
                    if self.stop_event.is_set():
                        logging.info("Analysis cancelled mid-process.")
                        executor.shutdown(wait=False, cancel_futures=True)  # Kill all pending tasks
                        return  # Exit immediately
                    symbol = futures[future]
                    try:
                        result = future.result()
                        self.all_live_results[symbol] = result
                        if result:
                            processed += 1
                    except Exception as e:
                        logging.error(f"Error processing {symbol}: {e}")
                        self.all_live_results[symbol] = None

                    if self.stop_event.is_set():
                        logging.info("Analysis cancelled after result.")
                        executor.shutdown(wait=False, cancel_futures=True)
                        return  # Exit immediately

                    if i % 5 == 0 or i == total:
                        status = f"Status: Analyzing {symbol} ({i}/{total})... {processed} processed."
                        if dpg.is_dearpygui_running() and dpg.does_item_exist("status_text"):
                            dpg.set_value("status_text", status)
        except Exception as e:
            logging.error(f"ThreadPool error: {e}")
        finally:
            if self.stop_event.is_set():
                logging.info("Analysis stopped.")
            else:
                final_status = f"Status: Analysis complete - {processed}/{total} processed."
                logging.info(final_status)
                if dpg.is_dearpygui_running() and dpg.does_item_exist("status_text"):
                    dpg.set_value("status_text", final_status)
            # No queue—rely on toggle_live_analysis to reset GUI

    def toggle_auto_refresh(self, sender, app_data):
        self.live_scan = app_data
        if self.live_scan:
            if self.live_scan_thread is None or not self.live_scan_thread.is_alive():
                self.stop_event.clear()
                self.live_scan_thread = threading.Thread(target=self.auto_refresh_loop, daemon=True)
                self.live_scan_thread.start()
                if dpg.does_item_exist("status_text"):
                    dpg.set_value("status_text", "Status: Auto Refresh ON")
                logging.info("Auto Refresh enabled.")
                if dpg.does_item_exist("refresh_btn"):
                    dpg.configure_item("refresh_btn", enabled=False)
                if dpg.does_item_exist("scan_btn"):
                    dpg.configure_item("scan_btn", enabled=False)
        else:
            logging.info("Requesting Auto Refresh stop...")
            self.stop_event.set()
            if dpg.does_item_exist("status_text"):
                dpg.set_value("status_text", "Status: Stopping Auto Refresh...")

            # Forcefully stop threads
            if self.live_scan_thread and self.live_scan_thread.is_alive():
                try:
                    self.live_scan_thread.join(timeout=1.0)
                    if self.live_scan_thread.is_alive():
                        logging.warning("Auto refresh thread did not stop. Daemon will terminate on exit.")
                except Exception as e:
                    logging.error(f"Error joining live_scan_thread: {e}")
            if self.analysis_thread and self.analysis_thread.is_alive():
                try:
                    self.analysis_thread.join(timeout=1.0)
                    if self.analysis_thread.is_alive():
                        logging.warning("Analysis thread did not stop. Daemon will terminate on exit.")
                except Exception as e:
                    logging.error(f"Error joining analysis_thread: {e}")

            # Reset GUI state immediately
            self.live_running = False
            self.live_scan = False
            if dpg.is_dearpygui_running():
                if dpg.does_item_exist("refresh_btn"):
                    dpg.configure_item("refresh_btn", enabled=True)
                if dpg.does_item_exist("scan_btn"):
                    dpg.configure_item("scan_btn", label="Start Scan", enabled=True)
                if dpg.does_item_exist("auto_refresh_cb"):
                    dpg.set_value("auto_refresh_cb", False)
                if dpg.does_item_exist("status_text"):
                    dpg.set_value("status_text", "Status: Auto Refresh OFF")
            logging.info("Auto Refresh forcefully stopped and GUI reset.")

    def auto_refresh_loop(self):
        logging.info("Auto Refresh loop started.")
        while self.live_scan and not self.stop_event.is_set():
            if not self.live_running:
                self.refresh_live_analysis()
                # Wait for analysis thread to finish
                if self.analysis_thread and self.analysis_thread.is_alive():
                    self.analysis_thread.join()  # Block until scan completes
            interval = dpg.get_value("refresh_interval") if dpg.does_item_exist("refresh_interval") else 60
            logging.info(f"Auto Refresh waiting {interval} seconds for next cycle...")
            stopped = self.stop_event.wait(timeout=max(10, interval))
            if stopped:
                logging.info("Auto Refresh stopped by event.")
                break
        logging.info("Auto Refresh loop finished.")
        if self.stop_event.is_set():
            # Ensure cleanup if stopped
            self.live_running = False
            self.live_scan = False
            if dpg.is_dearpygui_running():
                if dpg.does_item_exist("status_text"):
                    dpg.set_value("status_text", "Status: Auto Refresh OFF")

    def refresh_live_analysis(self):
        if self.live_running:
            logging.warning("Analysis running. Refresh ignored.")
            return
        self.live_running = True
        self.stop_event.clear()
        dpg.configure_item("scan_btn", label="Stop Scan")
        dpg.configure_item("refresh_btn", enabled=False)
        filters_active = any(self.filter_vars.values())
        syms = self.currently_displayed_symbols if filters_active and self.currently_displayed_symbols else self.custom_symbols
        num = len(syms)
        if num == len(self.custom_symbols) or not filters_active:
            logging.info(f"Refreshing all {num} symbols...")
            self.bot.cache.clear()
        else:
            logging.info(f"Refreshing {num} filtered symbols...")
        self.analysis_thread = threading.Thread(target=self.run_live_analysis, args=(syms,), daemon=True)
        self.analysis_thread.start()

    def _finalize_auto_refresh_stop(self):
        if self.live_running: self.stop_event.set()
        if dpg.is_dearpygui_running():
            if dpg.does_item_exist("refresh_btn"): dpg.configure_item("refresh_btn", enabled=True)
            if dpg.does_item_exist("scan_btn"): dpg.configure_item("scan_btn", enabled=True, label="Start Scan")
            if dpg.does_item_exist("auto_refresh_cb"): dpg.set_value("auto_refresh_cb", False)
            if dpg.does_item_exist("status_text") and self.live_scan == False: dpg.set_value("status_text", "Status: Auto Refresh OFF")
        self.live_scan = False

    def _finalize_analysis_run(self):
        logging.debug("_finalize_analysis_run CALLED.")
        self.live_running = False
        if dpg.is_dearpygui_running():
            is_auto = dpg.get_value("auto_refresh_cb") if dpg.does_item_exist("auto_refresh_cb") else False
            logging.debug(f"_finalize_analysis_run: Setting button states (is_auto={is_auto}).")
            if dpg.does_item_exist("scan_btn"):
                dpg.configure_item("scan_btn", label="Start Scan", enabled=not is_auto)
            if dpg.does_item_exist("refresh_btn"):
                dpg.configure_item("refresh_btn", enabled=not is_auto)
        else:
            logging.warning("_finalize_analysis_run called but DPG not running.")
        logging.debug("_finalize_analysis_run FINISHED.")

    def _finalize_analysis_run(self):
         self.live_running = False
         if dpg.is_dearpygui_running():
             is_auto = dpg.get_value("auto_refresh_cb") if dpg.does_item_exist("auto_refresh_cb") else False
             if dpg.does_item_exist("scan_btn"): dpg.configure_item("scan_btn", label="Start Scan", enabled=not is_auto)
             if dpg.does_item_exist("refresh_btn"): dpg.configure_item("refresh_btn", enabled=not is_auto)

    # --- UPDATED Symbol Analysis with more robust return handling ---
    def analyze_symbol(self, symbol: str) -> Optional[Dict[str, Any]]:
        result_data = None
        symbol_name_log = symbol if symbol else "N/A"

        try:
            # <<< Check 1: Before anything >>>
            if self.stop_event.is_set(): logging.debug(f"({symbol_name_log}) Stop requested before analysis start."); return None
            timeframe = self.timeframe
            logging.debug(f"({symbol_name_log}) Starting analysis for {timeframe}...")

            # <<< Check 2: Before data fetch >>>
            if self.stop_event.is_set(): logging.debug(f"({symbol_name_log}) Stop requested before fetch_data."); return None
            df = self.bot.fetch_data(symbol, timeframe, limit=70)
            if df.empty:
                logging.debug(f"({symbol_name_log}) Fetch data returned empty. Returning None.")
                return None
            df.name = symbol

            # <<< Check 3: Before indicator calculation >>>
            if self.stop_event.is_set(): logging.debug(f"({symbol_name_log}) Stop requested before calculate_indicators."); return None
            df = self.bot.calculate_indicators(df, timeframe)
            if df.empty:
                 logging.debug(f"({symbol_name_log}) Calculate indicators returned empty. Returning None.")
                 return None

            # <<< Check 4: Before signal generation >>>
            if self.stop_event.is_set(): logging.debug(f"({symbol_name_log}) Stop requested before generate_signal."); return None
            signal, confidence, trade_plan = self.bot.generate_signal(df, timeframe)

            last = df.iloc[-1]
            if pd.isna(last['close']):
                 logging.warning(f"({symbol_name_log}) Last row NaN close. Skip result.")
                 return None
            prev = df.iloc[-2] if len(df) > 1 else last

            # --- Formatting ---
            price=last['close']; decimals=2
            if not pd.isna(price) and price>0: log10_p=np.floor(np.log10(abs(price))); decimals=max(2, min(8, 4-int(log10_p)))
            price_str = f"{price:.{decimals}f}" if not pd.isna(price) else "N/A"
            entry_str, sl_str, tp_str = "N/A", "N/A", "N/A"
            if trade_plan: # Only format if trade_plan exists
                e_low, e_high = trade_plan.get('entry_zone', (np.nan, np.nan))
                if not pd.isna(e_low) and not pd.isna(e_high):
                    entry_str = f"{e_low:.{decimals}f} - {e_high:.{decimals}f}";
                    note = trade_plan.get('note','')
                    entry_str += f" {note}" if note else ""
                else: entry_str = "N/A" # Indicate zone calculation issue if needed
                sl=trade_plan.get('stop_loss',np.nan); tp=trade_plan.get('take_profit',np.nan)
                sl_str = f"{sl:.{decimals}f}" if not pd.isna(sl) else "N/A"; tp_str = f"{tp:.{decimals}f}" if not pd.isna(tp) else "N/A"
            # If signal is HOLD, trade_plan will be None, so entry/sl/tp remain "N/A"

            change_str = "N/A"
            if not pd.isna(last['close']) and not pd.isna(prev['close']) and prev['close'] != 0: change = ((last['close'] - prev['close']) / prev['close']) * 100; change_str = f"{change:+.2f}%"
            trend_icon = '🔼 Up' if not pd.isna(last['ema_fast']) and not pd.isna(last['ema_slow']) and last['ema_fast']>last['ema_slow'] else ('🔽 Down' if not pd.isna(last['ema_fast']) and not pd.isna(last['ema_slow']) else '❓')
            mom_icon = '➕ Pos' if not pd.isna(last['macd_hist']) and last['macd_hist']>0 else ('➖ Neg' if not pd.isna(last['macd_hist']) else '❓')
            vol_icon="N/A"; vol_spike=False
            if not pd.isna(last['volume']):
                vol=last['volume']; vol_icon=f"{vol/1e9:.1f}B" if vol>1e9 else (f"{vol/1e6:.1f}M" if vol>1e6 else (f"{vol/1e3:.1f}K" if vol>1e3 else f"{vol:.0f}")); vol_spike=last.get('volume_spike',False); vol_icon+="💥" if vol_spike else ""
            st_sig=last.get('supertrend_signal',0); st_status='➖ N/A'; st_align=False
            if st_sig==1: st_status='🔼 ST Up'; st_align = signal=='BUY'
            elif st_sig==-1: st_status='🔽 ST Down'; st_align = signal=='SELL'
            div_icon=''; div_confirm=False
            bull=last.get('bullish_div_rsi',False) or last.get('bullish_div_stoch',False) or last.get('bullish_div_wt1',False) or last.get('bullish_div_wt2',False)
            bear=last.get('bearish_div_rsi',False) or last.get('bearish_div_stoch',False) or last.get('bearish_div_wt1',False) or last.get('bearish_div_wt2',False)
            if bull and bear: div_icon='❓ Both'
            elif bull: div_icon='📈 Bull'
            elif bear: div_icon='📉 Bear'
            if (signal=='BUY' and bull) or (signal=='SELL' and bear): div_confirm=True
            market_type = self.bot.determine_market_type(df)
            higher_signal = self.bot.analyze_higher_timeframe(symbol, timeframe)
            if self.stop_event.is_set(): return None
            # Fetch funding rate
            funding_rate, _ = self.bot.fetch_funding_rate(symbol)

            # Fetch open interest
            oi_data = self.bot.fetch_open_interest(symbol, timeframe)

            # Process open interest trend
            oi_trend="N/A"; sent_confirm=False
            if oi_data and len(oi_data)>=2:
                oi_now, oi_prev = oi_data[1], oi_data[0]
                oi_trend = '📈 Inc' if oi_now>oi_prev*1.001 else ('📉 Dec' if oi_now<oi_prev*0.999 else '➖ Stable')

            # Format funding rate string
            funding_str = f"{funding_rate*100:.4f}%" if funding_rate is not None else "N/A"

            # Enhanced sentiment calculation based on funding rate and open interest
            sentiment_icon = 'N/A'

            # Only calculate sentiment if we have both funding rate and OI trend
            if funding_rate is not None and oi_trend != 'N/A':
                # Bullish sentiment: Negative funding rate (shorts paying longs) + increasing open interest
                if funding_rate < 0 and oi_trend == '📈 Inc':
                    sentiment_icon = '🔥 Long' # Strong bullish sentiment - potential short squeeze
                # Bearish sentiment: Positive funding rate (longs paying shorts) + increasing open interest
                elif funding_rate > 0 and oi_trend == '📈 Inc':
                    sentiment_icon = '💧 Short' # Strong bearish sentiment - potential long liquidation
                # Neutral with slight bullish bias
                elif funding_rate < 0 and oi_trend == '➖ Stable':
                    sentiment_icon = '📈 Bullish'
                # Neutral with slight bearish bias
                elif funding_rate > 0 and oi_trend == '➖ Stable':
                    sentiment_icon = '📉 Bearish'
                # Decreasing open interest - positions closing
                elif oi_trend == '📉 Dec':
                    sentiment_icon = '🔄 Closing'
                else:
                    sentiment_icon = '⚖️ Neutral'

                # Additional check for signal alignment with sentiment
                if (signal == 'BUY' and (sentiment_icon == '🔥 Long' or sentiment_icon == '📈 Bullish')) or \
                   (signal == 'SELL' and (sentiment_icon == '💧 Short' or sentiment_icon == '📉 Bearish')):
                    sent_confirm = True
                else:
                    sent_confirm = False
            wave_icon="N/A"; wave_cross=False
            if not pd.isna(last['tci']) and not pd.isna(last['wt2']):
                wt1=last['tci']; wt2=last['wt2']; wave_icon='🔼 Above' if wt1>wt2 else '🔽 Below'
                if not pd.isna(prev['tci']) and not pd.isna(prev['wt2']):
                    if wt1>wt2 and prev['tci']<=prev['wt2']: wave_icon+=' X Up'; wave_cross=(signal=='BUY')
                    elif wt1<wt2 and prev['tci']>=prev['wt2']: wave_icon+=' X Down'; wave_cross=(signal=='SELL')

            if self.stop_event.is_set(): logging.debug(f"({symbol_name_log}) Stop requested before final assembly."); return None

            values_tuple = ( # Assemble tuple
                symbol, signal, f"{confidence:.1f}%", price_str, entry_str, sl_str, tp_str, change_str, trend_icon, st_status,
                mom_icon, vol_icon, div_icon, market_type, higher_signal, funding_str, oi_trend, sentiment_icon, wave_icon, "Send", "Buy"
            )
            if len(values_tuple) != len(self.columns_config): # Validate length
                 logging.critical(f"({symbol_name_log}) FATAL: Value tuple length mismatch. Check analyze_symbol!")
                 expected=len(self.columns_config); current=len(values_tuple)
                 values_tuple = values_tuple + ("ERROR",)*(expected-current) if current<expected else values_tuple[:expected]

            result_data = { # Assign final result
                'symbol': symbol, 'timeframe': timeframe, 'values': values_tuple, 'signal': signal, 'confidence': confidence,
                 'trade_plan': trade_plan, 'higher_signal': higher_signal, 'sentiment_confirm': sent_confirm, 'wave_cross': wave_cross,
                 'volume_spike': vol_spike, 'div_confirm': div_confirm, 'st_align': st_align,
                 'timestamp': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

        except Exception as e:
            logging.error(f"({symbol_name_log}) Error during analyze_symbol for {timeframe}: {type(e).__name__} - {e}", exc_info=True)
            result_data = None

        finally:
             log_signal = result_data['signal'] if isinstance(result_data, dict) else "None"
             # Only log return if stop wasn't the reason for returning None
             if not (result_data is None and self.stop_event.is_set()):
                 logging.debug(f"({symbol_name_log}) analyze_symbol returning: Signal={log_signal}")
             return result_data

    # --- Positions Management ---
    def refresh_positions(self, sender=None, app_data=None):
        """Refresh the positions table with current positions"""
        try:
            # Update status
            if dpg.does_item_exist("positions_status_text"):
                dpg.set_value("positions_status_text", "Positions Status: Refreshing...")

            # Check for positions that were closed externally
            self.check_closed_positions()

            # Get positions from Bybit
            positions = self.bot.get_positions()
            self.positions_data = positions

            # Save current positions for future reference
            self.save_last_positions(positions)

            # Clear existing rows
            if dpg.does_item_exist("positions_table"):
                children = dpg.get_item_children("positions_table", 1)
                if children and len(children) > 0:
                    for child in children:
                        try:
                            dpg.delete_item(child)
                        except Exception as e:
                            logging.warning(f"Error deleting child {child}: {e}")

            # Add new rows
            for i, position in enumerate(positions):
                symbol = position.get('symbol', 'N/A')
                side = position.get('side', 'N/A')
                size = position.get('size', '0')
                entry_price = position.get('avgPrice', '0')
                current_price = position.get('markPrice', '0')
                position_idx = position.get('positionIdx', '0')

                # Calculate P&L
                try:
                    entry_price_float = float(entry_price)
                    current_price_float = float(current_price)
                    size_float = float(size)

                    if side == "Buy":
                        pnl = (current_price_float - entry_price_float) * size_float
                        pnl_percent = ((current_price_float / entry_price_float) - 1) * 100
                    else:  # Sell
                        pnl = (entry_price_float - current_price_float) * size_float
                        pnl_percent = ((entry_price_float / current_price_float) - 1) * 100

                    pnl_str = f"${pnl:.2f}"
                    pnl_percent_str = f"{pnl_percent:.2f}%"

                    # Color based on P&L
                    pnl_color = self.theme['success'] if pnl >= 0 else self.theme['error']
                except (ValueError, TypeError):
                    pnl_str = "N/A"
                    pnl_percent_str = "N/A"
                    pnl_color = self.theme['foreground']

                # Get TP/SL
                take_profit = position.get('takeProfit', 'N/A')
                stop_loss = position.get('stopLoss', 'N/A')

                # Add row to table
                with dpg.table_row(parent="positions_table", tag=f"positions_row_{i}"):
                    dpg.add_text(symbol)
                    dpg.add_text(side, color=self.theme['success'] if side == "Buy" else self.theme['error'])
                    dpg.add_text(size)
                    dpg.add_text(entry_price)
                    dpg.add_text(current_price)
                    dpg.add_text(pnl_str, color=pnl_color)
                    dpg.add_text(pnl_percent_str, color=pnl_color)
                    dpg.add_text(take_profit)
                    dpg.add_text(stop_loss)

                    # Add TP/SL modification buttons
                    with dpg.group(horizontal=True):
                        dpg.add_button(label="Set", callback=self.on_modify_tp_click, user_data={"symbol": symbol, "position_idx": position_idx}, width=40)

                    with dpg.group(horizontal=True):
                        dpg.add_button(label="Set", callback=self.on_modify_sl_click, user_data={"symbol": symbol, "position_idx": position_idx}, width=40)

                    # Add close position button
                    dpg.add_button(label="Close", callback=self.on_close_position_click, user_data={"symbol": symbol, "position_idx": position_idx}, width=60)

            # Update status
            if dpg.does_item_exist("positions_status_text"):
                timestamp = datetime.datetime.now().strftime("%H:%M:%S")
                dpg.set_value("positions_status_text", f"Positions Status: Updated at {timestamp}")

            return True
        except Exception as e:
            logging.error(f"Error refreshing positions: {e}", exc_info=True)
            if dpg.does_item_exist("positions_status_text"):
                dpg.set_value("positions_status_text", f"Positions Status: Error - {str(e)}")
            return False

    def save_last_positions(self, positions):
        """Save current positions to file for tracking closed positions"""
        try:
            last_positions_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "last_positions.json")
            with open(last_positions_file, "w") as f:
                json.dump(positions, f, indent=2)
            logging.debug(f"Saved {len(positions)} positions to last_positions.json")
        except Exception as e:
            logging.error(f"Error saving last positions: {e}")

    def toggle_auto_refresh_positions(self, sender=None, app_data=None):
        """Toggle automatic refresh of positions"""
        self.auto_refresh_positions = app_data if app_data is not None else not self.auto_refresh_positions

        if self.auto_refresh_positions:
            if self.positions_thread is None or not self.positions_thread.is_alive():
                self.stop_event.clear()
                self.positions_thread = threading.Thread(target=self.auto_refresh_positions_thread, daemon=True)
                self.positions_thread.start()
                logging.info("Started auto-refresh positions thread")
        else:
            logging.info("Stopped auto-refresh positions")

    def auto_refresh_positions_thread(self):
        """Thread for automatically refreshing positions"""
        while not self.stop_event.is_set() and self.auto_refresh_positions:
            try:
                # Get refresh interval
                interval = 30  # Default
                if dpg.does_item_exist("positions_refresh_interval"):
                    interval = dpg.get_value("positions_refresh_interval")

                # Queue the refresh operation to run on the main thread
                # This will also check for closed positions
                self.gui_queue.put((self.refresh_positions, (), {}))

                # Also refresh the trade history to show any newly closed positions
                self.gui_queue.put((self.refresh_trade_history, (), {}))

                # Sleep for the interval
                for _ in range(interval):
                    if self.stop_event.is_set() or not self.auto_refresh_positions:
                        break
                    time.sleep(1)
            except Exception as e:
                logging.error(f"Error in auto refresh positions thread: {e}", exc_info=True)
                time.sleep(5)  # Sleep on error to avoid tight loop

    def on_modify_tp_click(self, sender=None, app_data=None, user_data=None):
        """Handle click on modify take profit button"""
        if not user_data:
            return

        symbol = user_data.get("symbol")
        position_idx = user_data.get("position_idx")

        # Create a popup for entering new TP
        with dpg.window(label=f"Modify Take Profit for {symbol}", modal=True, width=400, height=200, pos=[400, 200]):
            dpg.add_text("Enter new Take Profit price:")
            dpg.add_input_float(tag="new_tp_input", default_value=0.0, format="%.8f", width=200)
            with dpg.group(horizontal=True):
                dpg.add_button(label="Cancel", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))
                dpg.add_button(label="Apply", width=100, callback=lambda: self.apply_tp_modification(symbol, dpg.get_value("new_tp_input"), position_idx))

    def apply_tp_modification(self, symbol, new_tp, position_idx):
        """Apply the take profit modification"""
        try:
            # Close the popup
            dpg.delete_item(dpg.last_container())

            # Apply the modification
            result = self.bot.modify_tp_sl(symbol, take_profit=new_tp, position_idx=position_idx)

            if result.get("success"):
                logging.info(f"Successfully modified TP for {symbol} to {new_tp}")
                # Refresh positions to show the update
                self.refresh_positions()
            else:
                logging.error(f"Failed to modify TP: {result.get('message')}")
                # Show error message
                with dpg.window(label="Error", modal=True, width=400, height=150, pos=[400, 200]):
                    dpg.add_text(f"Failed to modify TP: {result.get('message')}")
                    dpg.add_button(label="OK", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))
        except Exception as e:
            logging.error(f"Error applying TP modification: {e}", exc_info=True)

    def on_modify_sl_click(self, sender=None, app_data=None, user_data=None):
        """Handle click on modify stop loss button"""
        if not user_data:
            return

        symbol = user_data.get("symbol")
        position_idx = user_data.get("position_idx")

        # Create a popup for entering new SL
        with dpg.window(label=f"Modify Stop Loss for {symbol}", modal=True, width=400, height=200, pos=[400, 200]):
            dpg.add_text("Enter new Stop Loss price:")
            dpg.add_input_float(tag="new_sl_input", default_value=0.0, format="%.8f", width=200)
            with dpg.group(horizontal=True):
                dpg.add_button(label="Cancel", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))
                dpg.add_button(label="Apply", width=100, callback=lambda: self.apply_sl_modification(symbol, dpg.get_value("new_sl_input"), position_idx))

    def apply_sl_modification(self, symbol, new_sl, position_idx):
        """Apply the stop loss modification"""
        try:
            # Close the popup
            dpg.delete_item(dpg.last_container())

            # Apply the modification
            result = self.bot.modify_tp_sl(symbol, stop_loss=new_sl, position_idx=position_idx)

            if result.get("success"):
                logging.info(f"Successfully modified SL for {symbol} to {new_sl}")
                # Refresh positions to show the update
                self.refresh_positions()
            else:
                logging.error(f"Failed to modify SL: {result.get('message')}")
                # Show error message
                with dpg.window(label="Error", modal=True, width=400, height=150, pos=[400, 200]):
                    dpg.add_text(f"Failed to modify SL: {result.get('message')}")
                    dpg.add_button(label="OK", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))
        except Exception as e:
            logging.error(f"Error applying SL modification: {e}", exc_info=True)

    def on_close_position_click(self, sender=None, app_data=None, user_data=None):
        """Handle click on close position button"""
        if not user_data:
            return

        symbol = user_data.get("symbol")
        position_idx = user_data.get("position_idx")

        # Create a confirmation popup
        popup_id = dpg.generate_uuid()
        with dpg.window(label=f"Close Position for {symbol}", modal=True, width=400, height=150, pos=[400, 200], tag=popup_id):
            dpg.add_text(f"Are you sure you want to close your position for {symbol}?")
            with dpg.group(horizontal=True):
                dpg.add_button(label="Cancel", width=100, callback=lambda: dpg.delete_item(popup_id))
                dpg.add_button(label="Close Position", width=150, callback=lambda: self.confirm_close_position(symbol, position_idx, popup_id))

    def confirm_close_position(self, symbol, position_idx, popup_id):
        """Confirm and execute position closing"""
        try:
            # Close the popup
            dpg.delete_item(popup_id)

            # Close the position
            result = self.bot.close_position(symbol, position_idx)

            if result.get("success"):
                logging.info(f"Successfully closed position for {symbol}")

                # Get position details for the trade history
                position_data = result.get("position_data", {})

                # Add to trade history
                if position_data:
                    # Calculate duration if possible
                    duration = "N/A"
                    if position_data.get("created_time"):
                        try:
                            # Log the created_time for debugging
                            logging.info(f"Position created_time: {position_data.get('created_time')}")

                            # Try to handle different timestamp formats
                            created_time_str = str(position_data.get("created_time"))

                            # If it's a large number (milliseconds since epoch)
                            if len(created_time_str) > 10:
                                created_time = datetime.datetime.fromtimestamp(int(created_time_str) / 1000)
                            # If it's a standard timestamp (seconds since epoch)
                            elif created_time_str.isdigit():
                                created_time = datetime.datetime.fromtimestamp(int(created_time_str))
                            # If it's already a datetime string
                            else:
                                # Try different formats
                                try:
                                    created_time = datetime.datetime.fromisoformat(created_time_str)
                                except ValueError:
                                    # Default to 24 hours ago if we can't parse the timestamp
                                    created_time = datetime.datetime.now() - datetime.timedelta(hours=24)
                                    logging.warning(f"Could not parse created_time: {created_time_str}, using default")

                            # Ensure created_time is not in the future or too far in the past
                            now = datetime.datetime.now()
                            if created_time > now:
                                created_time = now - datetime.timedelta(hours=1)  # Default to 1 hour ago
                                logging.warning(f"Created time was in the future, using default")
                            elif (now - created_time).days > 365:  # More than a year ago
                                created_time = now - datetime.timedelta(hours=24)  # Default to 24 hours ago
                                logging.warning(f"Created time was too far in the past, using default")

                            duration_delta = now - created_time

                            # Format duration
                            days = duration_delta.days
                            hours, remainder = divmod(duration_delta.seconds, 3600)
                            minutes, seconds = divmod(remainder, 60)

                            if days > 0:
                                duration = f"{days}d {hours}h {minutes}m"
                            elif hours > 0:
                                duration = f"{hours}h {minutes}m"
                            else:
                                duration = f"{minutes}m {seconds}s"
                        except Exception as e:
                            logging.error(f"Error calculating duration: {e}")

                    # Calculate PnL percentage
                    pnl_percent = 0.0
                    try:
                        entry_price = float(position_data.get("entry_price", 0))
                        exit_price = float(position_data.get("exit_price", position_data.get("mark_price", 0)))
                        side = position_data.get("side")

                        if entry_price > 0:
                            if side == "Buy":
                                pnl_percent = (exit_price - entry_price) / entry_price * 100
                            else:  # Sell
                                pnl_percent = (entry_price - exit_price) / entry_price * 100
                    except Exception as e:
                        logging.error(f"Error calculating PnL percentage: {e}")

                    # Ensure we have valid values for entry_price and size
                    entry_price = position_data.get("entry_price")
                    if entry_price is None or entry_price == "":
                        entry_price = "0.0"

                    size = position_data.get("size")
                    if size is None or size == "":
                        size = "0.0"

                    trade_data = {
                        "symbol": symbol,
                        "side": "Sell" if position_data.get("side") == "Buy" else "Buy",  # Opposite of position side
                        "entry_price": entry_price,
                        "exit_price": position_data.get("exit_price", position_data.get("mark_price")),
                        "size": size,
                        "pnl": position_data.get("pnl", "0"),
                        "pnl_percent": pnl_percent,
                        "exit_type": "Manual Close",
                        "duration": duration,
                        "timestamp": datetime.datetime.now().isoformat()
                    }

                    # Log the trade data for debugging
                    logging.info(f"Trade data: {trade_data}")
                    self.add_trade_to_history(trade_data)

                # Refresh positions to show the update
                self.refresh_positions()
            else:
                logging.error(f"Failed to close position: {result.get('message')}")
                # Show error message
                with dpg.window(label="Error", modal=True, width=400, height=150, pos=[400, 200]):
                    dpg.add_text(f"Failed to close position: {result.get('message')}")
                    dpg.add_button(label="OK", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))
        except Exception as e:
            logging.error(f"Error closing position: {e}", exc_info=True)

    def check_closed_positions(self):
        """
        Check for positions that were closed while the app was not running
        and update the trade history accordingly
        """
        try:
            # Get the current open positions
            current_positions = self.bot.get_positions()
            current_symbols = {pos.get('symbol'): pos for pos in current_positions}

            # Load the last known positions from file
            last_positions_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "last_positions.json")
            last_positions = []

            if os.path.exists(last_positions_file):
                try:
                    with open(last_positions_file, "r") as f:
                        last_positions = json.load(f)
                except Exception as e:
                    logging.error(f"Error loading last positions: {e}")

            # Check for positions that were closed
            for last_pos in last_positions:
                symbol = last_pos.get('symbol')
                if symbol not in current_symbols:
                    # Position was closed, determine if it hit TP or SL
                    exit_type = "Unknown"

                    # Try to determine if it hit TP or SL based on the last price
                    tp = float(last_pos.get('takeProfit', 0) or 0)
                    sl = float(last_pos.get('stopLoss', 0) or 0)
                    side = last_pos.get('side')

                    # Get the current price to estimate exit price
                    try:
                        if self.bot.session:
                            ticker_info = self.bot.session.get_tickers(category="linear", symbol=symbol)
                            if ticker_info.get('retCode') == 0:
                                last_price = float(ticker_info.get('result', {}).get('list', [{}])[0].get('lastPrice', 0))

                                # Determine if it likely hit TP or SL
                                if side == "Buy":
                                    if tp > 0 and abs(last_price - tp) / tp < 0.01:  # Within 1% of TP
                                        exit_type = "Take Profit"
                                    elif sl > 0 and abs(last_price - sl) / sl < 0.01:  # Within 1% of SL
                                        exit_type = "Stop Loss"
                                else:  # Sell
                                    if tp > 0 and abs(last_price - tp) / tp < 0.01:  # Within 1% of TP
                                        exit_type = "Take Profit"
                                    elif sl > 0 and abs(last_price - sl) / sl < 0.01:  # Within 1% of SL
                                        exit_type = "Stop Loss"
                    except Exception as e:
                        logging.error(f"Error getting ticker info: {e}")

                    # Ensure we have valid values for entry_price and size
                    entry_price = last_pos.get('entryPrice', last_pos.get('avgPrice'))
                    if entry_price is None or entry_price == "":
                        entry_price = "0.0"

                    size = last_pos.get('size', last_pos.get('qty'))
                    if size is None or size == "":
                        size = "0.0"

                    # Calculate PnL percentage
                    pnl_percent = 0.0
                    try:
                        entry_price_float = float(entry_price)
                        exit_price_float = float(last_pos.get('markPrice', 0))
                        if entry_price_float > 0:
                            if side == "Buy":
                                pnl_percent = (exit_price_float - entry_price_float) / entry_price_float * 100
                            else:  # Sell
                                pnl_percent = (entry_price_float - exit_price_float) / entry_price_float * 100
                    except Exception as e:
                        logging.error(f"Error calculating PnL percentage: {e}")

                    # Add to trade history
                    trade_data = {
                        "symbol": symbol,
                        "side": "Sell" if side == "Buy" else "Buy",  # Opposite of position side
                        "entry_price": entry_price,
                        "exit_price": last_pos.get('markPrice'),  # Use mark price as exit price
                        "size": size,
                        "pnl": last_pos.get('unrealisedPnl', "0"),
                        "pnl_percent": pnl_percent,
                        "exit_type": exit_type,
                        "duration": "N/A",  # We don't have duration info for externally closed positions
                        "timestamp": datetime.datetime.now().isoformat()
                    }
                    self.add_trade_to_history(trade_data)
                    logging.info(f"Added closed position to history: {symbol} (Exit: {exit_type})")

            # Save current positions as last known positions
            with open(last_positions_file, "w") as f:
                json.dump(current_positions, f, indent=2)

            logging.info(f"Saved {len(current_positions)} positions as last known positions")
        except Exception as e:
            logging.error(f"Error checking closed positions: {e}", exc_info=True)

    # --- Performance Analytics ---
    def load_trade_history(self):
        """Load trade history from file"""
        try:
            history_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "trade_history.json")
            if os.path.exists(history_file):
                with open(history_file, "r") as f:
                    self.trade_history = json.load(f)
                logging.info(f"Loaded {len(self.trade_history)} trades from history file")

                # Calculate equity curve
                self.calculate_equity_curve()
            else:
                logging.info("No trade history file found. Starting with empty history.")
        except Exception as e:
            logging.error(f"Error loading trade history: {e}", exc_info=True)
            self.trade_history = []

    def save_trade_history(self):
        """Save trade history to file"""
        try:
            history_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "trade_history.json")
            with open(history_file, "w") as f:
                json.dump(self.trade_history, f, indent=2)
            logging.info(f"Saved {len(self.trade_history)} trades to history file")
        except Exception as e:
            logging.error(f"Error saving trade history: {e}", exc_info=True)

    def add_trade_to_history(self, trade_data):
        """Add a completed trade to history"""
        try:
            # Add timestamp if not present
            if "timestamp" not in trade_data:
                trade_data["timestamp"] = datetime.datetime.now().isoformat()

            # Ensure all required fields are present and valid
            required_fields = {
                "symbol": "Unknown",
                "side": "Unknown",
                "entry_price": "0.0",
                "exit_price": "0.0",
                "size": "0.0",
                "pnl": "0.0",
                "exit_type": "Unknown"
            }

            for field, default_value in required_fields.items():
                if field not in trade_data or trade_data[field] is None or trade_data[field] == "":
                    trade_data[field] = default_value
                    logging.warning(f"Missing {field} in trade data, using default: {default_value}")

            # Format numeric values to ensure they're displayed properly
            try:
                if isinstance(trade_data["entry_price"], (int, float)):
                    trade_data["entry_price"] = f"{float(trade_data['entry_price']):.8f}"
                if isinstance(trade_data["exit_price"], (int, float)):
                    trade_data["exit_price"] = f"{float(trade_data['exit_price']):.8f}"
                if isinstance(trade_data["size"], (int, float)):
                    trade_data["size"] = f"{float(trade_data['size']):.8f}"
                if isinstance(trade_data["pnl"], (int, float)):
                    trade_data["pnl"] = f"{float(trade_data['pnl']):.8f}"
            except Exception as e:
                logging.error(f"Error formatting trade data values: {e}")

            # Add the trade to history
            self.trade_history.append(trade_data)

            # Save updated history
            self.save_trade_history()

            # Update the performance metrics
            self.refresh_trade_history()

            logging.info(f"Added trade to history: {trade_data['symbol']} {trade_data['side']} P&L: {trade_data.get('pnl', 'N/A')}")
        except Exception as e:
            logging.error(f"Error adding trade to history: {e}", exc_info=True)

    def set_history_timeframe(self, sender=None, app_data=None):
        """Set the timeframe for filtering trade history"""
        self.history_timeframe = app_data
        logging.info(f"History timeframe set to: {self.history_timeframe}")
        self.refresh_trade_history()

    def filter_history_by_timeframe(self):
        """Filter trade history based on selected timeframe"""
        try:
            now = datetime.datetime.now()

            if self.history_timeframe == "All Time":
                self.filtered_history = self.trade_history.copy()
            elif self.history_timeframe == "This Month":
                month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                self.filtered_history = [trade for trade in self.trade_history
                                        if datetime.datetime.fromisoformat(trade["timestamp"]) >= month_start]
            elif self.history_timeframe == "This Week":
                week_start = now - datetime.timedelta(days=now.weekday())
                week_start = week_start.replace(hour=0, minute=0, second=0, microsecond=0)
                self.filtered_history = [trade for trade in self.trade_history
                                        if datetime.datetime.fromisoformat(trade["timestamp"]) >= week_start]
            elif self.history_timeframe == "Today":
                day_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
                self.filtered_history = [trade for trade in self.trade_history
                                        if datetime.datetime.fromisoformat(trade["timestamp"]) >= day_start]
            else:
                self.filtered_history = self.trade_history.copy()

            logging.info(f"Filtered history to {len(self.filtered_history)} trades for timeframe: {self.history_timeframe}")
        except Exception as e:
            logging.error(f"Error filtering history: {e}", exc_info=True)
            self.filtered_history = self.trade_history.copy()

    def calculate_performance_metrics(self):
        """Calculate performance metrics based on filtered history"""
        try:
            metrics = {
                "total_trades": len(self.filtered_history),
                "winning_trades": 0,
                "losing_trades": 0,
                "total_profit": 0.0,
                "total_loss": 0.0,
                "largest_win": 0.0,
                "largest_loss": 0.0,
                "avg_win": 0.0,
                "avg_loss": 0.0,
                "win_rate": 0.0,
                "profit_factor": 0.0,
                "net_pl": 0.0
            }

            # Calculate basic metrics
            for trade in self.filtered_history:
                pnl = float(trade.get("pnl", 0.0))
                metrics["net_pl"] += pnl

                if pnl > 0:
                    metrics["winning_trades"] += 1
                    metrics["total_profit"] += pnl
                    metrics["largest_win"] = max(metrics["largest_win"], pnl)
                elif pnl < 0:
                    metrics["losing_trades"] += 1
                    metrics["total_loss"] += abs(pnl)
                    metrics["largest_loss"] = max(metrics["largest_loss"], abs(pnl))

            # Calculate derived metrics
            if metrics["winning_trades"] > 0:
                metrics["avg_win"] = metrics["total_profit"] / metrics["winning_trades"]

            if metrics["losing_trades"] > 0:
                metrics["avg_loss"] = metrics["total_loss"] / metrics["losing_trades"]

            if metrics["total_trades"] > 0:
                metrics["win_rate"] = (metrics["winning_trades"] / metrics["total_trades"]) * 100

            if metrics["total_loss"] > 0:
                metrics["profit_factor"] = metrics["total_profit"] / metrics["total_loss"]
            elif metrics["total_profit"] > 0:
                metrics["profit_factor"] = float('inf')  # Infinite profit factor if no losses

            return metrics
        except Exception as e:
            logging.error(f"Error calculating performance metrics: {e}", exc_info=True)
            return {
                "total_trades": 0,
                "winning_trades": 0,
                "losing_trades": 0,
                "total_profit": 0.0,
                "total_loss": 0.0,
                "largest_win": 0.0,
                "largest_loss": 0.0,
                "avg_win": 0.0,
                "avg_loss": 0.0,
                "win_rate": 0.0,
                "profit_factor": 0.0,
                "net_pl": 0.0
            }

    def calculate_equity_curve(self):
        """Calculate equity curve based on trade history"""
        try:
            # Start with initial balance of 0
            equity = [0]

            # Sort trades by timestamp
            sorted_trades = sorted(self.trade_history, key=lambda x: x.get("timestamp", ""))

            # Calculate cumulative equity
            for trade in sorted_trades:
                pnl = float(trade.get("pnl", 0.0))
                equity.append(equity[-1] + pnl)

            self.equity_curve = equity
            logging.info(f"Calculated equity curve with {len(equity)} points")
        except Exception as e:
            logging.error(f"Error calculating equity curve: {e}", exc_info=True)
            self.equity_curve = [0]

    def update_performance_display(self, metrics):
        """Update the performance metrics display"""
        try:
            # Update metrics in the UI
            if dpg.does_item_exist("metric_total_trades"):
                dpg.set_value("metric_total_trades", str(metrics["total_trades"]))

            if dpg.does_item_exist("metric_win_rate"):
                dpg.set_value("metric_win_rate", f"{metrics['win_rate']:.2f}%")

            if dpg.does_item_exist("metric_profit_factor"):
                if metrics["profit_factor"] == float('inf'):
                    dpg.set_value("metric_profit_factor", "∞")
                else:
                    dpg.set_value("metric_profit_factor", f"{metrics['profit_factor']:.2f}")

            if dpg.does_item_exist("metric_avg_win"):
                dpg.set_value("metric_avg_win", f"${metrics['avg_win']:.2f}")

            if dpg.does_item_exist("metric_avg_loss"):
                dpg.set_value("metric_avg_loss", f"${metrics['avg_loss']:.2f}")

            if dpg.does_item_exist("metric_largest_win"):
                dpg.set_value("metric_largest_win", f"${metrics['largest_win']:.2f}")

            if dpg.does_item_exist("metric_largest_loss"):
                dpg.set_value("metric_largest_loss", f"${metrics['largest_loss']:.2f}")

            if dpg.does_item_exist("metric_net_pl"):
                net_pl = metrics["net_pl"]
                color = self.theme["success"] if net_pl >= 0 else self.theme["error"]
                dpg.set_value("metric_net_pl", f"${net_pl:.2f}")
                dpg.configure_item("metric_net_pl", color=color)

            # Update equity curve
            if dpg.does_item_exist("equity_series") and len(self.equity_curve) > 0:
                x_values = list(range(len(self.equity_curve)))
                dpg.set_value("equity_series", [x_values, self.equity_curve])

                # Update axis limits
                if dpg.does_item_exist("equity_x_axis"):
                    dpg.set_axis_limits("equity_x_axis", 0, max(1, len(self.equity_curve) - 1))

                if dpg.does_item_exist("equity_y_axis"):
                    min_equity = min(self.equity_curve)
                    max_equity = max(self.equity_curve)
                    padding = max(1, (max_equity - min_equity) * 0.1)  # 10% padding
                    dpg.set_axis_limits("equity_y_axis", min_equity - padding, max_equity + padding)
        except Exception as e:
            logging.error(f"Error updating performance display: {e}", exc_info=True)

    def update_trade_history_table(self):
        """Update the trade history table with filtered history"""
        try:
            # Clear existing rows
            if dpg.does_item_exist("trade_history_table"):
                children = dpg.get_item_children("trade_history_table", 1)
                if children and len(children) > 0:
                    for child in children:
                        try:
                            dpg.delete_item(child)
                        except Exception as e:
                            logging.warning(f"Error deleting child {child}: {e}")

            # Add new rows
            for i, trade in enumerate(reversed(self.filtered_history)):  # Show newest first
                try:
                    # Parse trade data
                    timestamp = datetime.datetime.fromisoformat(trade.get("timestamp", "")).strftime("%Y-%m-%d %H:%M:%S")
                    symbol = trade.get("symbol", "N/A")
                    side = trade.get("side", "N/A")
                    size = trade.get("size", "N/A")
                    entry_price = trade.get("entry_price", "N/A")
                    exit_price = trade.get("exit_price", "N/A")
                    pnl = float(trade.get("pnl", 0.0))
                    pnl_percent = float(trade.get("pnl_percent", 0.0))
                    duration = trade.get("duration", "N/A")

                    # Determine color based on P&L
                    pnl_color = self.theme["success"] if pnl >= 0 else self.theme["error"]

                    # Add row to table
                    with dpg.table_row(parent="trade_history_table", tag=f"history_row_{i}"):
                        dpg.add_text(timestamp)
                        dpg.add_text(symbol)
                        dpg.add_text(side, color=self.theme["success"] if side == "Buy" else self.theme["error"])
                        dpg.add_text(str(size))

                        # Format entry price with max 8 decimals
                        try:
                            entry_price_float = float(entry_price)
                            formatted_entry = f"{entry_price_float:.8f}".rstrip('0').rstrip('.') if '.' in f"{entry_price_float:.8f}" else f"{entry_price_float:.8f}"
                            dpg.add_text(formatted_entry)
                        except (ValueError, TypeError):
                            dpg.add_text(str(entry_price))

                        # Format exit price with max 8 decimals
                        try:
                            exit_price_float = float(exit_price)
                            formatted_exit = f"{exit_price_float:.8f}".rstrip('0').rstrip('.') if '.' in f"{exit_price_float:.8f}" else f"{exit_price_float:.8f}"
                            dpg.add_text(formatted_exit)
                        except (ValueError, TypeError):
                            dpg.add_text(str(exit_price))
                        dpg.add_text(f"${pnl:.2f}", color=pnl_color)
                        dpg.add_text(f"{pnl_percent:.2f}%", color=pnl_color)
                        dpg.add_text(duration)
                except Exception as e:
                    logging.error(f"Error adding trade row: {e}", exc_info=True)
        except Exception as e:
            logging.error(f"Error updating trade history table: {e}", exc_info=True)

    def refresh_trade_history(self, sender=None, app_data=None):
        """Refresh the trade history display"""
        try:
            # Update status
            if dpg.does_item_exist("performance_status_text"):
                dpg.set_value("performance_status_text", "Performance Status: Refreshing...")

            # Filter history based on timeframe
            self.filter_history_by_timeframe()

            # Calculate performance metrics
            metrics = self.calculate_performance_metrics()

            # Update the equity curve
            self.calculate_equity_curve()

            # Update the UI
            self.update_performance_display(metrics)
            self.update_trade_history_table()

            # Update status
            if dpg.does_item_exist("performance_status_text"):
                timestamp = datetime.datetime.now().strftime("%H:%M:%S")
                dpg.set_value("performance_status_text", f"Performance Status: Updated at {timestamp}")

            return True
        except Exception as e:
            logging.error(f"Error refreshing trade history: {e}", exc_info=True)
            if dpg.does_item_exist("performance_status_text"):
                dpg.set_value("performance_status_text", f"Performance Status: Error - {str(e)}")
            return False

    # --- User Experience Improvements ---
    def toggle_theme(self):
        """Toggle between dark and light themes"""
        try:
            # Toggle theme
            self.current_theme = "light" if self.current_theme == "dark" else "dark"
            self.theme = self.themes[self.current_theme]

            # Apply theme to viewport
            if self.current_theme == "dark":
                dpg.configure_app(docking=True, docking_space=True, init_file="", load_init_file=False)
                dpg.set_viewport_clear_color(self.theme['background'])
            else:
                dpg.configure_app(docking=True, docking_space=True, init_file="", load_init_file=False)
                dpg.set_viewport_clear_color(self.theme['background'])

            logging.info(f"Theme changed to {self.current_theme}")

            # Show confirmation message
            with dpg.window(label="Theme Changed", modal=True, width=300, height=100, pos=[400, 200]):
                dpg.add_text(f"Theme changed to {self.current_theme.capitalize()} Mode")
                dpg.add_button(label="OK", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))
        except Exception as e:
            logging.error(f"Error toggling theme: {e}", exc_info=True)

    def save_settings(self, sender=None, app_data=None):
        """Save application settings to file"""
        try:
            # Get position size from input field
            position_size = 100.0  # Default value
            if dpg.does_item_exist("position_size_input"):
                position_size = dpg.get_value("position_size_input")

            settings = {
                "theme": self.current_theme,
                "timeframe": self.timeframe,
                "auto_refresh": self.live_scan,
                "refresh_interval": dpg.get_value("refresh_interval") if dpg.does_item_exist("refresh_interval") else 60,
                "compact_mode": dpg.get_item_configuration("compact_mode")["check"] if dpg.does_item_exist("compact_mode") else False,
                "custom_symbols": self.custom_symbols,
                "position_size": position_size  # Add position size to settings
            }

            settings_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "settings.json")
            with open(settings_file, "w") as f:
                json.dump(settings, f, indent=2)

            logging.info(f"Settings saved successfully. Position size set to ${position_size}")

            # Update status text
            if dpg.does_item_exist("settings_status_text"):
                dpg.set_value("settings_status_text", f"Settings saved successfully! Position size: ${position_size}")
                dpg.configure_item("settings_status_text", color=self.theme['success'])

            # Store position size in instance variable for immediate use
            self.position_size = position_size

            # Show confirmation message
            with dpg.window(label="Settings Saved", modal=True, width=300, height=100, pos=[400, 200]):
                dpg.add_text("Settings saved successfully")
                dpg.add_button(label="OK", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))
        except Exception as e:
            logging.error(f"Error saving settings: {e}", exc_info=True)

            # Update status text
            if dpg.does_item_exist("settings_status_text"):
                dpg.set_value("settings_status_text", f"Error: {str(e)}")
                dpg.configure_item("settings_status_text", color=self.theme['error'])

            # Show error message
            with dpg.window(label="Error", modal=True, width=400, height=150, pos=[400, 200]):
                dpg.add_text(f"Failed to save settings: {str(e)}")
                dpg.add_button(label="OK", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))

    def load_settings(self):
        """Load application settings from file"""
        try:
            settings_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "settings.json")
            if os.path.exists(settings_file):
                with open(settings_file, "r") as f:
                    settings = json.load(f)

                # Apply settings
                if "theme" in settings:
                    self.current_theme = settings["theme"]
                    self.theme = self.themes[self.current_theme]
                    dpg.set_viewport_clear_color(self.theme['background'])

                if "timeframe" in settings:
                    self.timeframe = settings["timeframe"]
                    if dpg.does_item_exist("timeframe_combo"):
                        dpg.set_value("timeframe_combo", self.timeframe)

                if "auto_refresh" in settings:
                    self.live_scan = settings["auto_refresh"]
                    if dpg.does_item_exist("auto_refresh_cb"):
                        dpg.set_value("auto_refresh_cb", self.live_scan)

                if "refresh_interval" in settings:
                    if dpg.does_item_exist("refresh_interval"):
                        dpg.set_value("refresh_interval", settings["refresh_interval"])

                if "compact_mode" in settings:
                    if dpg.does_item_exist("compact_mode"):
                        dpg.configure_item("compact_mode", check=settings["compact_mode"])
                        self.toggle_compact_mode(None, settings["compact_mode"])

                if "custom_symbols" in settings:
                    self.custom_symbols = settings["custom_symbols"]
                    if dpg.does_item_exist("symbol_input"):
                        dpg.set_value("symbol_input", ",".join(self.custom_symbols))

                # Load position size setting
                if "position_size" in settings:
                    self.position_size = float(settings["position_size"])
                    if dpg.does_item_exist("position_size_input"):
                        dpg.set_value("position_size_input", self.position_size)
                    logging.info(f"Loaded position size: ${self.position_size}")
                else:
                    # Default position size if not in settings
                    self.position_size = 100.0

                logging.info("Settings loaded successfully")

                # Show confirmation message
                with dpg.window(label="Settings Loaded", modal=True, width=300, height=100, pos=[400, 200]):
                    dpg.add_text("Settings loaded successfully")
                    dpg.add_button(label="OK", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))
            else:
                logging.info("No settings file found. Using defaults.")

                # Show message
                with dpg.window(label="Settings", modal=True, width=300, height=100, pos=[400, 200]):
                    dpg.add_text("No settings file found. Using defaults.")
                    dpg.add_button(label="OK", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))
        except Exception as e:
            logging.error(f"Error loading settings: {e}", exc_info=True)

            # Show error message
            with dpg.window(label="Error", modal=True, width=400, height=150, pos=[400, 200]):
                dpg.add_text(f"Failed to load settings: {str(e)}")
                dpg.add_button(label="OK", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))

    def reset_layout(self):
        """Reset the UI layout to default"""
        try:
            # Ask for confirmation
            with dpg.window(label="Reset Layout", modal=True, width=400, height=150, pos=[400, 200]):
                dpg.add_text("Are you sure you want to reset the layout to default?")
                with dpg.group(horizontal=True):
                    dpg.add_button(label="Cancel", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))
                    dpg.add_button(label="Reset", width=100, callback=self.confirm_reset_layout)
        except Exception as e:
            logging.error(f"Error resetting layout: {e}", exc_info=True)

    def confirm_reset_layout(self):
        """Confirm and execute layout reset"""
        try:
            # Close the confirmation dialog
            dpg.delete_item(dpg.last_container())

            # Reset layout
            # This is a placeholder - in a real implementation, you would reset all UI elements
            # to their default positions and sizes

            logging.info("Layout reset to default")

            # Show confirmation message
            with dpg.window(label="Layout Reset", modal=True, width=300, height=100, pos=[400, 200]):
                dpg.add_text("Layout has been reset to default")
                dpg.add_button(label="OK", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))
        except Exception as e:
            logging.error(f"Error confirming layout reset: {e}", exc_info=True)

    def toggle_compact_mode(self, sender=None, app_data=None):
        """Toggle compact mode for the UI"""
        try:
            is_compact = app_data if app_data is not None else not getattr(self, "compact_mode", False)
            self.compact_mode = is_compact

            # Apply compact mode changes
            if is_compact:
                # Reduce spacing and sizes for compact mode
                if dpg.does_item_exist("main_window"):
                    dpg.configure_item("main_window", width=1200, height=700)
            else:
                # Restore normal spacing and sizes
                if dpg.does_item_exist("main_window"):
                    dpg.configure_item("main_window", width=1600, height=900)

            logging.info(f"Compact mode {'enabled' if is_compact else 'disabled'}")
        except Exception as e:
            logging.error(f"Error toggling compact mode: {e}", exc_info=True)

    def clear_cache(self):
        """Clear application cache"""
        try:
            # Ask for confirmation
            with dpg.window(label="Clear Cache", modal=True, width=400, height=150, pos=[400, 200]):
                dpg.add_text("Are you sure you want to clear the application cache?")
                with dpg.group(horizontal=True):
                    dpg.add_button(label="Cancel", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))
                    dpg.add_button(label="Clear", width=100, callback=self.confirm_clear_cache)
        except Exception as e:
            logging.error(f"Error clearing cache: {e}", exc_info=True)

    def confirm_clear_cache(self):
        """Confirm and execute cache clearing"""
        try:
            # Close the confirmation dialog
            dpg.delete_item(dpg.last_container())

            # Clear cache
            self.bot.data_cache.clear()
            self.all_live_results.clear()

            logging.info("Cache cleared successfully")

            # Show confirmation message
            with dpg.window(label="Cache Cleared", modal=True, width=300, height=100, pos=[400, 200]):
                dpg.add_text("Cache has been cleared successfully")
                dpg.add_button(label="OK", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))
        except Exception as e:
            logging.error(f"Error confirming cache clear: {e}", exc_info=True)

    def export_data(self):
        """Export application data"""
        try:
            # Create export options dialog
            with dpg.window(label="Export Data", modal=True, width=400, height=250, pos=[400, 200]):
                dpg.add_text("Select data to export:")
                dpg.add_checkbox(label="Trade History", default_value=True, tag="export_trade_history")
                dpg.add_checkbox(label="Positions", default_value=True, tag="export_positions")
                dpg.add_checkbox(label="Signals", default_value=True, tag="export_signals")
                dpg.add_spacer(height=10)
                dpg.add_text("Export format:")
                dpg.add_radio_button(["JSON", "CSV"], default_value="JSON", tag="export_format")
                dpg.add_spacer(height=10)
                with dpg.group(horizontal=True):
                    dpg.add_button(label="Cancel", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))
                    dpg.add_button(label="Export", width=100, callback=self.confirm_export_data)
        except Exception as e:
            logging.error(f"Error exporting data: {e}", exc_info=True)

    def confirm_export_data(self):
        """Confirm and execute data export"""
        try:
            # Get export options
            export_trade_history = dpg.get_value("export_trade_history")
            export_positions = dpg.get_value("export_positions")
            export_signals = dpg.get_value("export_signals")
            export_format = dpg.get_value("export_format")

            # Close the export dialog
            dpg.delete_item(dpg.last_container())

            # Create export directory if it doesn't exist
            export_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "exports")
            os.makedirs(export_dir, exist_ok=True)

            # Generate timestamp for filenames
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            # Export data based on selected options
            exported_files = []

            if export_trade_history:
                if export_format == "JSON":
                    filename = os.path.join(export_dir, f"trade_history_{timestamp}.json")
                    with open(filename, "w") as f:
                        json.dump(self.trade_history, f, indent=2)
                else:  # CSV
                    filename = os.path.join(export_dir, f"trade_history_{timestamp}.csv")
                    with open(filename, "w") as f:
                        # Write header
                        f.write("timestamp,symbol,side,size,entry_price,exit_price,pnl,pnl_percent,duration\n")
                        # Write data
                        for trade in self.trade_history:
                            f.write(f"{trade.get('timestamp', '')},{trade.get('symbol', '')},{trade.get('side', '')},{trade.get('size', '')},{trade.get('entry_price', '')},{trade.get('exit_price', '')},{trade.get('pnl', '')},{trade.get('pnl_percent', '')},{trade.get('duration', '')}\n")
                exported_files.append(filename)

            if export_positions:
                if export_format == "JSON":
                    filename = os.path.join(export_dir, f"positions_{timestamp}.json")
                    with open(filename, "w") as f:
                        json.dump(self.positions_data, f, indent=2)
                else:  # CSV
                    filename = os.path.join(export_dir, f"positions_{timestamp}.csv")
                    with open(filename, "w") as f:
                        # Write header
                        f.write("symbol,side,size,entry_price,current_price,pnl,take_profit,stop_loss\n")
                        # Write data
                        for pos in self.positions_data:
                            f.write(f"{pos.get('symbol', '')},{pos.get('side', '')},{pos.get('size', '')},{pos.get('avgPrice', '')},{pos.get('markPrice', '')},{pos.get('unrealisedPnl', '')},{pos.get('takeProfit', '')},{pos.get('stopLoss', '')}\n")
                exported_files.append(filename)

            if export_signals:
                if export_format == "JSON":
                    filename = os.path.join(export_dir, f"signals_{timestamp}.json")
                    with open(filename, "w") as f:
                        json.dump(self.all_live_results, f, indent=2)
                else:  # CSV
                    filename = os.path.join(export_dir, f"signals_{timestamp}.csv")
                    with open(filename, "w") as f:
                        # Write header
                        f.write("symbol,timeframe,signal,confidence,price\n")
                        # Write data
                        for symbol, data in self.all_live_results.items():
                            f.write(f"{symbol},{data.get('timeframe', '')},{data.get('signal', '')},{data.get('confidence', '')},{data.get('price', '')}\n")
                exported_files.append(filename)

            logging.info(f"Data exported successfully to {len(exported_files)} files")

            # Show confirmation message with list of exported files
            with dpg.window(label="Data Exported", modal=True, width=500, height=200, pos=[400, 200]):
                dpg.add_text(f"Data exported successfully to {len(exported_files)} files:")
                for filename in exported_files:
                    dpg.add_text(os.path.basename(filename))
                dpg.add_text(f"\nFiles saved to: {export_dir}")
                dpg.add_button(label="OK", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))
        except Exception as e:
            logging.error(f"Error confirming data export: {e}", exc_info=True)

            # Show error message
            with dpg.window(label="Error", modal=True, width=400, height=150, pos=[400, 200]):
                dpg.add_text(f"Failed to export data: {str(e)}")
                dpg.add_button(label="OK", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))

    def show_shortcuts(self):
        """Show keyboard shortcuts help"""
        try:
            with dpg.window(label="Keyboard Shortcuts", modal=True, width=500, height=400, pos=[400, 200]):
                dpg.add_text("Keyboard Shortcuts", color=(255, 255, 0))
                dpg.add_spacer(height=10)

                # Add shortcuts table
                with dpg.table(header_row=True, borders_innerH=True, borders_outerH=True, borders_innerV=True, borders_outerV=True):
                    dpg.add_table_column(label="Shortcut")
                    dpg.add_table_column(label="Description")

                    # Add shortcut rows
                    with dpg.table_row():
                        dpg.add_text("Ctrl+R")
                        dpg.add_text("Refresh signals")

                    with dpg.table_row():
                        dpg.add_text("Ctrl+S")
                        dpg.add_text("Save settings")

                    with dpg.table_row():
                        dpg.add_text("Ctrl+L")
                        dpg.add_text("Load settings")

                    with dpg.table_row():
                        dpg.add_text("Ctrl+T")
                        dpg.add_text("Toggle theme")

                    with dpg.table_row():
                        dpg.add_text("Ctrl+P")
                        dpg.add_text("Refresh positions")

                    with dpg.table_row():
                        dpg.add_text("Ctrl+H")
                        dpg.add_text("Refresh trade history")

                    with dpg.table_row():
                        dpg.add_text("Ctrl+C")
                        dpg.add_text("Clear cache")

                    with dpg.table_row():
                        dpg.add_text("Ctrl+E")
                        dpg.add_text("Export data")

                    with dpg.table_row():
                        dpg.add_text("F1")
                        dpg.add_text("Show this help")

                    with dpg.table_row():
                        dpg.add_text("Esc")
                        dpg.add_text("Close popup windows")

                dpg.add_spacer(height=10)
                dpg.add_button(label="OK", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))
        except Exception as e:
            logging.error(f"Error showing shortcuts: {e}", exc_info=True)

    def show_about(self):
        """Show about dialog"""
        try:
            with dpg.window(label="About SignalCheck Pro", modal=True, width=500, height=300, pos=[400, 200]):
                dpg.add_text("SignalCheck Pro", color=(255, 255, 0))
                dpg.add_text("Version 1.0.0")
                dpg.add_spacer(height=10)
                dpg.add_text("A professional trading signal scanner and position management tool.")
                dpg.add_spacer(height=10)
                dpg.add_text("Features:")
                dpg.add_text("- Advanced signal scanning")
                dpg.add_text("- Position management")
                dpg.add_text("- Performance analytics")
                dpg.add_text("- Risk management")
                dpg.add_text("- Customizable UI")
                dpg.add_spacer(height=10)
                dpg.add_text("© 2023 SignalCheck Pro. All rights reserved.")
                dpg.add_spacer(height=10)
                dpg.add_button(label="OK", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))
        except Exception as e:
            logging.error(f"Error showing about dialog: {e}", exc_info=True)

    # --- Telegram Integration and Trading Execution ---
    def on_send_button_click(self, sender=None, app_data=None, user_data=None):
        if user_data: logging.info(f"Send btn for: {user_data}"); self.send_to_telegram(user_data)

    def on_buy_button_click(self, sender=None, app_data=None, user_data=None):
        if user_data:
            logging.info(f"Buy btn for: {user_data}")
            self.execute_trade(user_data)

    def execute_trade(self, symbol: str):
        # Update status immediately to show we're processing
        if dpg.does_item_exist("status_text"):
            dpg.set_value("status_text", f"Status: Processing trade for {symbol}...")

        result = self.all_live_results.get(symbol)
        # Check if we have valid result data
        if not isinstance(result, dict):
            error_msg = f"No valid result data for {symbol} for trading."
            logging.warning(error_msg)
            if dpg.does_item_exist("status_text"):
                dpg.set_value("status_text", f"Status: {error_msg}")
            return

        # Get the trade plan with entry, SL, TP
        trade_plan = result.get('trade_plan')
        if not isinstance(trade_plan, dict):
            error_msg = f"No valid trade plan for {symbol}."
            logging.warning(error_msg)
            if dpg.does_item_exist("status_text"):
                dpg.set_value("status_text", f"Status: {error_msg}")
            return

        # Get the signal (BUY/SELL)
        signal = result.get('signal')
        if signal not in ['BUY', 'SELL']:
            error_msg = f"Invalid signal for {symbol}: {signal}"
            logging.warning(error_msg)
            if dpg.does_item_exist("status_text"):
                dpg.set_value("status_text", f"Status: {error_msg}")
            return

        # Map our signal to Bybit's side format
        side = 'Buy' if signal == 'BUY' else 'Sell'

        # Get entry, SL, TP from trade plan
        entry_zone = trade_plan.get('entry_zone', (0, 0))
        if not isinstance(entry_zone, tuple) or len(entry_zone) != 2:
            error_msg = f"Invalid entry zone for {symbol}: {entry_zone}"
            logging.warning(error_msg)
            if dpg.does_item_exist("status_text"):
                dpg.set_value("status_text", f"Status: {error_msg}")
            return

        # Use the middle of the entry zone as the entry price
        entry_price = sum(entry_zone) / 2
        stop_loss = trade_plan.get('stop_loss', 0)
        take_profit = trade_plan.get('take_profit', 0)

        if entry_price <= 0 or stop_loss <= 0 or take_profit <= 0:
            error_msg = f"Invalid prices for {symbol}: Entry={entry_price}, SL={stop_loss}, TP={take_profit}"
            logging.warning(error_msg)
            if dpg.does_item_exist("status_text"):
                dpg.set_value("status_text", f"Status: {error_msg}")
            return

        # Update status to show we're placing the order
        if dpg.does_item_exist("status_text"):
            dpg.set_value("status_text", f"Status: Placing {side} order for {symbol}...")

        # Execute the trade with 10x leverage and $100 position
        order_result = self.bot.place_order(
            symbol=symbol,
            side=side,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            leverage=10,  # 10x leverage as requested
            amount_usd=100.0  # $100 position as requested
        )

        # Show result to user
        if order_result.get('success', False):
            success_msg = f"Trade executed for {symbol}:\n" \
                         f"Side: {side}\n" \
                         f"Entry: {entry_price:.4f}\n" \
                         f"SL: {stop_loss:.4f}\n" \
                         f"TP: {take_profit:.4f}\n" \
                         f"Leverage: 10x\n" \
                         f"Position: $100 (Cost: $10)\n" \
                         f"Order ID: {order_result.get('order_id', 'N/A')}"
            logging.info(success_msg)
            if dpg.does_item_exist("status_text"):
                dpg.set_value("status_text", f"Status: Trade executed for {symbol}")
        else:
            error_msg = f"Failed to execute trade for {symbol}: {order_result.get('message', 'Unknown error')}"
            logging.error(error_msg)
            if dpg.does_item_exist("status_text"):
                dpg.set_value("status_text", f"Status: Trade failed for {symbol}")

    def send_to_telegram(self, symbol: str):
        result = self.all_live_results.get(symbol)
        # Added check for None result, which might be stored now
        if not isinstance(result, dict): logging.warning(f"No valid result data for {symbol} for Telegram."); return
        if not self.TELEGRAM_TOKEN or not self.TELEGRAM_CHAT_ID: logging.error("Telegram creds missing."); return
        try:
            vals = result.get('values', ())
            if len(vals) != len(self.columns_config): logging.error(f"TG Vals/Cols mismatch for {symbol}"); return
            labels=[cfg[0] for cfg in self.columns_config]
            try:
                sig_i=labels.index('Signal'); conf_i=labels.index('Conf.'); price_i=labels.index('Price'); zone_i=labels.index('Entry Zone')
                sl_i=labels.index('Stop Loss'); tp_i=labels.index('Take Profit'); chg_i=labels.index('Chg.'); trend_i=labels.index('Trend')
                st_i=labels.index('STrend'); mom_i=labels.index('Momentum'); vol_i=labels.index('Volume'); div_i=labels.index('Div.')
                mkt_i=labels.index('Market'); htf_i=labels.index('HTF'); fund_i=labels.index('Funding'); oi_i=labels.index('OI Trend')
                sent_i=labels.index('Sent.'); wave_i=labels.index('Wave')
            except ValueError as e: logging.error(f"TG: Error finding column index: {e}"); return

            sig=html.escape(vals[sig_i]); conf=html.escape(vals[conf_i]); price=html.escape(vals[price_i]); zone=html.escape(vals[zone_i])
            sl=html.escape(vals[sl_i]); tp=html.escape(vals[tp_i]); chg=html.escape(vals[chg_i]); trend=html.escape(vals[trend_i])
            st=html.escape(vals[st_i]); mom=html.escape(vals[mom_i]); vol=html.escape(vals[vol_i]); div=html.escape(vals[div_i])
            market=html.escape(vals[mkt_i]); htf=html.escape(vals[htf_i]); fund=html.escape(vals[fund_i]); oi=html.escape(vals[oi_i])
            sent=html.escape(vals[sent_i]); wave=html.escape(vals[wave_i]); sym_esc=html.escape(symbol); tf_esc=html.escape(self.timeframe)
            emoji = "🟢" if sig=="BUY" else ("🔴" if sig=="SELL" else "⚪")
            plan_method = result.get('trade_plan',{}).get('method','N/A') # Safely get method

            message = ( f"<b>⚡ Signal | {sym_esc} | {tf_esc} ⚡</b>\n\n"
                        f"<b>{emoji} {sig} </b> @ <code>{price}</code>\n"
                        f"<i>Confidence:</i> {conf}\n" f"<i>Change:</i> <code>{chg}</code>\n\n"
                        f"<b><u>Trade Plan ({plan_method}):</u></b>\n"
                        f"  Entry Zone: <code>{zone}</code>\n" f"  SL: <code>{sl}</code>\n" f"  TP: <code>{tp}</code>\n\n"
                        f"<b><u>Context:</u></b>\n" f"  Trend: {trend} | <b>STrend: {st}</b>\n" f"  Mom: {mom} | Vol: {vol}\n"
                        f"  Div: {div} | Market: {market}\n" f"  HTF: {htf}\n"
                        f"<b><u>Sentiment:</u></b>\n" f"  Funding: <code>{fund}</code> | OI: {oi}\n" f"  Confirm: {sent} | Wave: {wave}" )

            url=f"https://api.telegram.org/bot{self.TELEGRAM_TOKEN}/sendMessage"; payload={'chat_id': self.TELEGRAM_CHAT_ID, 'text': message, 'parse_mode': 'HTML'}
            response = requests.post(url, json=payload, timeout=10)
            if response.status_code==200: logging.info(f"Sent TG msg for {symbol}.")
            else: logging.error(f"Failed TG send ({symbol}): {response.status_code} - {response.text}")
        except Exception as e: logging.error(f"Error sending TG msg ({symbol}): {e}", exc_info=True)

    # --- GUI Queue Processing & Main Loop (No changes from previous full code) ---
    def process_gui_queue(self):
        """Process any GUI updates from threads"""
        try:
            while not self.gui_queue.empty():
                item = self.gui_queue.get_nowait()
                if isinstance(item, tuple) and len(item) >= 1:
                    # New format: (func, args, kwargs)
                    if len(item) == 3:
                        func, args, kwargs = item
                        try:
                            func(*args, **kwargs)  # Execute in main thread
                        except Exception as e:
                            logging.error(f"Error in GUI callback '{getattr(func,'__name__','N/A')}': {e}", exc_info=True)
                    else:
                        logging.warning(f"Invalid tuple format in GUI queue: {item}")
                elif callable(item):
                    # Old format: callback_func
                    try:
                        item()  # Execute in main thread
                    except Exception as e:
                        logging.error(f"Error in GUI callback '{getattr(item,'__name__','N/A')}': {e}", exc_info=True)
                else:
                    logging.warning(f"Non-callable item from GUI queue: {item}")
                self.gui_queue.task_done()
        except queue.Empty:
            pass
        except Exception as e:
            logging.error(f"Error processing GUI queue: {e}", exc_info=True)
        finally:  # Re-schedule for next frame
            if dpg.is_dearpygui_running():
                frame = dpg.get_frame_count()
                if frame >= 0:
                    dpg.set_frame_callback(frame + 1, callback=self.process_gui_queue)
                elif frame == -1 and not hasattr(self, '_queue_warned'):
                    logging.warning("DPG frame count invalid, cannot reschedule queue processing yet.")
                    self._queue_warned = True

    def create_main_window(self):
        """Create the main window and UI elements"""
        # Check if the main window already exists
        if dpg.does_item_exist("main_window"):
            logging.warning("Main window already exists, skipping creation")
            return

        with dpg.window(tag="main_window", no_close=True, no_title_bar=True): # Main Window Layout
            # Main Menu Bar
            with dpg.menu_bar(tag="main_menu_bar"):
                with dpg.menu(label="File"):
                    dpg.add_menu_item(label="Save Settings", callback=self.save_settings)
                    dpg.add_menu_item(label="Load Settings", callback=self.load_settings)
                    dpg.add_separator()
                    dpg.add_menu_item(label="Exit", callback=lambda: dpg.stop_dearpygui())

                with dpg.menu(label="View"):
                    dpg.add_menu_item(label="Toggle Theme", callback=self.toggle_theme)
                    with dpg.menu(label="Layout"):
                        dpg.add_menu_item(label="Reset Layout", callback=self.reset_layout)
                        dpg.add_menu_item(label="Compact Mode", callback=self.toggle_compact_mode, check=True)

                with dpg.menu(label="Tools"):
                    dpg.add_menu_item(label="Clear Cache", callback=self.clear_cache)
                    dpg.add_menu_item(label="Export Data", callback=self.export_data)

                with dpg.menu(label="Help"):
                    dpg.add_menu_item(label="Keyboard Shortcuts", callback=self.show_shortcuts)
                    dpg.add_menu_item(label="About", callback=self.show_about)

            # Create tabs for different sections
            with dpg.tab_bar(tag="main_tab_bar"):
                # Signals Tab
                with dpg.tab(label="Signals", tag="signals_tab"):
                    with dpg.group(horizontal=True): # Top Bar
                        default_sym = ",".join(self.custom_symbols) if self.custom_symbols else "BTCUSDT,ETHUSDT"
                        dpg.add_text("GUI Loaded")
                        dpg.add_input_text(tag="symbol_input", default_value=default_sym, hint="Symbols (comma-separated)", width=400)
                        dpg.add_button(label="Load List", callback=self.add_symbols, width=80); dpg.add_button(label="Clear List", callback=self.clear_symbols, width=80)
                        dpg.add_spacer(width=15); dpg.add_button(label="Refresh", tag="refresh_btn", callback=self.refresh_live_analysis, width=80)
                        dpg.add_combo(['5m', '15m', '1h', '4h', '1d', '1w'], default_value=self.timeframe, callback=self.set_timeframe, width=70)
                        dpg.add_spacer(width=15); dpg.add_button(label="Start Scan", tag="scan_btn", callback=self.toggle_live_analysis, width=100)
                        dpg.add_spacer(width=15); dpg.add_checkbox(label="Auto", tag="auto_refresh_cb", default_value=self.live_scan, callback=self.toggle_auto_refresh)
                        dpg.add_spacer(width=15); dpg.add_input_int(tag="refresh_interval", default_value=60, min_value=10, max_value=3600, width=100, step=10, label=" Sec")
                        dpg.add_spacer(width=30); dpg.add_text("Status: Ready", tag="status_text")
                    dpg.add_spacer(height=10)
                    with dpg.group(horizontal=True): # Filters Bar
                        dpg.add_text("Filters:"); dpg.add_spacer(width=10)
                        keys_labels=[('BUY','BUY'),('SELL','SELL'),('CONF>60','Conf>60'),('VOL_SPIKE','VolSpk'),('HTF_ALIGN','HTF'),('SENT_CONF','Sent'),('WAVE_X','WaveX'),('DIV_CONF','Div'),('ST_ALIGN','ST Align')]
                        for key, label in keys_labels:
                            if key in self.filter_vars: dpg.add_checkbox(label=label, callback=self.apply_live_filters_gui, user_data=key, tag=f'filter_{key}'); dpg.add_spacer(width=15)
                    dpg.add_spacer(height=10)
                    with dpg.table(tag="live_table", header_row=True, resizable=True, policy=dpg.mvTable_SizingStretchProp, # Results Table
                                   row_background=True, borders_outerH=True, borders_innerV=True, borders_innerH=True, borders_outerV=True,
                                   height=-1, width=-1, scrollY=True): pass # Columns added by _clear_table

                # Positions Tab
                with dpg.tab(label="Positions", tag="positions_tab"):
                    with dpg.group(horizontal=True): # Top Bar for Positions
                        dpg.add_button(label="Refresh Positions", tag="refresh_positions_btn", callback=self.refresh_positions, width=150)
                        dpg.add_spacer(width=15)
                        dpg.add_checkbox(label="Auto Refresh", tag="auto_refresh_positions_cb", default_value=False, callback=self.toggle_auto_refresh_positions)
                        dpg.add_spacer(width=15)
                        dpg.add_input_int(tag="positions_refresh_interval", default_value=30, min_value=5, max_value=300, width=100, step=5, label=" Sec")
                        dpg.add_spacer(width=30)
                        dpg.add_text("Positions Status: Ready", tag="positions_status_text")
                    dpg.add_spacer(height=10)
                    with dpg.table(tag="positions_table", header_row=True, resizable=True, policy=dpg.mvTable_SizingStretchProp,
                                   row_background=True, borders_outerH=True, borders_innerV=True, borders_innerH=True, borders_outerV=True,
                                   height=-1, width=-1, scrollY=True):
                        # Add columns for positions table
                        dpg.add_table_column(label="Symbol", width_fixed=True, width=120)
                        dpg.add_table_column(label="Side", width_fixed=True, width=80)
                        dpg.add_table_column(label="Size", width_fixed=True, width=100)
                        dpg.add_table_column(label="Entry Price", width_fixed=True, width=120)
                        dpg.add_table_column(label="Current Price", width_fixed=True, width=120)
                        dpg.add_table_column(label="P&L", width_fixed=True, width=100)
                        dpg.add_table_column(label="P&L %", width_fixed=True, width=100)
                        dpg.add_table_column(label="Take Profit", width_fixed=True, width=120)
                        dpg.add_table_column(label="Stop Loss", width_fixed=True, width=120)
                        dpg.add_table_column(label="Modify TP", width_fixed=True, width=100)
                        dpg.add_table_column(label="Modify SL", width_fixed=True, width=100)
                        dpg.add_table_column(label="Close", width_fixed=True, width=80)

                # Performance Tab
                with dpg.tab(label="Performance", tag="performance_tab"):
                    with dpg.group(horizontal=True): # Top Bar for Performance
                        dpg.add_button(label="Refresh History", tag="refresh_history_btn", callback=self.refresh_trade_history, width=150)
                        dpg.add_spacer(width=15)
                        dpg.add_combo(["All Time", "This Month", "This Week", "Today"], default_value="All Time", callback=self.set_history_timeframe, width=120, tag="history_timeframe")
                        dpg.add_spacer(width=30)
                        dpg.add_text("Performance Status: Ready", tag="performance_status_text")
                    dpg.add_spacer(height=10)

                    # Performance Metrics
                    with dpg.group(horizontal=True):
                        # Left column - Summary metrics
                        with dpg.group():
                            dpg.add_text("Performance Summary", color=(255, 255, 0))
                            dpg.add_spacer(height=5)
                            with dpg.table(header_row=False, borders_innerH=True, borders_outerH=True, borders_innerV=True, borders_outerV=True):
                                dpg.add_table_column()
                                dpg.add_table_column()

                                # Add metrics rows
                                with dpg.table_row():
                                    dpg.add_text("Total Trades")
                                    dpg.add_text("0", tag="metric_total_trades")

                                with dpg.table_row():
                                    dpg.add_text("Win Rate")
                                    dpg.add_text("0%", tag="metric_win_rate")

                                with dpg.table_row():
                                    dpg.add_text("Profit Factor")
                                    dpg.add_text("0", tag="metric_profit_factor")

                                with dpg.table_row():
                                    dpg.add_text("Average Win")
                                    dpg.add_text("$0", tag="metric_avg_win")

                                with dpg.table_row():
                                    dpg.add_text("Average Loss")
                                    dpg.add_text("$0", tag="metric_avg_loss")

                                with dpg.table_row():
                                    dpg.add_text("Largest Win")
                                    dpg.add_text("$0", tag="metric_largest_win")

                                with dpg.table_row():
                                    dpg.add_text("Largest Loss")
                                    dpg.add_text("$0", tag="metric_largest_loss")

                                with dpg.table_row():
                                    dpg.add_text("Net Profit/Loss")
                                    dpg.add_text("$0", tag="metric_net_pl")

                        dpg.add_spacer(width=30)

                        # Right column - Performance chart
                        with dpg.group():
                            dpg.add_text("Equity Curve", color=(255, 255, 0))
                            dpg.add_spacer(height=5)
                            with dpg.plot(label="Equity Curve", height=300, width=500, tag="equity_plot"):
                                dpg.add_plot_legend()
                                dpg.add_plot_axis(dpg.mvXAxis, label="Trades", tag="equity_x_axis")
                                dpg.add_plot_axis(dpg.mvYAxis, label="Equity ($)", tag="equity_y_axis")
                                dpg.add_line_series([0], [0], label="Equity", parent="equity_y_axis", tag="equity_series")

                    dpg.add_spacer(height=20)

                    # Trade History Table
                    dpg.add_text("Trade History", color=(255, 255, 0))
                    dpg.add_spacer(height=5)
                    with dpg.table(tag="trade_history_table", header_row=True, resizable=True, policy=dpg.mvTable_SizingStretchProp,
                                   row_background=True, borders_outerH=True, borders_innerV=True, borders_innerH=True, borders_outerV=True,
                                   height=250, width=-1, scrollY=True):
                        dpg.add_table_column(label="Date/Time", width_fixed=True, width=150)
                        dpg.add_table_column(label="Symbol", width_fixed=True, width=120)
                        dpg.add_table_column(label="Side", width_fixed=True, width=80)
                        dpg.add_table_column(label="Size", width_fixed=True, width=100)
                        dpg.add_table_column(label="Entry Price", width_fixed=True, width=120)
                        dpg.add_table_column(label="Exit Price", width_fixed=True, width=120)
                        dpg.add_table_column(label="P&L", width_fixed=True, width=100)
                        dpg.add_table_column(label="P&L %", width_fixed=True, width=100)
                        dpg.add_table_column(label="Duration", width_fixed=True, width=100)

        # Set primary window
        dpg.set_primary_window("main_window", True)

        # Clear and initialize the table
        self._clear_table()

    # setup_gui method removed as its functionality is now in the run method

    def run(self):
        """Run the GUI application"""
        logging.info("Running GUI...")

        try:
            # Create context
            dpg.create_context()
            logging.info("DPG context created")

            # Create viewport
            dpg.create_viewport(title="SignalCheck Pro", width=1600, height=900)
            dpg.set_viewport_clear_color(self.theme['background'])
            logging.info("DPG viewport created")

            # Set up the process_gui_queue callback to run on the first frame
            dpg.set_frame_callback(1, callback=self.process_gui_queue)

            # Create the main window and UI elements
            self.create_main_window()
            logging.info("Main window created")

            # Set primary window
            dpg.set_primary_window("main_window", True)

            # Setup and show
            dpg.setup_dearpygui()
            dpg.show_viewport()
            logging.info("Viewport shown")

            # Check for positions that were closed while the app was not running
            try:
                self.check_closed_positions()
            except Exception as e:
                logging.error(f"Error checking closed positions: {e}", exc_info=True)

            # Initial refresh of positions
            try:
                self.refresh_positions()
            except Exception as e:
                logging.error(f"Error during initial position refresh: {e}", exc_info=True)

            # Start DPG
            logging.info("Starting DPG main loop")
            dpg.start_dearpygui()

            # This point is reached when the GUI is closed
            logging.info("DPG main loop ended")
        except Exception as e:
            logging.error(f"Error running GUI: {e}", exc_info=True)
        finally:
            # Cleanup
            logging.info("Shutting down GUI...")
            self.stop_event.set()
            try:
                if dpg.is_dearpygui_running():
                    dpg.stop_dearpygui()
                dpg.destroy_context()
            except Exception as e:
                logging.error(f"Error during cleanup: {e}", exc_info=True)

# --- Main Function ---
def main():
    # Configure logging first
    configure_logging()
    logging.info("Starting application...")

    try:
        logging.info("Initializing AdvancedTradingBot...")
        bot = AdvancedTradingBot()
        logging.info("AdvancedTradingBot initialized.")

        if not bot.session:
            logging.critical("Failed Bybit connection. Check API keys/network. Exiting.")
            print("CRITICAL: Failed Bybit connection. Check API keys/network. Exiting.")
            return

        logging.info("Creating AdvancedTradingGUI...")
        gui = AdvancedTradingGUI(bot)
        logging.info("AdvancedTradingGUI created successfully.")

        logging.info("Running GUI...")
        gui.run()
        logging.info("GUI run completed.")

    except Exception as e:
        logging.error(f"Error in main: {type(e).__name__} - {e}", exc_info=True)
        print(f"Error: {type(e).__name__} - {e}")  # Force print to console
        raise  # Re-raise to see full traceback

    finally:
        logging.info("Shutting down application...")

if __name__ == "__main__":
    main()
