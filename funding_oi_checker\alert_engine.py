import time
import logging
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import numpy as np

class AlertType(Enum):
    # Funding Rate Alerts
    FUNDING_EXTREME_POSITIVE = "funding_extreme_positive"
    FUNDING_EXTREME_NEGATIVE = "funding_extreme_negative"
    FUNDING_REVERSAL_BULLISH = "funding_reversal_bullish"
    FUNDING_REVERSAL_BEARISH = "funding_reversal_bearish"
    FUNDING_PRICE_DIVERGENCE = "funding_price_divergence"

    # Open Interest Alerts
    OI_SURGE_BULLISH = "oi_surge_bullish"
    OI_SURGE_BEARISH = "oi_surge_bearish"
    OI_DUMP_LIQUIDATION = "oi_dump_liquidation"
    OI_TREND_REVERSAL = "oi_trend_reversal"
    OI_PRICE_DIVERGENCE = "oi_price_divergence"

    # Combined Squeeze Alerts
    BULLISH_SQUEEZE = "bullish_squeeze"
    BEARISH_SQUEEZE = "bearish_squeeze"

    # Reversal Pattern Alerts
    BULLISH_REVERSAL = "bullish_reversal"
    BEARISH_REVERSAL = "bearish_reversal"

    # CVD Alerts
    CVD_BULLISH_DIVERGENCE = "cvd_bullish_divergence"
    CVD_BEARISH_DIVERGENCE = "cvd_bearish_divergence"
    CVD_MOMENTUM_SHIFT = "cvd_momentum_shift"

    # Multi-factor Alerts
    MULTI_BULLISH_ALIGNMENT = "multi_bullish_alignment"
    MULTI_BEARISH_ALIGNMENT = "multi_bearish_alignment"

    # Volume Alerts
    VOLUME_SPIKE = "volume_spike"

@dataclass
class MarketSnapshot:
    symbol: str
    timestamp: datetime
    timeframe: str
    price: float
    funding_rate: float
    oi_value: float
    oi_change_pct: float
    cvd: float
    volume: float
    volume_avg: float
    price_change_pct: float

@dataclass
class MarketData:
    symbol: str
    price: float
    funding_rate: float
    oi_value: float
    oi_change: float
    cvd: float
    volume: float
    volume_avg: float
    timestamp: datetime
    price_change_1h: float = 0.0
    price_change_4h: float = 0.0
    price_change_1d: float = 0.0

@dataclass
class Alert:
    symbol: str
    alert_type: AlertType
    message: str
    confidence: float
    timestamp: datetime
    data: Dict[str, Any]

class MultiTimeframeMemory:
    """Stores historical snapshots across multiple timeframes for comprehensive trend analysis"""

    def __init__(self):
        self.timeframes = ['5m', '15m', '30m', '1h', '4h', '1d']
        self.max_snapshots_per_timeframe = {
            '5m': 50,   # ~4 hours of 5m data
            '15m': 40,  # ~10 hours of 15m data
            '30m': 48,  # ~24 hours of 30m data
            '1h': 72,   # ~3 days of 1h data
            '4h': 42,   # ~7 days of 4h data
            '1d': 30    # ~30 days of daily data
        }

        # Structure: {symbol: {timeframe: [snapshots]}}
        self.snapshots: Dict[str, Dict[str, List[MarketSnapshot]]] = {}
        self.warmup_counts: Dict[str, int] = {}  # Track warmup cycles per symbol
        self.min_warmup_cycles = 3  # Minimum cycles before alerting

    def add_snapshot(self, symbol: str, data: MarketData):
        """Add snapshots across all timeframes"""
        if symbol not in self.snapshots:
            self.snapshots[symbol] = {tf: [] for tf in self.timeframes}
            self.warmup_counts[symbol] = 0

        # Increment warmup counter
        self.warmup_counts[symbol] += 1

        # Create snapshots for each timeframe
        for timeframe in self.timeframes:
            snapshot = MarketSnapshot(
                symbol=symbol,
                timestamp=data.timestamp,
                timeframe=timeframe,
                price=data.price,
                funding_rate=data.funding_rate,
                oi_value=data.oi_value,
                oi_change_pct=data.oi_change,
                cvd=data.cvd,
                volume=data.volume,
                volume_avg=data.volume_avg,
                price_change_pct=0.0  # Will be calculated
            )

            self.snapshots[symbol][timeframe].append(snapshot)

            # Maintain max snapshots per timeframe
            max_snaps = self.max_snapshots_per_timeframe[timeframe]
            if len(self.snapshots[symbol][timeframe]) > max_snaps:
                self.snapshots[symbol][timeframe] = self.snapshots[symbol][timeframe][-max_snaps:]

    def is_warmed_up(self, symbol: str) -> bool:
        """Check if symbol has enough data for reliable alerting"""
        return self.warmup_counts.get(symbol, 0) >= self.min_warmup_cycles

    def get_trend_analysis(self, symbol: str, metric: str, timeframe: str, periods: int = 5) -> Dict[str, Any]:
        """Get comprehensive trend analysis for a metric"""
        if (symbol not in self.snapshots or
            timeframe not in self.snapshots[symbol] or
            len(self.snapshots[symbol][timeframe]) < periods):
            return {
                'trend': 'insufficient_data',
                'strength': 0.0,
                'change_pct': 0.0,
                'values': []
            }

        snapshots = self.snapshots[symbol][timeframe][-periods:]
        values = []

        for snap in snapshots:
            if hasattr(snap, metric):
                values.append(getattr(snap, metric))

        if len(values) < 2:
            return {'trend': 'insufficient_data', 'strength': 0.0, 'change_pct': 0.0, 'values': values}

        # Calculate trend
        first_val = values[0]
        last_val = values[-1]
        change_pct = (last_val - first_val) / abs(first_val) if first_val != 0 else 0

        # Calculate trend strength using linear regression slope
        x = np.arange(len(values))
        slope = np.polyfit(x, values, 1)[0] if len(values) > 1 else 0

        # Determine trend direction
        if change_pct > 0.02:  # 2% threshold
            trend = 'rising'
        elif change_pct < -0.02:
            trend = 'falling'
        else:
            trend = 'stable'

        return {
            'trend': trend,
            'strength': abs(slope),
            'change_pct': change_pct,
            'values': values,
            'slope': slope
        }

    def detect_divergence(self, symbol: str, metric1: str, metric2: str, timeframe: str = '1h') -> Dict[str, Any]:
        """Detect divergence between two metrics (e.g., price vs CVD)"""
        if (symbol not in self.snapshots or
            timeframe not in self.snapshots[symbol] or
            len(self.snapshots[symbol][timeframe]) < 5):
            return {'divergence': False, 'type': None, 'strength': 0.0}

        analysis1 = self.get_trend_analysis(symbol, metric1, timeframe, 5)
        analysis2 = self.get_trend_analysis(symbol, metric2, timeframe, 5)

        if analysis1['trend'] == 'insufficient_data' or analysis2['trend'] == 'insufficient_data':
            return {'divergence': False, 'type': None, 'strength': 0.0}

        # Check for divergence
        trend1 = analysis1['trend']
        trend2 = analysis2['trend']

        divergence_detected = False
        divergence_type = None

        # Bullish divergence: price falling, indicator rising
        if trend1 == 'falling' and trend2 == 'rising':
            divergence_detected = True
            divergence_type = 'bullish'

        # Bearish divergence: price rising, indicator falling
        elif trend1 == 'rising' and trend2 == 'falling':
            divergence_detected = True
            divergence_type = 'bearish'

        strength = (abs(analysis1['strength']) + abs(analysis2['strength'])) / 2

        return {
            'divergence': divergence_detected,
            'type': divergence_type,
            'strength': strength,
            'metric1_trend': analysis1,
            'metric2_trend': analysis2
        }

    def get_recent_snapshots(self, symbol: str, timeframe: str, count: int = 5) -> List[MarketSnapshot]:
        """Get recent snapshots for a symbol and timeframe"""
        if (symbol not in self.snapshots or
            timeframe not in self.snapshots[symbol]):
            return []

        return self.snapshots[symbol][timeframe][-count:]

class AlertCooldownManager:
    """Manages alert cooldowns to prevent spam"""
    
    def __init__(self, default_cooldown_minutes: int = 5):
        self.default_cooldown = default_cooldown_minutes * 60  # Convert to seconds
        self.last_alerts: Dict[str, Dict[AlertType, datetime]] = {}
    
    def can_send_alert(self, symbol: str, alert_type: AlertType, cooldown_minutes: Optional[int] = None) -> bool:
        """Check if an alert can be sent based on cooldown"""
        cooldown_seconds = (cooldown_minutes or self.default_cooldown // 60) * 60
        
        if symbol not in self.last_alerts:
            return True
        
        if alert_type not in self.last_alerts[symbol]:
            return True
        
        time_since_last = datetime.now() - self.last_alerts[symbol][alert_type]
        return time_since_last.total_seconds() >= cooldown_seconds
    
    def record_alert(self, symbol: str, alert_type: AlertType):
        """Record that an alert was sent"""
        if symbol not in self.last_alerts:
            self.last_alerts[symbol] = {}
        
        self.last_alerts[symbol][alert_type] = datetime.now()

class SentimentScorer:
    """Calculates sentiment scores based on multiple factors with enhanced analysis"""

    def calculate_sentiment(self, data: MarketData, memory: MultiTimeframeMemory) -> float:
        """Calculate sentiment score from -1 (bearish) to +1 (bullish)"""
        score = 0.0

        # Funding rate component (30% weight)
        if data.funding_rate > 0.01:  # Very positive funding
            score -= 0.25  # Bearish (overheated longs, squeeze risk)
        elif data.funding_rate > 0.005:
            score -= 0.1   # Mildly bearish
        elif data.funding_rate < -0.01:  # Very negative funding
            score += 0.3   # Bullish (shorts paying, potential squeeze)
        elif data.funding_rate < -0.005:
            score += 0.15  # Mildly bullish

        # Open Interest component (25% weight)
        if data.oi_change > 0.05:  # Strong OI increase
            if data.funding_rate >= 0:
                score += 0.15  # New longs entering (bullish)
            else:
                score -= 0.05  # New shorts entering (potential bearish)
        elif data.oi_change < -0.10:  # Strong OI decrease
            score -= 0.15  # Major position closures (bearish)

        # CVD component (25% weight) - enhanced with trend analysis
        cvd_analysis = memory.get_trend_analysis(data.symbol, 'cvd', '1h', 5)
        if cvd_analysis['trend'] == 'rising':
            score += 0.2 * min(1.0, cvd_analysis['strength'] * 10)
        elif cvd_analysis['trend'] == 'falling':
            score -= 0.2 * min(1.0, cvd_analysis['strength'] * 10)

        # Volume component (20% weight)
        volume_ratio = data.volume / max(data.volume_avg, 1)
        if volume_ratio > 2.0:  # High volume
            score += 0.1 * min(1.0, volume_ratio / 3.0)
        elif volume_ratio < 0.5:  # Low volume
            score -= 0.05

        # Price momentum component (additional factor)
        price_analysis = memory.get_trend_analysis(data.symbol, 'price', '1h', 5)
        if price_analysis['trend'] == 'rising':
            score += 0.05
        elif price_analysis['trend'] == 'falling':
            score -= 0.05

        # Divergence penalty/bonus
        price_cvd_divergence = memory.detect_divergence(data.symbol, 'price', 'cvd', '1h')
        if price_cvd_divergence['divergence']:
            if price_cvd_divergence['type'] == 'bullish':
                score += 0.1 * price_cvd_divergence['strength']
            elif price_cvd_divergence['type'] == 'bearish':
                score -= 0.1 * price_cvd_divergence['strength']

        return max(-1.0, min(1.0, score))

class AlertTimingManager:
    """Manages alert timing, baseline data, and change detection"""

    def __init__(self):
        self.baseline_data: Dict[str, MarketData] = {}  # Stores baseline data for each symbol
        self.previous_scan_data: Dict[str, MarketData] = {}  # Stores previous scan data
        self.initial_scan_completed = False
        self.auto_refresh_enabled = False
        self.scan_count = 0

        # Change detection thresholds - more conservative to reduce noise
        self.change_thresholds = {
            'funding_rate': 0.002,      # 0.2% funding rate change (was 0.1%)
            'oi_change': 0.05,          # 5% OI change (was 2%)
            'cvd': 0.10,                # 10% CVD change (was 5%)
            'volume_ratio': 0.5,        # 50% volume ratio change (was 30%)
            'price': 0.01               # 1% price change (was 0.5%)
        }

    def set_auto_refresh_enabled(self, enabled: bool):
        """Set auto-refresh status"""
        self.auto_refresh_enabled = enabled
        logging.info(f"Alert timing: Auto-refresh {'enabled' if enabled else 'disabled'}")

    def is_initial_scan(self) -> bool:
        """Check if this is the initial baseline scan"""
        return self.scan_count == 0 or not self.initial_scan_completed

    def should_evaluate_alerts(self) -> bool:
        """Determine if alerts should be evaluated"""
        # Only evaluate alerts if:
        # 1. Auto-refresh is enabled
        # 2. Initial scan is completed
        # 3. We have baseline data
        return (self.auto_refresh_enabled and
                self.initial_scan_completed and
                len(self.baseline_data) > 0)

    def process_scan_data(self, symbol: str, current_data: MarketData) -> bool:
        """Process scan data and determine if alerts should be checked for this symbol"""
        if self.is_initial_scan():
            # Initial scan - store baseline data, no alerts
            self.baseline_data[symbol] = current_data
            logging.debug(f"Baseline data stored for {symbol}")
            return False

        # Check if we should evaluate alerts
        if not self.should_evaluate_alerts():
            logging.debug(f"Alert evaluation disabled for {symbol}")
            # Still update previous scan data even if alerts disabled
            self.previous_scan_data[symbol] = current_data
            return False

        # Check if data has changed significantly (compares vs baseline or previous)
        has_changed = self._detect_significant_changes(symbol, current_data)

        # Always update previous scan data after comparison
        self.previous_scan_data[symbol] = current_data

        return has_changed

    def complete_initial_scan(self):
        """Mark initial scan as completed"""
        if not self.initial_scan_completed:
            self.initial_scan_completed = True
            logging.info(f"Initial baseline scan completed for {len(self.baseline_data)} symbols")

    def reset_baseline(self):
        """Reset baseline data (for manual scans)"""
        self.baseline_data.clear()
        self.previous_scan_data.clear()
        self.initial_scan_completed = False
        self.scan_count = 0
        logging.info("Baseline data reset for new scan")

    def _detect_significant_changes(self, symbol: str, current_data: MarketData) -> bool:
        """Detect if there are significant changes in market data"""
        # If no previous scan data, use baseline data for comparison
        if symbol not in self.previous_scan_data:
            if symbol in self.baseline_data:
                # Compare against baseline data
                comparison_data = self.baseline_data[symbol]
                logging.debug(f"Comparing {symbol} against baseline data")
            else:
                # No baseline data either - this shouldn't happen in normal flow
                logging.warning(f"No baseline or previous data for {symbol} - skipping alert evaluation")
                return False
        else:
            # Use previous scan data for comparison
            comparison_data = self.previous_scan_data[symbol]
            logging.debug(f"Comparing {symbol} against previous scan data")

        changes_detected = []

        # Check funding rate change
        fr_change = abs(current_data.funding_rate - comparison_data.funding_rate)
        if fr_change >= self.change_thresholds['funding_rate']:
            changes_detected.append(f"funding_rate: {fr_change:.4f}")

        # Check OI change
        oi_change = abs(current_data.oi_change - comparison_data.oi_change)
        if oi_change >= self.change_thresholds['oi_change']:
            changes_detected.append(f"oi_change: {oi_change:.3f}")

        # Check CVD change
        cvd_change = abs(current_data.cvd - comparison_data.cvd)
        cvd_change_pct = cvd_change / max(abs(comparison_data.cvd), 1)
        if cvd_change_pct >= self.change_thresholds['cvd']:
            changes_detected.append(f"cvd: {cvd_change_pct:.3f}")

        # Check volume ratio change
        current_vol_ratio = current_data.volume / max(current_data.volume_avg, 1)
        comparison_vol_ratio = comparison_data.volume / max(comparison_data.volume_avg, 1)
        vol_ratio_change = abs(current_vol_ratio - comparison_vol_ratio)
        if vol_ratio_change >= self.change_thresholds['volume_ratio']:
            changes_detected.append(f"volume_ratio: {vol_ratio_change:.3f}")

        # Check price change
        price_change_pct = abs(current_data.price - comparison_data.price) / comparison_data.price
        if price_change_pct >= self.change_thresholds['price']:
            changes_detected.append(f"price: {price_change_pct:.3f}")

        if changes_detected:
            logging.debug(f"Significant changes detected for {symbol}: {', '.join(changes_detected)}")
            return True

        logging.debug(f"No significant changes for {symbol} - skipping alert evaluation")
        return False

    def get_baseline_data(self, symbol: str) -> Optional[MarketData]:
        """Get baseline data for a symbol"""
        return self.baseline_data.get(symbol)

    def get_previous_data(self, symbol: str) -> Optional[MarketData]:
        """Get previous scan data for a symbol"""
        return self.previous_scan_data.get(symbol)

class EnhancedAlertEngine:
    """Enhanced alert engine with proper timing and change detection"""

    def __init__(self, config_manager):
        self.config = config_manager
        self.memory = MultiTimeframeMemory()
        self.cooldown_manager = AlertCooldownManager(
            self.config.get('alerts.cooldown_minutes', 5)
        )
        self.sentiment_scorer = SentimentScorer()
        self.timing_manager = AlertTimingManager()
        self.alert_history: List[Alert] = []
        self.alert_history_file = os.path.join(
            self.config.config_dir, "alerts.log"
        )

        # Alert thresholds
        self.thresholds = {
            'funding_extreme_positive': 0.015,  # 1.5% funding rate
            'funding_extreme_negative': -0.015,
            'funding_reversal_threshold': 0.008,  # 0.8% change
            'oi_surge_threshold': 0.15,  # 15% OI change
            'oi_dump_threshold': -0.20,  # 20% OI drop
            'cvd_divergence_threshold': 0.05,
            'volume_spike_multiplier': 3.0,
            'multi_factor_threshold': 0.7  # Sentiment threshold for multi-factor alerts
        }

        # Load existing alert history
        self.load_alert_history()
    
    def process_market_data(self, data: MarketData, is_manual_scan: bool = False) -> List[Alert]:
        """Process new market data with proper timing and change detection"""
        alerts = []

        # Always add to memory for trend analysis
        self.memory.add_snapshot(data.symbol, data)

        # Process through timing manager
        should_check_alerts = self.timing_manager.process_scan_data(data.symbol, data)

        # If this is a manual scan, reset baseline and don't check alerts
        if is_manual_scan:
            logging.debug(f"Manual scan for {data.symbol} - storing baseline data only")
            return alerts

        # Check if we should evaluate alerts for this symbol
        if not should_check_alerts:
            logging.debug(f"No significant changes or alert evaluation disabled for {data.symbol}")
            return alerts

        # Check if symbol is warmed up (has enough historical data)
        if not self.memory.is_warmed_up(data.symbol):
            logging.debug(f"Symbol {data.symbol} still in warmup phase, skipping alerts")
            return alerts

        # Get symbol-specific alert config
        symbol_config = self.config.get_symbol_alert_config(data.symbol)

        if not symbol_config.get('enable_alerts', True):
            return alerts

        # Calculate sentiment
        sentiment = self.sentiment_scorer.calculate_sentiment(data, self.memory)

        logging.info(f"Evaluating alerts for {data.symbol} (significant changes detected)")

        # Check all alert conditions
        alerts.extend(self._check_funding_rate_alerts(data, symbol_config))
        alerts.extend(self._check_open_interest_alerts(data, symbol_config))
        alerts.extend(self._check_squeeze_setups(data, sentiment, symbol_config))
        alerts.extend(self._check_reversal_patterns(data, sentiment, symbol_config))
        alerts.extend(self._check_cvd_alerts(data, symbol_config))
        alerts.extend(self._check_multi_factor_alerts(data, sentiment, symbol_config))
        alerts.extend(self._check_volume_spikes(data, symbol_config))

        # Filter alerts based on cooldown
        filtered_alerts = []
        for alert in alerts:
            if self.cooldown_manager.can_send_alert(
                alert.symbol, alert.alert_type,
                symbol_config.get('cooldown_minutes')
            ):
                filtered_alerts.append(alert)
                self.cooldown_manager.record_alert(alert.symbol, alert.alert_type)
                logging.info(f"Alert generated: {alert.alert_type.value} for {alert.symbol}")
            else:
                logging.debug(f"Alert suppressed by cooldown: {alert.alert_type.value} for {alert.symbol}")

        # Save alerts to history
        for alert in filtered_alerts:
            self.alert_history.append(alert)
            self.save_alert_to_file(alert)

        return filtered_alerts

    def set_auto_refresh_enabled(self, enabled: bool):
        """Enable/disable auto-refresh mode for alert evaluation"""
        self.timing_manager.set_auto_refresh_enabled(enabled)

    def start_manual_scan(self):
        """Start a manual scan - resets baseline data"""
        self.timing_manager.reset_baseline()
        logging.info("Manual scan started - baseline data reset")

    def complete_initial_scan(self):
        """Mark initial scan as completed"""
        self.timing_manager.complete_initial_scan()

    def get_scan_status(self) -> Dict[str, Any]:
        """Get current scan status information"""
        return {
            'initial_scan_completed': self.timing_manager.initial_scan_completed,
            'auto_refresh_enabled': self.timing_manager.auto_refresh_enabled,
            'baseline_symbols_count': len(self.timing_manager.baseline_data),
            'scan_count': self.timing_manager.scan_count,
            'should_evaluate_alerts': self.timing_manager.should_evaluate_alerts()
        }
    
    def _check_funding_rate_alerts(self, data: MarketData, config: Dict) -> List[Alert]:
        """Check for funding rate related alerts"""
        alerts = []

        # Extreme funding rate alerts
        if data.funding_rate > self.thresholds['funding_extreme_positive']:
            alert = Alert(
                symbol=data.symbol,
                alert_type=AlertType.FUNDING_EXTREME_POSITIVE,
                message=f"🔴 EXTREME POSITIVE FUNDING: {data.symbol}\n"
                       f"Funding Rate: {data.funding_rate*100:.3f}%\n"
                       f"Longs paying heavily - potential long squeeze risk",
                confidence=min(0.9, data.funding_rate / 0.02),
                timestamp=datetime.now(),
                data={'funding_rate': data.funding_rate, 'price': data.price}
            )
            alerts.append(alert)

        elif data.funding_rate < self.thresholds['funding_extreme_negative']:
            alert = Alert(
                symbol=data.symbol,
                alert_type=AlertType.FUNDING_EXTREME_NEGATIVE,
                message=f"🟢 EXTREME NEGATIVE FUNDING: {data.symbol}\n"
                       f"Funding Rate: {data.funding_rate*100:.3f}%\n"
                       f"Shorts paying heavily - potential short squeeze setup",
                confidence=min(0.9, abs(data.funding_rate) / 0.02),
                timestamp=datetime.now(),
                data={'funding_rate': data.funding_rate, 'price': data.price}
            )
            alerts.append(alert)

        # Funding rate reversal detection
        fr_analysis = self.memory.get_trend_analysis(data.symbol, 'funding_rate', '1h', 5)
        if fr_analysis['trend'] != 'insufficient_data':
            change_pct = abs(fr_analysis['change_pct'])

            if change_pct > self.thresholds['funding_reversal_threshold']:
                if fr_analysis['trend'] == 'rising' and data.funding_rate > 0:
                    alert = Alert(
                        symbol=data.symbol,
                        alert_type=AlertType.FUNDING_REVERSAL_BEARISH,
                        message=f"🔴 FUNDING REVERSAL (BEARISH): {data.symbol}\n"
                               f"Funding shifted from negative to positive\n"
                               f"Current: {data.funding_rate*100:.3f}% (Change: {change_pct*100:.1f}%)",
                        confidence=min(0.8, change_pct / 0.02),
                        timestamp=datetime.now(),
                        data={'funding_rate': data.funding_rate, 'change_pct': change_pct}
                    )
                    alerts.append(alert)

                elif fr_analysis['trend'] == 'falling' and data.funding_rate < 0:
                    alert = Alert(
                        symbol=data.symbol,
                        alert_type=AlertType.FUNDING_REVERSAL_BULLISH,
                        message=f"🟢 FUNDING REVERSAL (BULLISH): {data.symbol}\n"
                               f"Funding shifted from positive to negative\n"
                               f"Current: {data.funding_rate*100:.3f}% (Change: {change_pct*100:.1f}%)",
                        confidence=min(0.8, change_pct / 0.02),
                        timestamp=datetime.now(),
                        data={'funding_rate': data.funding_rate, 'change_pct': change_pct}
                    )
                    alerts.append(alert)

        # Funding vs Price divergence
        divergence = self.memory.detect_divergence(data.symbol, 'price', 'funding_rate', '1h')
        if divergence['divergence'] and divergence['strength'] > 0.1:
            alert = Alert(
                symbol=data.symbol,
                alert_type=AlertType.FUNDING_PRICE_DIVERGENCE,
                message=f"⚠️ FUNDING-PRICE DIVERGENCE: {data.symbol}\n"
                       f"Price and funding rate moving in opposite directions\n"
                       f"Type: {divergence['type'].title()} divergence\n"
                       f"Strength: {divergence['strength']:.2f}",
                confidence=min(0.8, divergence['strength']),
                timestamp=datetime.now(),
                data={'divergence_type': divergence['type'], 'strength': divergence['strength']}
            )
            alerts.append(alert)

        return alerts

    def _check_open_interest_alerts(self, data: MarketData, config: Dict) -> List[Alert]:
        """Check for open interest related alerts"""
        alerts = []

        # OI surge alerts
        if data.oi_change > self.thresholds['oi_surge_threshold']:
            # Determine if bullish or bearish based on funding rate
            if data.funding_rate >= 0:
                alert_type = AlertType.OI_SURGE_BULLISH
                message = f"🟢 BULLISH OI SURGE: {data.symbol}\n" \
                         f"Open Interest: +{data.oi_change*100:.1f}%\n" \
                         f"Funding: {data.funding_rate*100:.3f}% (New longs entering)"
            else:
                alert_type = AlertType.OI_SURGE_BEARISH
                message = f"🔴 BEARISH OI SURGE: {data.symbol}\n" \
                         f"Open Interest: +{data.oi_change*100:.1f}%\n" \
                         f"Funding: {data.funding_rate*100:.3f}% (New shorts entering)"

            alert = Alert(
                symbol=data.symbol,
                alert_type=alert_type,
                message=message,
                confidence=min(0.9, data.oi_change / 0.3),
                timestamp=datetime.now(),
                data={'oi_change': data.oi_change, 'funding_rate': data.funding_rate}
            )
            alerts.append(alert)

        # OI dump (liquidation) alerts
        elif data.oi_change < self.thresholds['oi_dump_threshold']:
            alert = Alert(
                symbol=data.symbol,
                alert_type=AlertType.OI_DUMP_LIQUIDATION,
                message=f"💥 LIQUIDATION EVENT: {data.symbol}\n"
                       f"Open Interest: {data.oi_change*100:.1f}%\n"
                       f"Major position closures detected",
                confidence=min(0.9, abs(data.oi_change) / 0.4),
                timestamp=datetime.now(),
                data={'oi_change': data.oi_change, 'price': data.price}
            )
            alerts.append(alert)

        # OI trend reversal
        oi_analysis = self.memory.get_trend_analysis(data.symbol, 'oi_change_pct', '1h', 5)
        if oi_analysis['trend'] != 'insufficient_data':
            if abs(oi_analysis['change_pct']) > 0.1:  # 10% trend change
                alert = Alert(
                    symbol=data.symbol,
                    alert_type=AlertType.OI_TREND_REVERSAL,
                    message=f"🔄 OI TREND REVERSAL: {data.symbol}\n"
                           f"OI trend changed to: {oi_analysis['trend']}\n"
                           f"Change: {oi_analysis['change_pct']*100:.1f}%",
                    confidence=min(0.7, abs(oi_analysis['change_pct']) / 0.2),
                    timestamp=datetime.now(),
                    data={'trend': oi_analysis['trend'], 'change_pct': oi_analysis['change_pct']}
                )
                alerts.append(alert)

        # OI vs Price divergence
        divergence = self.memory.detect_divergence(data.symbol, 'price', 'oi_value', '1h')
        if divergence['divergence'] and divergence['strength'] > 0.1:
            alert = Alert(
                symbol=data.symbol,
                alert_type=AlertType.OI_PRICE_DIVERGENCE,
                message=f"⚠️ OI-PRICE DIVERGENCE: {data.symbol}\n"
                       f"Price and OI moving in opposite directions\n"
                       f"Type: {divergence['type'].title()} divergence",
                confidence=min(0.8, divergence['strength']),
                timestamp=datetime.now(),
                data={'divergence_type': divergence['type'], 'strength': divergence['strength']}
            )
            alerts.append(alert)

        return alerts

    def _check_squeeze_setups(self, data: MarketData, sentiment: float, config: Dict) -> List[Alert]:
        """Check for squeeze setup patterns with enhanced logic"""
        alerts = []

        # Get trend analysis for CVD
        cvd_analysis = self.memory.get_trend_analysis(data.symbol, 'cvd', '1h', 5)

        # Bullish squeeze: Negative funding + Rising OI + Rising CVD
        if (data.funding_rate < -0.008 and  # Shorts paying significantly
            data.oi_change > 0.05 and      # 5% OI increase
            cvd_analysis['trend'] == 'rising' and
            data.oi_change > config.get('oi_threshold', 0.02)):

            # Calculate confidence based on multiple factors
            funding_strength = abs(data.funding_rate) / 0.02  # Normalize to 2%
            oi_strength = data.oi_change / 0.15  # Normalize to 15%
            cvd_strength = cvd_analysis['strength']
            confidence = min(0.95, (funding_strength + oi_strength + cvd_strength) / 3)

            alert = Alert(
                symbol=data.symbol,
                alert_type=AlertType.BULLISH_SQUEEZE,
                message=f"🟢 BULLISH SQUEEZE SETUP: {data.symbol}\n"
                       f"💰 Funding: {data.funding_rate*100:.3f}% (shorts paying)\n"
                       f"📈 OI Change: +{data.oi_change*100:.1f}% (new positions)\n"
                       f"🔄 CVD: {cvd_analysis['trend']} (buying pressure)\n"
                       f"⚡ Confidence: {confidence:.1%}",
                confidence=confidence,
                timestamp=datetime.now(),
                data={
                    'funding_rate': data.funding_rate,
                    'oi_change': data.oi_change,
                    'cvd_trend': cvd_analysis['trend'],
                    'price': data.price
                }
            )
            alerts.append(alert)

        # Bearish squeeze: Positive funding + Rising OI + Falling CVD
        elif (data.funding_rate > 0.008 and   # Longs paying significantly
              data.oi_change > 0.05 and      # 5% OI increase
              cvd_analysis['trend'] == 'falling' and
              data.oi_change > config.get('oi_threshold', 0.02)):

            # Calculate confidence
            funding_strength = data.funding_rate / 0.02
            oi_strength = data.oi_change / 0.15
            cvd_strength = cvd_analysis['strength']
            confidence = min(0.95, (funding_strength + oi_strength + cvd_strength) / 3)

            alert = Alert(
                symbol=data.symbol,
                alert_type=AlertType.BEARISH_SQUEEZE,
                message=f"🔴 BEARISH SQUEEZE SETUP: {data.symbol}\n"
                       f"💰 Funding: {data.funding_rate*100:.3f}% (longs paying)\n"
                       f"📈 OI Change: +{data.oi_change*100:.1f}% (new positions)\n"
                       f"🔄 CVD: {cvd_analysis['trend']} (selling pressure)\n"
                       f"⚡ Confidence: {confidence:.1%}",
                confidence=confidence,
                timestamp=datetime.now(),
                data={
                    'funding_rate': data.funding_rate,
                    'oi_change': data.oi_change,
                    'cvd_trend': cvd_analysis['trend'],
                    'price': data.price
                }
            )
            alerts.append(alert)

        return alerts
    
    def _check_reversal_patterns(self, data: MarketData, sentiment: float, config: Dict) -> List[Alert]:
        """Check for reversal patterns with enhanced detection"""
        alerts = []

        # Get trend analyses
        oi_analysis = self.memory.get_trend_analysis(data.symbol, 'oi_change_pct', '1h', 5)
        cvd_analysis = self.memory.get_trend_analysis(data.symbol, 'cvd', '1h', 5)

        # Bullish reversal: Falling OI + Extreme negative funding + CVD momentum shift
        if (data.oi_change < -0.10 and  # 10% OI drop
            data.funding_rate < -0.012 and  # Extreme negative funding
            oi_analysis['trend'] == 'falling' and
            cvd_analysis['trend'] == 'rising'):

            confidence = min(0.85, (abs(data.funding_rate) / 0.02 + abs(data.oi_change) / 0.2) / 2)

            alert = Alert(
                symbol=data.symbol,
                alert_type=AlertType.BULLISH_REVERSAL,
                message=f"🟢 BULLISH REVERSAL PATTERN: {data.symbol}\n"
                       f"📉 OI falling: {data.oi_change*100:.1f}% (shorts covering)\n"
                       f"💰 Extreme funding: {data.funding_rate*100:.3f}%\n"
                       f"🔄 CVD momentum: {cvd_analysis['trend']}\n"
                       f"⚡ Confidence: {confidence:.1%}",
                confidence=confidence,
                timestamp=datetime.now(),
                data={
                    'funding_rate': data.funding_rate,
                    'oi_change': data.oi_change,
                    'cvd_trend': cvd_analysis['trend'],
                    'price': data.price
                }
            )
            alerts.append(alert)

        # Bearish reversal: Falling OI + Extreme positive funding + CVD momentum shift
        elif (data.oi_change < -0.10 and  # 10% OI drop
              data.funding_rate > 0.012 and  # Extreme positive funding
              oi_analysis['trend'] == 'falling' and
              cvd_analysis['trend'] == 'falling'):

            confidence = min(0.85, (data.funding_rate / 0.02 + abs(data.oi_change) / 0.2) / 2)

            alert = Alert(
                symbol=data.symbol,
                alert_type=AlertType.BEARISH_REVERSAL,
                message=f"🔴 BEARISH REVERSAL PATTERN: {data.symbol}\n"
                       f"📉 OI falling: {data.oi_change*100:.1f}% (longs covering)\n"
                       f"💰 Extreme funding: {data.funding_rate*100:.3f}%\n"
                       f"🔄 CVD momentum: {cvd_analysis['trend']}\n"
                       f"⚡ Confidence: {confidence:.1%}",
                confidence=confidence,
                timestamp=datetime.now(),
                data={
                    'funding_rate': data.funding_rate,
                    'oi_change': data.oi_change,
                    'cvd_trend': cvd_analysis['trend'],
                    'price': data.price
                }
            )
            alerts.append(alert)

        return alerts

    def _check_cvd_alerts(self, data: MarketData, config: Dict) -> List[Alert]:
        """Check for CVD-related alerts"""
        alerts = []

        # CVD vs Price divergence (bullish)
        divergence = self.memory.detect_divergence(data.symbol, 'price', 'cvd', '1h')
        if divergence['divergence'] and divergence['strength'] > 0.15:
            if divergence['type'] == 'bullish':  # Price falling, CVD rising
                alert = Alert(
                    symbol=data.symbol,
                    alert_type=AlertType.CVD_BULLISH_DIVERGENCE,
                    message=f"🟢 BULLISH CVD DIVERGENCE: {data.symbol}\n"
                           f"📉 Price trend: {divergence['metric1_trend']['trend']}\n"
                           f"📈 CVD trend: {divergence['metric2_trend']['trend']}\n"
                           f"💪 Strength: {divergence['strength']:.2f}",
                    confidence=min(0.8, divergence['strength']),
                    timestamp=datetime.now(),
                    data={'divergence_type': 'bullish', 'strength': divergence['strength']}
                )
                alerts.append(alert)

            elif divergence['type'] == 'bearish':  # Price rising, CVD falling
                alert = Alert(
                    symbol=data.symbol,
                    alert_type=AlertType.CVD_BEARISH_DIVERGENCE,
                    message=f"🔴 BEARISH CVD DIVERGENCE: {data.symbol}\n"
                           f"📈 Price trend: {divergence['metric1_trend']['trend']}\n"
                           f"📉 CVD trend: {divergence['metric2_trend']['trend']}\n"
                           f"💪 Strength: {divergence['strength']:.2f}",
                    confidence=min(0.8, divergence['strength']),
                    timestamp=datetime.now(),
                    data={'divergence_type': 'bearish', 'strength': divergence['strength']}
                )
                alerts.append(alert)

        # CVD momentum shift
        cvd_analysis = self.memory.get_trend_analysis(data.symbol, 'cvd', '1h', 5)
        if cvd_analysis['trend'] != 'insufficient_data' and abs(cvd_analysis['change_pct']) > 0.1:
            recent_snapshots = self.memory.get_recent_snapshots(data.symbol, '1h', 3)
            if len(recent_snapshots) >= 3:
                # Check for momentum shift pattern
                cvd_values = [snap.cvd for snap in recent_snapshots]
                if ((cvd_values[0] < cvd_values[1] > cvd_values[2]) or  # Peak
                    (cvd_values[0] > cvd_values[1] < cvd_values[2])):   # Trough

                    shift_type = "bullish" if cvd_values[-1] > cvd_values[0] else "bearish"

                    alert = Alert(
                        symbol=data.symbol,
                        alert_type=AlertType.CVD_MOMENTUM_SHIFT,
                        message=f"🔄 CVD MOMENTUM SHIFT: {data.symbol}\n"
                               f"Direction: {shift_type.title()}\n"
                               f"Change: {cvd_analysis['change_pct']*100:.1f}%",
                        confidence=min(0.7, abs(cvd_analysis['change_pct']) / 0.2),
                        timestamp=datetime.now(),
                        data={'shift_type': shift_type, 'change_pct': cvd_analysis['change_pct']}
                    )
                    alerts.append(alert)

        return alerts
    
    def _check_multi_factor_alerts(self, data: MarketData, sentiment: float, config: Dict) -> List[Alert]:
        """Check for multi-factor alignment signals"""
        alerts = []

        # Get trend analyses
        fr_analysis = self.memory.get_trend_analysis(data.symbol, 'funding_rate', '1h', 5)
        oi_analysis = self.memory.get_trend_analysis(data.symbol, 'oi_change_pct', '1h', 5)
        cvd_analysis = self.memory.get_trend_analysis(data.symbol, 'cvd', '1h', 5)

        # Multi-factor bullish alignment
        bullish_factors = 0
        bullish_details = []

        if data.funding_rate < -0.005:  # Negative funding
            bullish_factors += 1
            bullish_details.append(f"Funding: {data.funding_rate*100:.3f}% (shorts paying)")

        if data.oi_change > 0.03:  # Rising OI
            bullish_factors += 1
            bullish_details.append(f"OI: +{data.oi_change*100:.1f}% (new positions)")

        if cvd_analysis['trend'] == 'rising':  # Rising CVD
            bullish_factors += 1
            bullish_details.append(f"CVD: {cvd_analysis['trend']} (buying pressure)")

        if data.volume / data.volume_avg > 1.5:  # Above average volume
            bullish_factors += 1
            bullish_details.append(f"Volume: {data.volume/data.volume_avg:.1f}x average")

        if sentiment > self.thresholds['multi_factor_threshold']:
            bullish_factors += 1
            bullish_details.append(f"Sentiment: {sentiment:.2f} (bullish)")

        # Trigger alert if 3+ factors align
        if bullish_factors >= 3:
            confidence = min(0.95, bullish_factors / 5)

            alert = Alert(
                symbol=data.symbol,
                alert_type=AlertType.MULTI_BULLISH_ALIGNMENT,
                message=f"🟢 MULTI-FACTOR BULLISH SIGNAL: {data.symbol}\n"
                       f"✅ {bullish_factors}/5 bullish factors aligned:\n" +
                       "\n".join([f"• {detail}" for detail in bullish_details]) +
                       f"\n⚡ Confidence: {confidence:.1%}",
                confidence=confidence,
                timestamp=datetime.now(),
                data={
                    'factors_count': bullish_factors,
                    'factors': bullish_details,
                    'sentiment': sentiment
                }
            )
            alerts.append(alert)

        # Multi-factor bearish alignment
        bearish_factors = 0
        bearish_details = []

        if data.funding_rate > 0.005:  # Positive funding
            bearish_factors += 1
            bearish_details.append(f"Funding: {data.funding_rate*100:.3f}% (longs paying)")

        if data.oi_change > 0.03:  # Rising OI (with positive funding = new shorts)
            bearish_factors += 1
            bearish_details.append(f"OI: +{data.oi_change*100:.1f}% (new positions)")

        if cvd_analysis['trend'] == 'falling':  # Falling CVD
            bearish_factors += 1
            bearish_details.append(f"CVD: {cvd_analysis['trend']} (selling pressure)")

        if data.volume / data.volume_avg > 1.5:  # Above average volume
            bearish_factors += 1
            bearish_details.append(f"Volume: {data.volume/data.volume_avg:.1f}x average")

        if sentiment < -self.thresholds['multi_factor_threshold']:
            bearish_factors += 1
            bearish_details.append(f"Sentiment: {sentiment:.2f} (bearish)")

        # Trigger alert if 3+ factors align
        if bearish_factors >= 3:
            confidence = min(0.95, bearish_factors / 5)

            alert = Alert(
                symbol=data.symbol,
                alert_type=AlertType.MULTI_BEARISH_ALIGNMENT,
                message=f"🔴 MULTI-FACTOR BEARISH SIGNAL: {data.symbol}\n"
                       f"❌ {bearish_factors}/5 bearish factors aligned:\n" +
                       "\n".join([f"• {detail}" for detail in bearish_details]) +
                       f"\n⚡ Confidence: {confidence:.1%}",
                confidence=confidence,
                timestamp=datetime.now(),
                data={
                    'factors_count': bearish_factors,
                    'factors': bearish_details,
                    'sentiment': sentiment
                }
            )
            alerts.append(alert)

        return alerts

    def _check_volume_spikes(self, data: MarketData, config: Dict) -> List[Alert]:
        """Check for volume spikes"""
        alerts = []

        volume_ratio = data.volume / max(data.volume_avg, 1)
        threshold = self.thresholds['volume_spike_multiplier']

        if volume_ratio > threshold:
            alert = Alert(
                symbol=data.symbol,
                alert_type=AlertType.VOLUME_SPIKE,
                message=f"📊 VOLUME SPIKE: {data.symbol}\n"
                       f"Volume: {volume_ratio:.1f}x average\n"
                       f"Current: {data.volume:,.0f}\n"
                       f"Average: {data.volume_avg:,.0f}",
                confidence=min(0.8, volume_ratio / threshold / 2),
                timestamp=datetime.now(),
                data={
                    'volume_ratio': volume_ratio,
                    'volume': data.volume,
                    'volume_avg': data.volume_avg,
                    'price': data.price
                }
            )
            alerts.append(alert)

        return alerts
    
    def _check_cvd_divergence(self, data: MarketData, config: Dict) -> List[Alert]:
        """Check for CVD divergence patterns"""
        alerts = []
        
        if len(self.context_memory.snapshots.get(data.symbol, [])) < 3:
            return alerts
        
        # Simple divergence detection would go here
        # This is a placeholder for more complex divergence logic
        
        return alerts
    
    def save_alert_to_file(self, alert: Alert):
        """Save alert to log file"""
        try:
            with open(self.alert_history_file, 'a', encoding='utf-8') as f:
                alert_data = {
                    'timestamp': alert.timestamp.isoformat(),
                    'symbol': alert.symbol,
                    'type': alert.alert_type.value,
                    'message': alert.message,
                    'confidence': alert.confidence,
                    'data': alert.data
                }
                f.write(json.dumps(alert_data) + '\n')
        except Exception as e:
            logging.error(f"Error saving alert to file: {e}")
    
    def load_alert_history(self):
        """Load alert history from file"""
        try:
            if os.path.exists(self.alert_history_file):
                with open(self.alert_history_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        try:
                            alert_data = json.loads(line.strip())
                            alert = Alert(
                                symbol=alert_data['symbol'],
                                alert_type=AlertType(alert_data['type']),
                                message=alert_data['message'],
                                confidence=alert_data['confidence'],
                                timestamp=datetime.fromisoformat(alert_data['timestamp']),
                                data=alert_data['data']
                            )
                            self.alert_history.append(alert)
                        except Exception as e:
                            logging.warning(f"Error parsing alert line: {e}")
        except Exception as e:
            logging.error(f"Error loading alert history: {e}")
    
    def get_recent_alerts(self, hours: int = 24) -> List[Alert]:
        """Get alerts from the last N hours"""
        cutoff = datetime.now() - timedelta(hours=hours)
        return [alert for alert in self.alert_history if alert.timestamp > cutoff]
