import time
import logging
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

class AlertType(Enum):
    BULLISH_SQUEEZE = "bullish_squeeze"
    BEARISH_SQUEEZE = "bearish_squeeze"
    BULLISH_REVERSAL = "bullish_reversal"
    BEARISH_REVERSAL = "bearish_reversal"
    VOLUME_SPIKE = "volume_spike"
    CVD_DIVERGENCE = "cvd_divergence"
    FUNDING_EXTREME = "funding_extreme"
    OI_SURGE = "oi_surge"

@dataclass
class MarketData:
    symbol: str
    price: float
    funding_rate: float
    oi_change: float
    cvd: float
    volume: float
    volume_avg: float
    timestamp: datetime

@dataclass
class Alert:
    symbol: str
    alert_type: AlertType
    message: str
    confidence: float
    timestamp: datetime
    data: Dict[str, Any]

class TrendContextMemory:
    """Stores historical snapshots for trend analysis"""
    
    def __init__(self, max_snapshots: int = 5):
        self.max_snapshots = max_snapshots
        self.snapshots: Dict[str, List[MarketData]] = {}
    
    def add_snapshot(self, symbol: str, data: MarketData):
        """Add a new snapshot for a symbol"""
        if symbol not in self.snapshots:
            self.snapshots[symbol] = []
        
        self.snapshots[symbol].append(data)
        
        # Keep only the last N snapshots
        if len(self.snapshots[symbol]) > self.max_snapshots:
            self.snapshots[symbol] = self.snapshots[symbol][-self.max_snapshots:]
    
    def get_trend(self, symbol: str, metric: str) -> str:
        """Get trend direction for a specific metric"""
        if symbol not in self.snapshots or len(self.snapshots[symbol]) < 2:
            return "N/A"
        
        snapshots = self.snapshots[symbol]
        values = [getattr(snap, metric) for snap in snapshots if hasattr(snap, metric)]
        
        if len(values) < 2:
            return "N/A"
        
        # Simple trend detection
        recent_avg = sum(values[-2:]) / 2
        older_avg = sum(values[:-2]) / max(1, len(values) - 2) if len(values) > 2 else values[0]
        
        if recent_avg > older_avg * 1.02:
            return "↗️ Rising"
        elif recent_avg < older_avg * 0.98:
            return "↘️ Falling"
        else:
            return "➖ Stable"
    
    def detect_momentum_shift(self, symbol: str) -> bool:
        """Detect if there's been a momentum shift"""
        if symbol not in self.snapshots or len(self.snapshots[symbol]) < 3:
            return False
        
        snapshots = self.snapshots[symbol][-3:]
        
        # Check for CVD momentum shift
        cvd_values = [snap.cvd for snap in snapshots]
        if len(cvd_values) >= 3:
            # Look for reversal pattern
            if cvd_values[0] < cvd_values[1] > cvd_values[2]:  # Peak
                return True
            if cvd_values[0] > cvd_values[1] < cvd_values[2]:  # Trough
                return True
        
        return False

class AlertCooldownManager:
    """Manages alert cooldowns to prevent spam"""
    
    def __init__(self, default_cooldown_minutes: int = 5):
        self.default_cooldown = default_cooldown_minutes * 60  # Convert to seconds
        self.last_alerts: Dict[str, Dict[AlertType, datetime]] = {}
    
    def can_send_alert(self, symbol: str, alert_type: AlertType, cooldown_minutes: Optional[int] = None) -> bool:
        """Check if an alert can be sent based on cooldown"""
        cooldown_seconds = (cooldown_minutes or self.default_cooldown // 60) * 60
        
        if symbol not in self.last_alerts:
            return True
        
        if alert_type not in self.last_alerts[symbol]:
            return True
        
        time_since_last = datetime.now() - self.last_alerts[symbol][alert_type]
        return time_since_last.total_seconds() >= cooldown_seconds
    
    def record_alert(self, symbol: str, alert_type: AlertType):
        """Record that an alert was sent"""
        if symbol not in self.last_alerts:
            self.last_alerts[symbol] = {}
        
        self.last_alerts[symbol][alert_type] = datetime.now()

class SentimentScorer:
    """Calculates sentiment scores based on multiple factors"""
    
    def calculate_sentiment(self, data: MarketData, context: TrendContextMemory) -> float:
        """Calculate sentiment score from -1 (bearish) to +1 (bullish)"""
        score = 0.0
        
        # Funding rate component (30% weight)
        if data.funding_rate > 0.01:  # Very positive funding
            score -= 0.2  # Slightly bearish (overheated longs)
        elif data.funding_rate > 0.005:
            score += 0.1  # Mildly bullish
        elif data.funding_rate < -0.01:  # Very negative funding
            score += 0.3  # Bullish (shorts paying, potential squeeze)
        elif data.funding_rate < -0.005:
            score += 0.1  # Mildly bullish
        
        # Open Interest component (25% weight)
        if data.oi_change > 0.05:  # Strong OI increase
            if data.funding_rate > 0:
                score += 0.15  # New longs entering
            else:
                score -= 0.1   # New shorts entering
        elif data.oi_change < -0.05:  # Strong OI decrease
            score -= 0.1  # Positions closing
        
        # CVD component (25% weight)
        cvd_trend = context.get_trend(data.symbol, 'cvd')
        if "Rising" in cvd_trend:
            score += 0.2
        elif "Falling" in cvd_trend:
            score -= 0.2
        
        # Volume component (20% weight)
        volume_ratio = data.volume / max(data.volume_avg, 1)
        if volume_ratio > 2.0:  # High volume
            score += 0.1
        elif volume_ratio < 0.5:  # Low volume
            score -= 0.05
        
        # Momentum shift bonus
        if context.detect_momentum_shift(data.symbol):
            score += 0.1 if score > 0 else -0.1
        
        return max(-1.0, min(1.0, score))

class AlertEngine:
    """Main alert engine that combines all components"""
    
    def __init__(self, config_manager):
        self.config = config_manager
        self.context_memory = TrendContextMemory()
        self.cooldown_manager = AlertCooldownManager(
            self.config.get('alerts.cooldown_minutes', 5)
        )
        self.sentiment_scorer = SentimentScorer()
        self.alert_history: List[Alert] = []
        self.alert_history_file = os.path.join(
            self.config.config_dir, "alerts.log"
        )
        
        # Load existing alert history
        self.load_alert_history()
    
    def process_market_data(self, data: MarketData) -> List[Alert]:
        """Process new market data and generate alerts"""
        alerts = []
        
        # Add to context memory
        self.context_memory.add_snapshot(data.symbol, data)
        
        # Calculate sentiment
        sentiment = self.sentiment_scorer.calculate_sentiment(data, self.context_memory)
        
        # Get symbol-specific alert config
        symbol_config = self.config.get_symbol_alert_config(data.symbol)
        
        if not symbol_config.get('enable_alerts', True):
            return alerts
        
        # Check for various alert conditions
        alerts.extend(self._check_squeeze_setups(data, sentiment, symbol_config))
        alerts.extend(self._check_reversal_patterns(data, sentiment, symbol_config))
        alerts.extend(self._check_volume_spikes(data, symbol_config))
        alerts.extend(self._check_cvd_divergence(data, symbol_config))
        
        # Filter alerts based on cooldown
        filtered_alerts = []
        for alert in alerts:
            if self.cooldown_manager.can_send_alert(
                alert.symbol, alert.alert_type, 
                symbol_config.get('cooldown_minutes')
            ):
                filtered_alerts.append(alert)
                self.cooldown_manager.record_alert(alert.symbol, alert.alert_type)
        
        # Save alerts to history
        for alert in filtered_alerts:
            self.alert_history.append(alert)
            self.save_alert_to_file(alert)
        
        return filtered_alerts
    
    def _check_squeeze_setups(self, data: MarketData, sentiment: float, config: Dict) -> List[Alert]:
        """Check for squeeze setup patterns"""
        alerts = []
        
        # Bullish squeeze: Negative funding + Rising OI + Rising CVD
        if (data.funding_rate < -config.get('fr_threshold', 0.01) and
            data.oi_change > config.get('oi_threshold', 0.02) and
            sentiment > config.get('sentiment_threshold', 0.3)):
            
            alert = Alert(
                symbol=data.symbol,
                alert_type=AlertType.BULLISH_SQUEEZE,
                message=f"🟢 BULLISH SQUEEZE SETUP: {data.symbol}\n"
                       f"Funding: {data.funding_rate*100:.3f}% (shorts paying)\n"
                       f"OI Change: +{data.oi_change*100:.1f}% (new positions)\n"
                       f"Sentiment: {sentiment:.2f} (bullish)",
                confidence=min(0.9, abs(sentiment)),
                timestamp=datetime.now(),
                data={
                    'funding_rate': data.funding_rate,
                    'oi_change': data.oi_change,
                    'sentiment': sentiment,
                    'price': data.price
                }
            )
            alerts.append(alert)
        
        # Bearish squeeze: Positive funding + Rising OI + Falling CVD
        elif (data.funding_rate > config.get('fr_threshold', 0.01) and
              data.oi_change > config.get('oi_threshold', 0.02) and
              sentiment < -config.get('sentiment_threshold', 0.3)):
            
            alert = Alert(
                symbol=data.symbol,
                alert_type=AlertType.BEARISH_SQUEEZE,
                message=f"🔴 BEARISH SQUEEZE SETUP: {data.symbol}\n"
                       f"Funding: {data.funding_rate*100:.3f}% (longs paying)\n"
                       f"OI Change: +{data.oi_change*100:.1f}% (new positions)\n"
                       f"Sentiment: {sentiment:.2f} (bearish)",
                confidence=min(0.9, abs(sentiment)),
                timestamp=datetime.now(),
                data={
                    'funding_rate': data.funding_rate,
                    'oi_change': data.oi_change,
                    'sentiment': sentiment,
                    'price': data.price
                }
            )
            alerts.append(alert)
        
        return alerts
    
    def _check_reversal_patterns(self, data: MarketData, sentiment: float, config: Dict) -> List[Alert]:
        """Check for reversal patterns"""
        alerts = []
        
        # Bullish reversal: Falling OI + Extreme negative funding + CVD turning up
        if (data.oi_change < -config.get('oi_threshold', 0.02) and
            data.funding_rate < -0.015 and  # More extreme threshold for reversals
            self.context_memory.detect_momentum_shift(data.symbol) and
            sentiment > 0):
            
            alert = Alert(
                symbol=data.symbol,
                alert_type=AlertType.BULLISH_REVERSAL,
                message=f"🟢 BULLISH REVERSAL: {data.symbol}\n"
                       f"OI falling: {data.oi_change*100:.1f}% (shorts covering)\n"
                       f"Extreme funding: {data.funding_rate*100:.3f}%\n"
                       f"Momentum shift detected",
                confidence=0.7,
                timestamp=datetime.now(),
                data={
                    'funding_rate': data.funding_rate,
                    'oi_change': data.oi_change,
                    'sentiment': sentiment,
                    'price': data.price
                }
            )
            alerts.append(alert)
        
        return alerts
    
    def _check_volume_spikes(self, data: MarketData, config: Dict) -> List[Alert]:
        """Check for volume spikes"""
        alerts = []
        
        volume_ratio = data.volume / max(data.volume_avg, 1)
        threshold = config.get('volume_threshold_multiplier', 2.0)
        
        if volume_ratio > threshold:
            alert = Alert(
                symbol=data.symbol,
                alert_type=AlertType.VOLUME_SPIKE,
                message=f"📊 VOLUME SPIKE: {data.symbol}\n"
                       f"Volume: {volume_ratio:.1f}x average\n"
                       f"Current: {data.volume:,.0f}",
                confidence=min(0.8, volume_ratio / threshold / 2),
                timestamp=datetime.now(),
                data={
                    'volume_ratio': volume_ratio,
                    'volume': data.volume,
                    'volume_avg': data.volume_avg,
                    'price': data.price
                }
            )
            alerts.append(alert)
        
        return alerts
    
    def _check_cvd_divergence(self, data: MarketData, config: Dict) -> List[Alert]:
        """Check for CVD divergence patterns"""
        alerts = []
        
        if len(self.context_memory.snapshots.get(data.symbol, [])) < 3:
            return alerts
        
        # Simple divergence detection would go here
        # This is a placeholder for more complex divergence logic
        
        return alerts
    
    def save_alert_to_file(self, alert: Alert):
        """Save alert to log file"""
        try:
            with open(self.alert_history_file, 'a', encoding='utf-8') as f:
                alert_data = {
                    'timestamp': alert.timestamp.isoformat(),
                    'symbol': alert.symbol,
                    'type': alert.alert_type.value,
                    'message': alert.message,
                    'confidence': alert.confidence,
                    'data': alert.data
                }
                f.write(json.dumps(alert_data) + '\n')
        except Exception as e:
            logging.error(f"Error saving alert to file: {e}")
    
    def load_alert_history(self):
        """Load alert history from file"""
        try:
            if os.path.exists(self.alert_history_file):
                with open(self.alert_history_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        try:
                            alert_data = json.loads(line.strip())
                            alert = Alert(
                                symbol=alert_data['symbol'],
                                alert_type=AlertType(alert_data['type']),
                                message=alert_data['message'],
                                confidence=alert_data['confidence'],
                                timestamp=datetime.fromisoformat(alert_data['timestamp']),
                                data=alert_data['data']
                            )
                            self.alert_history.append(alert)
                        except Exception as e:
                            logging.warning(f"Error parsing alert line: {e}")
        except Exception as e:
            logging.error(f"Error loading alert history: {e}")
    
    def get_recent_alerts(self, hours: int = 24) -> List[Alert]:
        """Get alerts from the last N hours"""
        cutoff = datetime.now() - timedelta(hours=hours)
        return [alert for alert in self.alert_history if alert.timestamp > cutoff]
