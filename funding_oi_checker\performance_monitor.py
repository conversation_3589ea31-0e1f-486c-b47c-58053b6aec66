import time
import logging
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass
from collections import deque

@dataclass
class PerformanceMetric:
    operation: str
    duration: float
    timestamp: datetime
    success: bool
    symbol_count: int = 0
    error_message: str = ""

class PerformanceMonitor:
    """Monitor and track performance metrics for the funding/OI checker"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics = deque(maxlen=max_history)
        self.lock = threading.Lock()
        
        # Current operation tracking
        self.active_operations = {}
        
        # Performance statistics
        self.stats = {
            'total_operations': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'avg_duration': 0.0,
            'symbols_per_second': 0.0,
            'last_update': datetime.now()
        }
    
    def start_operation(self, operation: str, symbol_count: int = 0) -> str:
        """Start tracking an operation"""
        operation_id = f"{operation}_{int(time.time() * 1000)}"
        
        with self.lock:
            self.active_operations[operation_id] = {
                'operation': operation,
                'start_time': time.time(),
                'symbol_count': symbol_count
            }
        
        return operation_id
    
    def end_operation(self, operation_id: str, success: bool = True, error_message: str = ""):
        """End tracking an operation"""
        with self.lock:
            if operation_id not in self.active_operations:
                return
            
            op_data = self.active_operations.pop(operation_id)
            duration = time.time() - op_data['start_time']
            
            metric = PerformanceMetric(
                operation=op_data['operation'],
                duration=duration,
                timestamp=datetime.now(),
                success=success,
                symbol_count=op_data['symbol_count'],
                error_message=error_message
            )
            
            self.metrics.append(metric)
            self._update_stats()
            
            # Log performance
            if success:
                logging.info(f"Operation {op_data['operation']} completed in {duration:.2f}s "
                           f"({op_data['symbol_count']} symbols)")
            else:
                logging.error(f"Operation {op_data['operation']} failed after {duration:.2f}s: {error_message}")
    
    def _update_stats(self):
        """Update performance statistics"""
        if not self.metrics:
            return
        
        recent_metrics = [m for m in self.metrics if m.timestamp > datetime.now() - timedelta(hours=1)]
        
        if recent_metrics:
            self.stats['total_operations'] = len(recent_metrics)
            self.stats['successful_operations'] = sum(1 for m in recent_metrics if m.success)
            self.stats['failed_operations'] = sum(1 for m in recent_metrics if not m.success)
            
            # Calculate average duration
            durations = [m.duration for m in recent_metrics if m.success]
            self.stats['avg_duration'] = sum(durations) / len(durations) if durations else 0
            
            # Calculate symbols per second
            total_symbols = sum(m.symbol_count for m in recent_metrics if m.success)
            total_duration = sum(m.duration for m in recent_metrics if m.success)
            self.stats['symbols_per_second'] = total_symbols / total_duration if total_duration > 0 else 0
            
            self.stats['last_update'] = datetime.now()
    
    def get_performance_stats(self) -> Dict:
        """Get current performance statistics"""
        with self.lock:
            return self.stats.copy()
    
    def get_recent_metrics(self, hours: int = 1) -> List[PerformanceMetric]:
        """Get recent performance metrics"""
        cutoff = datetime.now() - timedelta(hours=hours)
        with self.lock:
            return [m for m in self.metrics if m.timestamp > cutoff]
    
    def get_operation_summary(self, operation: str, hours: int = 1) -> Dict:
        """Get summary for specific operation type"""
        recent_metrics = self.get_recent_metrics(hours)
        op_metrics = [m for m in recent_metrics if m.operation == operation]
        
        if not op_metrics:
            return {
                'operation': operation,
                'count': 0,
                'success_rate': 0,
                'avg_duration': 0,
                'total_symbols': 0
            }
        
        successful = [m for m in op_metrics if m.success]
        
        return {
            'operation': operation,
            'count': len(op_metrics),
            'success_rate': len(successful) / len(op_metrics) * 100,
            'avg_duration': sum(m.duration for m in successful) / len(successful) if successful else 0,
            'total_symbols': sum(m.symbol_count for m in successful),
            'last_run': max(m.timestamp for m in op_metrics) if op_metrics else None
        }
    
    def log_performance_summary(self):
        """Log a performance summary"""
        stats = self.get_performance_stats()
        
        logging.info("=== PERFORMANCE SUMMARY ===")
        logging.info(f"Operations (1h): {stats['total_operations']} total, "
                    f"{stats['successful_operations']} successful, "
                    f"{stats['failed_operations']} failed")
        logging.info(f"Average duration: {stats['avg_duration']:.2f}s")
        logging.info(f"Processing rate: {stats['symbols_per_second']:.1f} symbols/second")
        
        # Operation-specific summaries
        operations = ['manual_scan', 'auto_refresh', 'alert_processing']
        for op in operations:
            summary = self.get_operation_summary(op)
            if summary['count'] > 0:
                logging.info(f"{op}: {summary['count']} runs, "
                           f"{summary['success_rate']:.1f}% success, "
                           f"{summary['avg_duration']:.2f}s avg")

class PerformanceContext:
    """Context manager for tracking operation performance"""
    
    def __init__(self, monitor: PerformanceMonitor, operation: str, symbol_count: int = 0):
        self.monitor = monitor
        self.operation = operation
        self.symbol_count = symbol_count
        self.operation_id = None
    
    def __enter__(self):
        self.operation_id = self.monitor.start_operation(self.operation, self.symbol_count)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        success = exc_type is None
        error_message = str(exc_val) if exc_val else ""
        self.monitor.end_operation(self.operation_id, success, error_message)

# Global performance monitor instance
performance_monitor = PerformanceMonitor()

def track_performance(operation: str, symbol_count: int = 0):
    """Decorator for tracking function performance"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with PerformanceContext(performance_monitor, operation, symbol_count):
                return func(*args, **kwargs)
        return wrapper
    return decorator

# Performance monitoring functions
def start_performance_tracking(operation: str, symbol_count: int = 0) -> str:
    """Start tracking an operation"""
    return performance_monitor.start_operation(operation, symbol_count)

def end_performance_tracking(operation_id: str, success: bool = True, error_message: str = ""):
    """End tracking an operation"""
    performance_monitor.end_operation(operation_id, success, error_message)

def get_performance_stats() -> Dict:
    """Get current performance statistics"""
    return performance_monitor.get_performance_stats()

def log_performance_summary():
    """Log performance summary"""
    performance_monitor.log_performance_summary()

# Example usage:
# with PerformanceContext(performance_monitor, "data_fetch", 400):
#     data = fetch_data()
#
# or
#
# @track_performance("alert_processing", 1)
# def process_alerts(data):
#     return alerts
