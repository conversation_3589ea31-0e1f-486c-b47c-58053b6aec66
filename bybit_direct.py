"""
Direct Bybit API implementation with proper timestamp synchronization
"""

import requests
import hmac
import hashlib
import json
# import urllib.parse  # Not needed anymore
import time
import logging

class BybitDirect:
    """
    Direct implementation of Bybit API with proper timestamp synchronization
    """

    def __init__(self, api_key, api_secret):
        self.api_key = api_key
        self.api_secret = api_secret
        self.base_url = "https://api.bybit.com"

    def get_server_time(self):
        """Get Bybit server time"""
        url = f"{self.base_url}/v5/market/time"
        response = requests.get(url)
        data = response.json()

        if data['retCode'] != 0:
            logging.error(f"Error getting server time: {data['retMsg']}")
            return None

        return data['result']

    def sync_time(self):
        """Synchronize time with Bybit server and return offset"""
        server_time = self.get_server_time()
        if not server_time:
            return 0

        server_timestamp = int(server_time['timeSecond']) * 1000
        local_timestamp = int(time.time() * 1000)
        time_offset = server_timestamp - local_timestamp

        logging.info(f"Time synchronized. Server: {server_timestamp}, Local: {local_timestamp}, Offset: {time_offset} ms")
        return time_offset

    def _get(self, endpoint, params=None):
        """
        Make a GET request to the Bybit API.

        Args:
            endpoint (str): The API endpoint
            params (dict): The parameters for the request

        Returns:
            dict: The response from the API
        """
        if params is None:
            params = {}

        # Get the current timestamp with the offset
        timestamp = int(time.time() * 1000)

        # Add the API key and timestamp to the parameters
        params["api_key"] = self.api_key
        params["timestamp"] = str(timestamp)
        params["recv_window"] = "120000"  # 2 minutes

        # Generate the signature
        # Sort parameters alphabetically as required by Bybit
        sorted_params = dict(sorted(params.items()))
        query_string = "&".join([f"{key}={value}" for key, value in sorted_params.items()])

        # Generate the signature
        signature = hmac.new(
            bytes(self.api_secret, "utf-8"),
            bytes(query_string, "utf-8"),
            hashlib.sha256
        ).hexdigest()

        # Add the signature to the parameters
        params["sign"] = signature

        # Make the request
        url = f"{self.base_url}{endpoint}"
        logging.debug(f"Request URL: {url}")
        logging.debug(f"Request Params: {params}")

        response = requests.get(url, params=params)

        # Parse the response
        try:
            return response.json()
        except Exception as e:
            logging.error(f"Error parsing response: {e}")
            return {"retCode": -1, "retMsg": f"Error parsing response: {e}"}

    def get_open_interest(self, category, symbol, intervalTime, limit=2):
        """
        Get the open interest for a symbol.

        Args:
            category (str): Product type. linear,inverse
            symbol (str): Symbol name
            intervalTime (str): Interval time. 5min,15min,30min,1h,4h,1d
            limit (int): Limit for data size per page. [1, 200]. Default: 50

        Returns:
            dict: The response from the API
        """
        params = {
            "category": category,
            "symbol": symbol,
            "intervalTime": intervalTime,
            "limit": limit
        }

        return self._get("/v5/market/open-interest", params)

    def get_funding_rate_history(self, category, symbol, limit=1):
        """
        Get the funding rate history for a symbol.

        Args:
            category (str): Product type. linear,inverse
            symbol (str): Symbol name
            limit (int): Limit for data size per page. [1, 200]. Default: 200

        Returns:
            dict: The response from the API
        """
        params = {
            "category": category,
            "symbol": symbol,
            "limit": limit
        }

        return self._get("/v5/market/funding/history", params)

    def get_kline(self, category, symbol, interval, limit=200):
        """
        Get the kline data for a symbol.

        Args:
            category (str): Product type. spot,linear,inverse
            symbol (str): Symbol name
            interval (str): Kline interval. 1,3,5,15,30,60,120,240,360,720,D,M,W
            limit (int): Limit for data size per page. [1, 1000]. Default: 200

        Returns:
            dict: The response from the API
        """
        params = {
            "category": category,
            "symbol": symbol,
            "interval": interval,
            "limit": limit
        }

        return self._get("/v5/market/kline", params)

    def get_tickers(self, category, symbol):
        """Get ticker information for a symbol"""
        url = f"{self.base_url}/v5/market/tickers?category={category}&symbol={symbol}"
        response = requests.get(url)
        return response.json()

    def get_open_orders(self, category, symbol=None, base_coin=None, settle_coin=None, order_id=None, order_link_id=None, open_only=0, order_filter=None, limit=20, cursor=None):
        """
        Get open & closed orders

        Args:
            category: Product type (linear, inverse, spot, option)
            symbol: Symbol name (optional)
            base_coin: Base coin (optional)
            settle_coin: Settle coin (optional)
            order_id: Order ID (optional)
            order_link_id: User customised order ID (optional)
            open_only: 0 (default): query open status orders only, 1: query a maximum of recent 500 closed status records
            order_filter: Order: active order, StopOrder: conditional order for Futures and Spot, tpslOrder: spot TP/SL order, OcoOrder: Spot oco order, BidirectionalTpslOrder: Spot bidirectional TPSL order
            limit: Limit for data size per page. [1, 50]. Default: 20
            cursor: Cursor. Use the nextPageCursor token from the response to retrieve the next page of the result set

        Returns:
            API response as dictionary
        """
        try:
            # Build the URL with query parameters
            url = f"{self.base_url}/v5/order/realtime"

            # Get server time for timestamp
            server_time = self.get_server_time()
            if not server_time:
                return {"retCode": -1, "retMsg": "Failed to get server time"}

            server_timestamp = int(server_time['timeSecond']) * 1000
            timestamp = server_timestamp
            recv_window = 120000  # 120 seconds (2 minutes) to handle large time differences

            # Create the query parameters
            params = {}
            params["category"] = category

            # Add optional parameters
            if symbol:
                params["symbol"] = symbol
            if base_coin:
                params["baseCoin"] = base_coin
            if settle_coin:
                params["settleCoin"] = settle_coin
            if order_id:
                params["orderId"] = order_id
            if order_link_id:
                params["orderLinkId"] = order_link_id
            if open_only is not None:
                params["openOnly"] = open_only
            if order_filter:
                params["orderFilter"] = order_filter
            if limit:
                params["limit"] = limit
            if cursor:
                params["cursor"] = cursor

            # Add authentication parameters
            params["api_key"] = self.api_key
            params["timestamp"] = timestamp
            params["recv_window"] = recv_window

            # Create the signature string
            # Sort parameters alphabetically as required by Bybit
            sorted_params = dict(sorted(params.items()))
            query_string = "&".join([f"{key}={value}" for key, value in sorted_params.items()])

            # Generate the signature
            signature = hmac.new(
                bytes(self.api_secret, "utf-8"),
                bytes(query_string, "utf-8"),
                hashlib.sha256
            ).hexdigest()

            # Add the signature to the parameters
            params["sign"] = signature

            # Add headers
            headers = {
                "X-BAPI-API-KEY": self.api_key,
                "X-BAPI-SIGN": signature,
                "X-BAPI-SIGN-TYPE": "2",
                "X-BAPI-TIMESTAMP": str(timestamp),
                "X-BAPI-RECV-WINDOW": str(recv_window)
            }

            # Log the request details for debugging
            logging.debug(f"Request URL: {url}")
            logging.debug(f"Request Params: {params}")

            # Make the request
            response = requests.get(url, params=params)
            return response.json()
        except Exception as e:
            logging.error(f"Error getting open orders: {e}")
            return {"retCode": -1, "retMsg": str(e)}

    def get_instruments_info(self, category, symbol):
        """Get instrument information for a symbol"""
        url = f"{self.base_url}/v5/market/instruments-info?category={category}&symbol={symbol}"
        response = requests.get(url)
        return response.json()

    def set_position_mode(self, mode="0"):
        """
        Set the position mode for the account

        Args:
            mode: Position mode (0 for one-way mode, 3 for hedge mode)

        Returns:
            API response
        """
        # First, get the server time to synchronize
        server_time = self.get_server_time()
        if not server_time:
            return {"retCode": -1, "retMsg": "Failed to get server time"}

        server_timestamp = int(server_time['timeSecond']) * 1000

        # Prepare the request
        url = f"{self.base_url}/v5/position/switch-mode"

        # Prepare the request body
        params = {
            "category": "linear",
            "symbol": "BTCUSDT",  # Need to specify a symbol
            "coin": "",  # Optional
            "mode": mode  # 0: one-way mode, 3: hedge mode
        }

        # Add timestamp and recv_window to parameters
        timestamp = server_timestamp  # Use server timestamp directly
        recv_window = 120000  # 120 seconds (2 minutes) to handle large time differences

        # Prepare the request body
        request_body = json.dumps(params)

        # Prepare the signature string
        signature_string = f"{timestamp}{self.api_key}{recv_window}{request_body}"

        # Generate the signature
        signature = hmac.new(
            bytes(self.api_secret, "utf-8"),
            bytes(signature_string, "utf-8"),
            hashlib.sha256
        ).hexdigest()

        # Add signature to headers
        headers = {
            "X-BAPI-API-KEY": self.api_key,
            "X-BAPI-SIGN": signature,
            "X-BAPI-SIGN-TYPE": "2",
            "X-BAPI-TIMESTAMP": str(timestamp),
            "X-BAPI-RECV-WINDOW": str(recv_window),
            "Content-Type": "application/json"
        }

        # Make the request
        try:
            response = requests.post(url, headers=headers, data=request_body)
            result = response.json()

            if result.get('retCode') == 0:
                logging.info(f"Successfully set position mode to {mode}")
            else:
                logging.error(f"Failed to set position mode: {result.get('retMsg')}")

            return result
        except Exception as e:
            logging.error(f"Error setting position mode: {e}")
            return {"retCode": -1, "retMsg": str(e)}

    def place_order(self, **order_params):
        """
        Place an order with proper timestamp synchronization

        Args:
            order_params: Order parameters (category, symbol, side, orderType, qty, etc.)

        Returns:
            API response as dictionary
        """
        # Try to set position mode to one-way mode first
        try:
            self.set_position_mode("0")
        except Exception as e:
            logging.warning(f"Failed to set position mode: {e}")
        # Check if we need to format the quantity correctly
        if 'symbol' in order_params and 'qty' in order_params and order_params.get('category') == 'linear':
            symbol = order_params['symbol']
            qty = order_params['qty']

            # Get instrument info to format quantity correctly
            instrument_info = self.get_instruments_info('linear', symbol)
            if instrument_info.get('retCode') == 0 and len(instrument_info.get('result', {}).get('list', [])) > 0:
                contract_info = instrument_info['result']['list'][0]

                # Get the lot size filter
                if 'lotSizeFilter' in contract_info:
                    min_qty = float(contract_info['lotSizeFilter'].get('minOrderQty', '0.0001'))
                    qty_step = float(contract_info['lotSizeFilter'].get('qtyStep', '0.0001'))

                    # Parse the current quantity
                    try:
                        current_qty = float(qty)

                        # Round the quantity to the nearest step size
                        rounded_qty = round(current_qty / qty_step) * qty_step

                        # Ensure the quantity is at least the minimum
                        rounded_qty = max(rounded_qty, min_qty)

                        # Format with enough decimal places to handle the step size
                        decimal_places = len(str(qty_step).split('.')[-1])
                        formatted_qty = f"{rounded_qty:.{decimal_places}f}"

                        # Update the order parameters
                        order_params['qty'] = formatted_qty
                        logging.info(f"Formatted quantity from {qty} to {formatted_qty} (min: {min_qty}, step: {qty_step})")
                    except (ValueError, TypeError) as e:
                        logging.warning(f"Could not format quantity: {e}")

        # Remove position_mode if it's present
        if 'position_mode' in order_params:
            order_params.pop('position_mode', None)

        # We'll try different position index values if needed

        # First, get the server time to synchronize
        server_time = self.get_server_time()
        if not server_time:
            return {"retCode": -1, "retMsg": "Failed to get server time"}

        server_timestamp = int(server_time['timeSecond']) * 1000

        # Now place the order with proper authentication
        url = f"{self.base_url}/v5/order/create"

        # Add timestamp and recv_window to parameters
        timestamp = server_timestamp  # Use server timestamp directly
        recv_window = 120000  # 120 seconds (2 minutes) to handle large time differences

        # Prepare the request body (order parameters)
        request_body = json.dumps(order_params)

        # Prepare the signature string according to Bybit documentation
        # Format: timestamp + api_key + recv_window + request_body
        signature_string = f"{timestamp}{self.api_key}{recv_window}{request_body}"

        # Generate the signature
        signature = hmac.new(
            bytes(self.api_secret, "utf-8"),
            bytes(signature_string, "utf-8"),
            hashlib.sha256
        ).hexdigest()

        # Add signature to headers
        headers = {
            "X-BAPI-API-KEY": self.api_key,
            "X-BAPI-SIGN": signature,
            "X-BAPI-SIGN-TYPE": "2",
            "X-BAPI-TIMESTAMP": str(timestamp),
            "X-BAPI-RECV-WINDOW": str(recv_window),
            "Content-Type": "application/json"
        }

        # Log the request details for debugging
        logging.debug(f"Request URL: {url}")
        logging.debug(f"Request Headers: {headers}")
        logging.debug(f"Request Body: {request_body}")
        logging.debug(f"Signature String: {signature_string}")

        # Try with different position index values
        position_indices = [None, 0, 1, 2]  # Try without positionIdx first, then with different values

        for idx in position_indices:
            # Set or remove positionIdx
            current_params = order_params.copy()
            if idx is None:
                if 'positionIdx' in current_params:
                    del current_params['positionIdx']
                    logging.info("Trying without positionIdx")
            else:
                current_params['positionIdx'] = idx
                logging.info(f"Trying with positionIdx={idx}")

            # We need to recalculate the signature for each attempt
            # Update the request body with the current parameters
            current_request_body = json.dumps(current_params)

            # Recalculate the signature string
            current_signature_string = f"{timestamp}{self.api_key}{recv_window}{current_request_body}"

            # Generate the new signature
            current_signature = hmac.new(
                bytes(self.api_secret, "utf-8"),
                bytes(current_signature_string, "utf-8"),
                hashlib.sha256
            ).hexdigest()

            # Update the headers with the new signature
            current_headers = {
                "X-BAPI-API-KEY": self.api_key,
                "X-BAPI-SIGN": current_signature,
                "X-BAPI-SIGN-TYPE": "2",
                "X-BAPI-TIMESTAMP": str(timestamp),
                "X-BAPI-RECV-WINDOW": str(recv_window),
                "Content-Type": "application/json"
            }

            # Log the request details for debugging
            logging.debug(f"Request URL: {url}")
            logging.debug(f"Request Headers: {current_headers}")
            logging.debug(f"Request Body: {current_request_body}")
            logging.debug(f"Signature String: {current_signature_string}")

            # Make the request
            response = requests.post(url, headers=current_headers, data=current_request_body)
            result = response.json()

            # Check for errors
            if result.get('retCode') == 0:
                # Success!
                logging.info(f"Order placed successfully with positionIdx={idx}")
                return result
            elif result.get('retCode') == 10001 and "position idx not match position mode" in result.get('retMsg', ''):
                # Try the next position index
                logging.warning(f"Position index {idx} not matching position mode. Trying another index.")
                continue
            else:
                # Other error
                error_msg = f"Failed to place order: {result.get('retMsg')} (Code: {result.get('retCode')})"
                logging.error(error_msg)
                return result

        # If we get here, all attempts failed
        error_msg = "Failed to place order after trying all position index values"
        logging.error(error_msg)
        return {"retCode": -1, "retMsg": error_msg}

    def get_positions(self, category="linear", symbol=None, settle_coin=None):
        """
        Get positions

        Args:
            category: Product type (linear, inverse, etc.)
            symbol: Symbol name (optional)
            settle_coin: Settle coin (optional)

        Returns:
            API response as dictionary
        """
        try:
            # Build the URL with query parameters
            url = f"{self.base_url}/v5/position/list"

            # Get server time for timestamp
            server_time = self.get_server_time()
            if not server_time:
                return {"retCode": -1, "retMsg": "Failed to get server time"}

            server_timestamp = int(server_time['timeSecond']) * 1000
            timestamp = server_timestamp
            recv_window = 120000  # 120 seconds (2 minutes) to handle large time differences

            # Create the query parameters
            params = {}
            params["category"] = category

            # For linear category, we need to provide either symbol or settleCoin
            if category == "linear":
                if symbol:
                    params["symbol"] = symbol
                elif settle_coin:
                    params["settleCoin"] = settle_coin
                else:
                    # Default to USDT as settleCoin if neither is provided
                    params["settleCoin"] = "USDT"
            else:
                # For other categories, add optional parameters if provided
                if symbol:
                    params["symbol"] = symbol
                if settle_coin:
                    params["settleCoin"] = settle_coin

            # Add authentication parameters
            params["api_key"] = self.api_key
            params["timestamp"] = timestamp
            params["recv_window"] = recv_window

            # Create the signature string
            # Sort parameters alphabetically as required by Bybit
            sorted_params = dict(sorted(params.items()))
            query_string = "&".join([f"{key}={value}" for key, value in sorted_params.items()])

            # Generate the signature
            signature = hmac.new(
                bytes(self.api_secret, "utf-8"),
                bytes(query_string, "utf-8"),
                hashlib.sha256
            ).hexdigest()

            # Add the signature to the parameters
            params["sign"] = signature

            # Add headers
            headers = {
                "X-BAPI-API-KEY": self.api_key,
                "X-BAPI-SIGN": signature,
                "X-BAPI-SIGN-TYPE": "2",
                "X-BAPI-TIMESTAMP": str(timestamp),
                "X-BAPI-RECV-WINDOW": str(recv_window)
            }

            # Log the request details for debugging
            logging.debug(f"Request URL: {url}")
            logging.debug(f"Request Params: {params}")

            # Make the request
            response = requests.get(url, params=params)
            return response.json()
        except Exception as e:
            logging.error(f"Error getting positions: {e}")
            return {"retCode": -1, "retMsg": str(e)}

    def set_trading_stop(self, **params):
        """
        Set take profit, stop loss, and trailing stop for a position

        Args:
            params: Parameters for setting trading stop (category, symbol, takeProfit, stopLoss, etc.)

        Returns:
            API response as dictionary
        """
        # Get server time for timestamp
        server_time = self.get_server_time()
        if not server_time:
            return {"retCode": -1, "retMsg": "Failed to get server time"}

        server_timestamp = int(server_time['timeSecond']) * 1000

        # Now set the trading stop with proper authentication
        url = f"{self.base_url}/v5/position/trading-stop"

        # Add timestamp and recv_window to parameters
        timestamp = server_timestamp  # Use server timestamp directly
        recv_window = 120000  # 120 seconds (2 minutes) to handle large time differences

        # Prepare the request body
        request_body = json.dumps(params)

        # Prepare the signature string
        signature_string = f"{timestamp}{self.api_key}{recv_window}{request_body}"

        # Generate the signature
        signature = hmac.new(
            bytes(self.api_secret, "utf-8"),
            bytes(signature_string, "utf-8"),
            hashlib.sha256
        ).hexdigest()

        # Add signature to headers
        headers = {
            "X-BAPI-API-KEY": self.api_key,
            "X-BAPI-SIGN": signature,
            "X-BAPI-SIGN-TYPE": "2",
            "X-BAPI-TIMESTAMP": str(timestamp),
            "X-BAPI-RECV-WINDOW": str(recv_window),
            "Content-Type": "application/json"
        }

        # Make the request
        response = requests.post(url, headers=headers, data=request_body)
        return response.json()

    def get_kline(self, category, symbol, interval, limit=100):
        """
        Get klines (candlestick data) for a symbol.

        Args:
            category (str): The category (e.g., "linear")
            symbol (str): The symbol (e.g., "BTCUSDT")
            interval (str or int): The interval (e.g., 1, 5, 15, 30, 60, 240, "D", "W", "M")
            limit (int): The number of klines to return (default: 100)

        Returns:
            dict: The response from the API
        """
        # Convert interval to string if it's an integer
        if isinstance(interval, int):
            interval = str(interval)

        params = {
            "category": category,
            "symbol": symbol,
            "interval": interval,
            "limit": str(limit)
        }

        return self._get("/v5/market/kline", params)
