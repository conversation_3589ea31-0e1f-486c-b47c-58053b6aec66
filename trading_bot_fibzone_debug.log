2025-07-08 00:02:55,181 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=MORPHOUSDT HTTP/1.1" 200 314
2025-07-08 00:02:55,181 DEBUG: Request Params: {'category': 'linear', 'symbol': 'NEIROETHUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922175117', 'recv_window': '120000', 'sign': '1ad1498d9b62eb00238bbd9d8419cb1156caa271822030b7224c3316d809ffc8'}
2025-07-08 00:02:55,183 DEBUG: (MVLUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:02:55,183 DEBUG: (NKNUSDT) Starting analysis for 5m...
2025-07-08 00:02:55,183 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:55,183 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:55,183 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:55,184 DEBUG: (NCUSDT) analyze_symbol returning: Signal=None
2025-07-08 00:02:55,185 DEBUG: (MORPHOUSDT) analyze_symbol returning: Signal=BUY
2025-07-08 00:02:55,186 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:55,186 DEBUG: (NMRUSDT) Starting analysis for 5m...
2025-07-08 00:02:55,186 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:55,188 DEBUG: Request Params: {'category': 'linear', 'symbol': 'NEOUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922175183', 'recv_window': '120000', 'sign': 'fc414244ee91aada7b5e43787e99cd2dbf7837dfd63a6ef1b22c5461e9213e4a'}
2025-07-08 00:02:55,188 DEBUG: Request Params: {'category': 'linear', 'symbol': 'NFPUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922175183', 'recv_window': '120000', 'sign': 'bc9bd546b7a23f3d43a837ad4269ccbcb321530d3646ddb272b58ad5e65a3177'}
2025-07-08 00:02:55,189 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:55,190 DEBUG: (NOTUSDT) Starting analysis for 5m...
2025-07-08 00:02:55,190 DEBUG: (NSUSDT) Starting analysis for 5m...
2025-07-08 00:02:55,190 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:55,190 DEBUG: Request Params: {'category': 'linear', 'symbol': 'NKNUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922175186', 'recv_window': '120000', 'sign': '2d41f8e03df7fe9792fa7efcc9725ed0883a707a02e24438208dd1db8acfa06e'}
2025-07-08 00:02:55,191 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:55,192 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:55,192 DEBUG: Request Params: {'category': 'linear', 'symbol': 'NEARUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922175189', 'recv_window': '120000', 'sign': '7650afa48a3f0e882bc0565cf90ad6f490fbb8f153b7f1113628c989b45ccd26'}
2025-07-08 00:02:55,193 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:55,193 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:55,193 DEBUG: Request Params: {'category': 'linear', 'symbol': 'NMRUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922175190', 'recv_window': '120000', 'sign': '92f721a51e2f0c29321be0ab00304b6e7ab4df05609ed0d91d412250a43566a0'}
2025-07-08 00:02:55,194 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:55,195 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:55,195 DEBUG: Request Params: {'category': 'linear', 'symbol': 'NOTUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922175192', 'recv_window': '120000', 'sign': '4b6ce913bcf97a091e36aab55dff50d20bee9ad7a0192a94f358815e0ffe9973'}
2025-07-08 00:02:55,195 DEBUG: Request Params: {'category': 'linear', 'symbol': 'NSUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922175193', 'recv_window': '120000', 'sign': '1a9d1e33be5ac7eb7e9b679821e00a0e0ffcf452afc823c6f0d35c2025c3902c'}
2025-07-08 00:02:55,196 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:55,198 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:55,198 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:55,405 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=MYRIAUSDT HTTP/1.1" 200 189
2025-07-08 00:02:55,432 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=MYROUSDT HTTP/1.1" 200 189
2025-07-08 00:02:55,500 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=NFPUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922175183&recv_window=120000&sign=bc9bd546b7a23f3d43a837ad4269ccbcb321530d3646ddb272b58ad5e65a3177 HTTP/1.1" 200 None
2025-07-08 00:02:55,502 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=NMRUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922175190&recv_window=120000&sign=92f721a51e2f0c29321be0ab00304b6e7ab4df05609ed0d91d412250a43566a0 HTTP/1.1" 200 None
2025-07-08 00:02:55,505 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=NOTUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922175192&recv_window=120000&sign=4b6ce913bcf97a091e36aab55dff50d20bee9ad7a0192a94f358815e0ffe9973 HTTP/1.1" 200 None
2025-07-08 00:02:55,562 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=NEIROETHUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922175117&recv_window=120000&sign=1ad1498d9b62eb00238bbd9d8419cb1156caa271822030b7224c3316d809ffc8 HTTP/1.1" 200 None
2025-07-08 00:02:55,562 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=NKNUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922175186&recv_window=120000&sign=2d41f8e03df7fe9792fa7efcc9725ed0883a707a02e24438208dd1db8acfa06e HTTP/1.1" 200 None
2025-07-08 00:02:55,563 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=NEOUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922175183&recv_window=120000&sign=fc414244ee91aada7b5e43787e99cd2dbf7837dfd63a6ef1b22c5461e9213e4a HTTP/1.1" 200 None
2025-07-08 00:02:55,566 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=NEARUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922175189&recv_window=120000&sign=7650afa48a3f0e882bc0565cf90ad6f490fbb8f153b7f1113628c989b45ccd26 HTTP/1.1" 200 None
2025-07-08 00:02:55,571 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=NSUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922175193&recv_window=120000&sign=1a9d1e33be5ac7eb7e9b679821e00a0e0ffcf452afc823c6f0d35c2025c3902c HTTP/1.1" 200 None
2025-07-08 00:02:55,607 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/1.50 vs Threshold: 3.5
2025-07-08 00:02:55,641 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=MYRIAUSDT HTTP/1.1" 200 317
2025-07-08 00:02:55,663 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:55,683 DEBUG: (MYRIAUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:02:55,684 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=MYROUSDT HTTP/1.1" 200 314
2025-07-08 00:02:55,697 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:55,736 DEBUG: (NTRNUSDT) Starting analysis for 5m...
2025-07-08 00:02:55,754 DEBUG: (MYROUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:02:55,785 DEBUG: Request Params: {'category': 'linear', 'symbol': 'NFPUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922175697', 'recv_window': '120000', 'sign': '7f9d2c4e518d9b249653b664834c5b76b4917e6f53d889cf8642b7d92d84be4d'}
2025-07-08 00:02:55,791 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/1.50 vs Threshold: 3.5
2025-07-08 00:02:55,793 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:55,926 DEBUG: Request Params: {'category': 'linear', 'symbol': 'NTRNUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922175793', 'recv_window': '120000', 'sign': 'cf5be023bbbf76110986caeb9fedb36054fcee66af13faef64c1c0efb0612cc4'}
2025-07-08 00:02:55,927 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:55,837 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:55,891 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/1.50 vs Threshold: 3.5
2025-07-08 00:02:55,894 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:55,912 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.50/1.50 vs Threshold: 3.5
2025-07-08 00:02:55,926 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/2.00 vs Threshold: 3.5
2025-07-08 00:02:55,798 DEBUG: (NULSUSDT) Starting analysis for 5m...
2025-07-08 00:02:55,818 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/1.50 vs Threshold: 3.5
2025-07-08 00:02:55,929 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:55,933 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:55,940 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-07-08 00:02:55,946 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/1.80 vs Threshold: 3.5
2025-07-08 00:02:55,948 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:55,949 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:55,949 DEBUG: Request Params: {'category': 'linear', 'symbol': 'NEOUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922175949', 'recv_window': '120000', 'sign': 'e8184926507f3ebc30051abcf4191b668c008982e7d03db92b83c3f21215985d'}
2025-07-08 00:02:55,948 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:55,948 DEBUG: Request Params: {'category': 'linear', 'symbol': 'NMRUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922175932', 'recv_window': '120000', 'sign': '90588f48138c824a0b14e7a30f47df5f7d854dd770d2873673841e4a3fca75c8'}
2025-07-08 00:02:55,948 DEBUG: --- Calculating Trade Plan for N/A 5m BUY ---
2025-07-08 00:02:55,946 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:55,952 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:55,952 DEBUG: Request Params: {'category': 'linear', 'symbol': 'NKNUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922175952', 'recv_window': '120000', 'sign': '0215ce55274b3c15cb8f2fdaf8baa5750c01cb69c4ff2e7b806ea99aaeb25c7e'}
2025-07-08 00:02:55,953 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:55,950 DEBUG: Request Params: {'category': 'linear', 'symbol': 'NEIROETHUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922175948', 'recv_window': '120000', 'sign': 'f680728c181cbf072c51dc509b3509e456bc627aa92fee443ddfd739a5cd91ff'}
2025-07-08 00:02:55,951 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:55,951 DEBUG: (N/A) Last Close: 0.1310, Raw ATR: 0.0004, Valid ATR: 0.0007
2025-07-08 00:02:55,947 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:55,956 DEBUG: Request Params: {'category': 'linear', 'symbol': 'NULSUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922175947', 'recv_window': '120000', 'sign': '06e65469a6f0ad99157309d115afbdb4ecc33a747ebfe6daf474dfd1a1f3399e'}
2025-07-08 00:02:55,956 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:55,955 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:55,947 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:55,955 DEBUG: (N/A) Fibs Available: True
2025-07-08 00:02:55,958 DEBUG: (N/A) Fib Levels: 0%=0.1308, 38.2%=0.1316, 61.8%=0.1320, 100%=0.1328, 161.8%=0.1340, -61.8%=0.1296
2025-07-08 00:02:55,950 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:55,958 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:55,958 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.131564, 0.132036), SL: 0.1306, TP: 0.1340
2025-07-08 00:02:55,959 DEBUG: Request Params: {'category': 'linear', 'symbol': 'NOTUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922175958', 'recv_window': '120000', 'sign': 'ededcdc931c5e6c79d1dde73373135a8ccfbc529d134e90ffef8a4b6a00e6f99'}
2025-07-08 00:02:55,959 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-07-08 00:02:55,961 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.131564, 0.132036), SL=0.1306, TP=0.1340
2025-07-08 00:02:55,961 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-07-08 00:02:55,962 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-07-08 00:02:55,962 DEBUG: (N/A) Price (0.1310) is INSIDE entry zone (0.1316-0.1320).
2025-07-08 00:02:55,960 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:55,962 DEBUG: N/A 5m: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.50/9.0 | Final Confidence: 50.0% | Trade Plan Exists: True | Zone Note: ''
2025-07-08 00:02:55,963 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:55,963 DEBUG: Request Params: {'category': 'linear', 'symbol': 'NSUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922175963', 'recv_window': '120000', 'sign': '89790c6040cc2982dc6007875b1789e02611a48cf434cb70fba044ba6d582aa4'}
2025-07-08 00:02:55,964 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:56,134 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=NEARUSDT HTTP/1.1" 200 188
2025-07-08 00:02:56,259 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=NMRUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922175932&recv_window=120000&sign=90588f48138c824a0b14e7a30f47df5f7d854dd770d2873673841e4a3fca75c8 HTTP/1.1" 200 None
2025-07-08 00:02:56,263 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=NKNUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922175952&recv_window=120000&sign=0215ce55274b3c15cb8f2fdaf8baa5750c01cb69c4ff2e7b806ea99aaeb25c7e HTTP/1.1" 200 None
2025-07-08 00:02:56,263 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=NULSUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922175947&recv_window=120000&sign=06e65469a6f0ad99157309d115afbdb4ecc33a747ebfe6daf474dfd1a1f3399e HTTP/1.1" 200 126
2025-07-08 00:02:56,270 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=NOTUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922175958&recv_window=120000&sign=ededcdc931c5e6c79d1dde73373135a8ccfbc529d134e90ffef8a4b6a00e6f99 HTTP/1.1" 200 None
2025-07-08 00:02:56,274 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=NEIROETHUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922175948&recv_window=120000&sign=f680728c181cbf072c51dc509b3509e456bc627aa92fee443ddfd739a5cd91ff HTTP/1.1" 200 None
2025-07-08 00:02:56,277 WARNING: No data returned for NULSUSDT on 5m. RC: 0, Msg: OK
2025-07-08 00:02:56,277 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=NSUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922175963&recv_window=120000&sign=89790c6040cc2982dc6007875b1789e02611a48cf434cb70fba044ba6d582aa4 HTTP/1.1" 200 None
2025-07-08 00:02:56,280 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=NEOUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922175949&recv_window=120000&sign=e8184926507f3ebc30051abcf4191b668c008982e7d03db92b83c3f21215985d HTTP/1.1" 200 None
2025-07-08 00:02:56,293 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=NTRNUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922175793&recv_window=120000&sign=cf5be023bbbf76110986caeb9fedb36054fcee66af13faef64c1c0efb0612cc4 HTTP/1.1" 200 None
2025-07-08 00:02:56,300 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=NFPUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922175697&recv_window=120000&sign=7f9d2c4e518d9b249653b664834c5b76b4917e6f53d889cf8642b7d92d84be4d HTTP/1.1" 200 None
2025-07-08 00:02:56,307 DEBUG: (NULSUSDT) Fetch data returned empty. Returning None.
2025-07-08 00:02:56,386 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=NEARUSDT HTTP/1.1" 200 314
2025-07-08 00:02:56,400 DEBUG: (NULSUSDT) analyze_symbol returning: Signal=None
2025-07-08 00:02:56,439 DEBUG: (NEARUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:02:56,461 DEBUG: (OGNUSDT) Starting analysis for 5m...
2025-07-08 00:02:56,481 DEBUG: (OGUSDT) Starting analysis for 5m...
2025-07-08 00:02:56,518 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:56,544 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:56,613 DEBUG: Request Params: {'category': 'linear', 'symbol': 'OGNUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922176518', 'recv_window': '120000', 'sign': '753ad4159ef2877f3e41578bef57662492e431f312f0fd895a9c7a904c6ae98f'}
2025-07-08 00:02:56,617 DEBUG: Request Params: {'category': 'linear', 'symbol': 'OGUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922176544', 'recv_window': '120000', 'sign': '2f79d051c82bb5b36d7b5b945d569e2b389b613632ce733ab37d517b18a2a3cd'}
2025-07-08 00:02:56,622 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/0.00 vs Threshold: 3.5
2025-07-08 00:02:56,658 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:56,680 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:56,682 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=NMRUSDT HTTP/1.1" 200 192
2025-07-08 00:02:56,684 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:56,692 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:56,693 DEBUG: Request Params: {'category': 'linear', 'symbol': 'NTRNUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922176692', 'recv_window': '120000', 'sign': 'f7ca5f6a92ee9d00811ad0ceeaae05dc50554828a1f8cc137bda3d4e32711eb5'}
2025-07-08 00:02:56,694 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:56,769 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=NKNUSDT HTTP/1.1" 200 192
2025-07-08 00:02:56,915 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=NFPUSDT HTTP/1.1" 200 192
2025-07-08 00:02:56,916 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=NMRUSDT HTTP/1.1" 200 309
2025-07-08 00:02:56,917 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=NEIROETHUSDT HTTP/1.1" 200 193
2025-07-08 00:02:56,917 DEBUG: (NMRUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:02:56,918 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=NOTUSDT HTTP/1.1" 200 192
2025-07-08 00:02:56,919 DEBUG: (OLUSDT) Starting analysis for 5m...
2025-07-08 00:02:56,920 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:56,920 DEBUG: Request Params: {'category': 'linear', 'symbol': 'OLUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922176920', 'recv_window': '120000', 'sign': '4787a53edebed03b3507a07bcecc37d1d8b334618a5df9ac90c598473700ee10'}
2025-07-08 00:02:56,921 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:56,922 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=NEOUSDT HTTP/1.1" 200 187
2025-07-08 00:02:56,938 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=NSUSDT HTTP/1.1" 200 191
2025-07-08 00:02:57,018 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=NKNUSDT HTTP/1.1" 200 313
2025-07-08 00:02:57,019 DEBUG: (NKNUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:02:57,019 DEBUG: (OMGUSDT) Starting analysis for 5m...
2025-07-08 00:02:57,019 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:57,020 DEBUG: Request Params: {'category': 'linear', 'symbol': 'OMGUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922177019', 'recv_window': '120000', 'sign': 'e02441e8da398048e14f4fc48921a14742fb887f442095ae1d17655c319b5529'}
2025-07-08 00:02:57,020 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:57,061 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=OGUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922176544&recv_window=120000&sign=2f79d051c82bb5b36d7b5b945d569e2b389b613632ce733ab37d517b18a2a3cd HTTP/1.1" 200 None
2025-07-08 00:02:57,071 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=NTRNUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922176692&recv_window=120000&sign=f7ca5f6a92ee9d00811ad0ceeaae05dc50554828a1f8cc137bda3d4e32711eb5 HTTP/1.1" 200 None
2025-07-08 00:02:57,130 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/0.30 vs Threshold: 3.5
2025-07-08 00:02:57,130 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:57,134 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:57,141 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=NFPUSDT HTTP/1.1" 200 311
2025-07-08 00:02:57,163 DEBUG: (NFPUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:02:57,145 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=NEIROETHUSDT HTTP/1.1" 200 318
2025-07-08 00:02:57,144 DEBUG: Request Params: {'category': 'linear', 'symbol': 'OGUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922177133', 'recv_window': '120000', 'sign': '23257da970b7720fad97e3a5121eafb116d32174044e391d150516ee553b7ae0'}
2025-07-08 00:02:57,163 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=OGNUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922176518&recv_window=120000&sign=753ad4159ef2877f3e41578bef57662492e431f312f0fd895a9c7a904c6ae98f HTTP/1.1" 200 None
2025-07-08 00:02:57,163 DEBUG: (OMNIUSDT) Starting analysis for 5m...
2025-07-08 00:02:57,164 DEBUG: (NEIROETHUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:02:57,165 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:57,165 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=NOTUSDT HTTP/1.1" 200 317
2025-07-08 00:02:57,177 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:57,186 DEBUG: (OMUSDT) Starting analysis for 5m...
2025-07-08 00:02:57,187 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=NSUSDT HTTP/1.1" 200 310
2025-07-08 00:02:57,193 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=NEOUSDT HTTP/1.1" 200 309
2025-07-08 00:02:57,194 DEBUG: (NOTUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:02:57,200 DEBUG: Request Params: {'category': 'linear', 'symbol': 'OMNIUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922177177', 'recv_window': '120000', 'sign': '451278fa2ab299cfa945862bd82140af955a3cb06aef304cd72db99da14581bd'}
2025-07-08 00:02:57,209 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:57,230 DEBUG: (NSUSDT) analyze_symbol returning: Signal=BUY
2025-07-08 00:02:57,232 DEBUG: (NEOUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:02:57,241 DEBUG: (ONDOUSDT) Starting analysis for 5m...
2025-07-08 00:02:57,250 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:57,252 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/0.00 vs Threshold: 3.5
2025-07-08 00:02:57,252 DEBUG: Request Params: {'category': 'linear', 'symbol': 'OMUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922177209', 'recv_window': '120000', 'sign': '3fa37c613e43503b4c6e6e74b77146f09d0dfcecc89b4d28d12ce1b2a04e24fb'}
2025-07-08 00:02:57,252 DEBUG: (ONEUSDT) Starting analysis for 5m...
2025-07-08 00:02:57,253 DEBUG: (ONGUSDT) Starting analysis for 5m...
2025-07-08 00:02:57,253 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:57,254 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-07-08 00:02:57,255 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:57,255 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:57,255 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:57,255 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ONDOUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922177253', 'recv_window': '120000', 'sign': 'd5220002b8206e51684cdf069d34b0dc50bbdf101e859be0e567bdad5bfcfa73'}
2025-07-08 00:02:57,256 DEBUG: --- Calculating Trade Plan for N/A 5m BUY ---
2025-07-08 00:02:57,256 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ONEUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922177255', 'recv_window': '120000', 'sign': 'f9bfd779d6a3d014244d5c3745b43335f2e302bc58ed10a40c047e5020f2b5fe'}
2025-07-08 00:02:57,256 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ONGUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922177255', 'recv_window': '120000', 'sign': '618fa7cd1cf0a815afe6c83a2966433901eb17ce0261e37633f4665ad51a6d84'}
2025-07-08 00:02:57,257 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:57,257 DEBUG: (N/A) Last Close: 0.0513, Raw ATR: 0.0002, Valid ATR: 0.0003
2025-07-08 00:02:57,258 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:57,259 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:57,259 DEBUG: (N/A) Fibs Available: True
2025-07-08 00:02:57,260 DEBUG: (N/A) Fib Levels: 0%=0.0507, 38.2%=0.0510, 61.8%=0.0512, 100%=0.0515, 161.8%=0.0521, -61.8%=0.0501
2025-07-08 00:02:57,260 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.05098234, 0.05118766), SL: 0.0506, TP: 0.0521
2025-07-08 00:02:57,260 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-07-08 00:02:57,260 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.05098234, 0.05118766), SL=0.0506, TP=0.0521
2025-07-08 00:02:57,261 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-07-08 00:02:57,261 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-07-08 00:02:57,261 INFO: N/A 5m: BUY signal, but price (0.0513) is outside entry zone (0.0510-0.0512). Penalty currently disabled for test.
2025-07-08 00:02:57,261 DEBUG: N/A 5m: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-07-08 00:02:57,262 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:57,262 DEBUG: Request Params: {'category': 'linear', 'symbol': 'OGNUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922177262', 'recv_window': '120000', 'sign': 'efc717919ea33a195469ddc3fd6a3936385cf3b5d9071d2615802a4eb4bdf138'}
2025-07-08 00:02:57,263 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:57,298 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=OLUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922176920&recv_window=120000&sign=4787a53edebed03b3507a07bcecc37d1d8b334618a5df9ac90c598473700ee10 HTTP/1.1" 200 None
2025-07-08 00:02:57,336 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=OMGUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922177019&recv_window=120000&sign=e02441e8da398048e14f4fc48921a14742fb887f442095ae1d17655c319b5529 HTTP/1.1" 200 125
2025-07-08 00:02:57,346 WARNING: No data returned for OMGUSDT on 5m. RC: 0, Msg: OK
2025-07-08 00:02:57,349 DEBUG: (OMGUSDT) Fetch data returned empty. Returning None.
2025-07-08 00:02:57,350 DEBUG: (OMGUSDT) analyze_symbol returning: Signal=None
2025-07-08 00:02:57,350 DEBUG: (ONTUSDT) Starting analysis for 5m...
2025-07-08 00:02:57,351 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:57,351 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ONTUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922177351', 'recv_window': '120000', 'sign': 'f91efa33fb90d9b1c385db3576bb6f8b7929e19af90c8c1d397c0ab46305b248'}
2025-07-08 00:02:57,352 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:57,366 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/1.50 vs Threshold: 3.5
2025-07-08 00:02:57,366 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-07-08 00:02:57,367 DEBUG: --- Calculating Trade Plan for N/A 5m BUY ---
2025-07-08 00:02:57,367 DEBUG: (N/A) Last Close: 0.0270, Raw ATR: 0.0001, Valid ATR: 0.0001
2025-07-08 00:02:57,367 DEBUG: (N/A) Fibs Available: True
2025-07-08 00:02:57,367 DEBUG: (N/A) Fib Levels: 0%=0.0267, 38.2%=0.0270, 61.8%=0.0272, 100%=0.0276, 161.8%=0.0281, -61.8%=0.0261
2025-07-08 00:02:57,367 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.02700762, 0.02722238), SL: 0.0266, TP: 0.0281
2025-07-08 00:02:57,367 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-07-08 00:02:57,368 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.02700762, 0.02722238), SL=0.0266, TP=0.0281
2025-07-08 00:02:57,368 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-07-08 00:02:57,368 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-07-08 00:02:57,368 DEBUG: (N/A) Price (0.0270) is INSIDE entry zone (0.0270-0.0272).
2025-07-08 00:02:57,368 DEBUG: N/A 5m: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: ''
2025-07-08 00:02:57,369 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:57,369 DEBUG: Request Params: {'category': 'linear', 'symbol': 'OLUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922177369', 'recv_window': '120000', 'sign': 'b7291831ee44786ff3ed89316068e796453749b22419623b2d022b1e57c3ab90'}
2025-07-08 00:02:57,370 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:57,483 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=NTRNUSDT HTTP/1.1" 200 189
2025-07-08 00:02:57,565 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=OGUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922177133&recv_window=120000&sign=23257da970b7720fad97e3a5121eafb116d32174044e391d150516ee553b7ae0 HTTP/1.1" 200 None
2025-07-08 00:02:57,568 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ONGUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922177255&recv_window=120000&sign=618fa7cd1cf0a815afe6c83a2966433901eb17ce0261e37633f4665ad51a6d84 HTTP/1.1" 200 None
2025-07-08 00:02:57,570 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ONEUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922177255&recv_window=120000&sign=f9bfd779d6a3d014244d5c3745b43335f2e302bc58ed10a40c047e5020f2b5fe HTTP/1.1" 200 None
2025-07-08 00:02:57,590 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=OGNUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922177262&recv_window=120000&sign=efc717919ea33a195469ddc3fd6a3936385cf3b5d9071d2615802a4eb4bdf138 HTTP/1.1" 200 None
2025-07-08 00:02:57,625 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=OMUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922177209&recv_window=120000&sign=3fa37c613e43503b4c6e6e74b77146f09d0dfcecc89b4d28d12ce1b2a04e24fb HTTP/1.1" 200 None
2025-07-08 00:02:57,631 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ONDOUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922177253&recv_window=120000&sign=d5220002b8206e51684cdf069d34b0dc50bbdf101e859be0e567bdad5bfcfa73 HTTP/1.1" 200 None
2025-07-08 00:02:57,650 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=OMNIUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922177177&recv_window=120000&sign=451278fa2ab299cfa945862bd82140af955a3cb06aef304cd72db99da14581bd HTTP/1.1" 200 None
2025-07-08 00:02:57,677 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=OLUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922177369&recv_window=120000&sign=b7291831ee44786ff3ed89316068e796453749b22419623b2d022b1e57c3ab90 HTTP/1.1" 200 None
2025-07-08 00:02:57,725 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ONTUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922177351&recv_window=120000&sign=f91efa33fb90d9b1c385db3576bb6f8b7929e19af90c8c1d397c0ab46305b248 HTTP/1.1" 200 None
2025-07-08 00:02:57,744 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=NTRNUSDT HTTP/1.1" 200 312
2025-07-08 00:02:57,836 DEBUG: (NTRNUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:02:57,859 DEBUG: (OPUSDT) Starting analysis for 5m...
2025-07-08 00:02:57,879 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:57,907 DEBUG: Request Params: {'category': 'linear', 'symbol': 'OPUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922177879', 'recv_window': '120000', 'sign': 'b2ee55a66a714fc632c06abc9d72f39b49c1d593ac001c996f8648ab7843cb37'}
2025-07-08 00:02:57,932 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:02:57,935 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/1.80 vs Threshold: 3.5
2025-07-08 00:02:57,965 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:57,973 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:57,999 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:58,007 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:02:58,026 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:58,068 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:58,071 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:02:58,075 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/1.50 vs Threshold: 3.5
2025-07-08 00:02:58,075 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:58,077 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ONGUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922178026', 'recv_window': '120000', 'sign': '796e096357169a9a10a13bfef3301fc57cd3c446f375758a947955a27499147e'}
2025-07-08 00:02:58,094 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:02:58,095 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ONEUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922178068', 'recv_window': '120000', 'sign': '8f93bf27b2a74cfd354d114cc195ce76a1e413d81caa0cedd593d0fcd6d251a5'}
2025-07-08 00:02:58,095 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:58,095 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-07-08 00:02:58,096 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:58,097 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:58,097 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:58,098 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:58,099 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:58,101 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ONTUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922178099', 'recv_window': '120000', 'sign': 'f6450e2658dae379da4e814fa11d1320ba71c554894caa18ddc0ff36102204e1'}
2025-07-08 00:02:58,099 DEBUG: Request Params: {'category': 'linear', 'symbol': 'OMUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922178096', 'recv_window': '120000', 'sign': 'd9373615d8724d84a273e8df828875ef5e4bdc58f627acf0ed43f46fe6fb84a6'}
2025-07-08 00:02:58,100 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:58,099 DEBUG: --- Calculating Trade Plan for N/A 5m BUY ---
2025-07-08 00:02:58,102 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:58,103 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:58,103 DEBUG: Request Params: {'category': 'linear', 'symbol': 'OMNIUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922178100', 'recv_window': '120000', 'sign': '17e26600d1ce6e0aa871a39a8ecaba9ac775e3ad98fbcad89200412295f965ee'}
2025-07-08 00:02:58,103 DEBUG: (N/A) Last Close: 0.7717, Raw ATR: 0.0021, Valid ATR: 0.0039
2025-07-08 00:02:58,105 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:58,105 DEBUG: (N/A) Fibs Available: True
2025-07-08 00:02:58,105 DEBUG: (N/A) Fib Levels: 0%=0.7673, 38.2%=0.7732, 61.8%=0.7768, 100%=0.7827, 161.8%=0.7922, -61.8%=0.7578
2025-07-08 00:02:58,105 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.7731828, 0.7768172), SL: 0.7663, TP: 0.7922
2025-07-08 00:02:58,105 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-07-08 00:02:58,106 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.7731828, 0.7768172), SL=0.7663, TP=0.7922
2025-07-08 00:02:58,106 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-07-08 00:02:58,106 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-07-08 00:02:58,106 DEBUG: (N/A) Price (0.7717) is INSIDE entry zone (0.7732-0.7768).
2025-07-08 00:02:58,106 DEBUG: N/A 5m: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: ''
2025-07-08 00:02:58,107 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:58,107 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ONDOUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922178107', 'recv_window': '120000', 'sign': 'a57356f4166c0d16eb544c78085b40330c863e62f3ea1c5841df6db62b04cb9d'}
2025-07-08 00:02:58,108 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:58,171 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=OGNUSDT HTTP/1.1" 200 192
2025-07-08 00:02:58,342 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=OLUSDT HTTP/1.1" 200 186
2025-07-08 00:02:58,375 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=OPUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922177879&recv_window=120000&sign=b2ee55a66a714fc632c06abc9d72f39b49c1d593ac001c996f8648ab7843cb37 HTTP/1.1" 200 None
2025-07-08 00:02:58,414 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=OMNIUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922178100&recv_window=120000&sign=17e26600d1ce6e0aa871a39a8ecaba9ac775e3ad98fbcad89200412295f965ee HTTP/1.1" 200 None
2025-07-08 00:02:58,420 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=OGNUSDT HTTP/1.1" 200 313
2025-07-08 00:02:58,425 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/1.00 vs Threshold: 3.5
2025-07-08 00:02:58,443 DEBUG: (OGNUSDT) analyze_symbol returning: Signal=BUY
2025-07-08 00:02:58,446 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:58,465 DEBUG: (ORBSUSDT) Starting analysis for 5m...
2025-07-08 00:02:58,472 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ONGUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922178026&recv_window=120000&sign=796e096357169a9a10a13bfef3301fc57cd3c446f375758a947955a27499147e HTTP/1.1" 200 None
2025-07-08 00:02:58,474 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ONEUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922178068&recv_window=120000&sign=8f93bf27b2a74cfd354d114cc195ce76a1e413d81caa0cedd593d0fcd6d251a5 HTTP/1.1" 200 None
2025-07-08 00:02:58,474 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=OMUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922178096&recv_window=120000&sign=d9373615d8724d84a273e8df828875ef5e4bdc58f627acf0ed43f46fe6fb84a6 HTTP/1.1" 200 None
2025-07-08 00:02:58,479 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:58,479 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ONDOUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922178107&recv_window=120000&sign=a57356f4166c0d16eb544c78085b40330c863e62f3ea1c5841df6db62b04cb9d HTTP/1.1" 200 None
2025-07-08 00:02:58,480 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ONTUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922178099&recv_window=120000&sign=f6450e2658dae379da4e814fa11d1320ba71c554894caa18ddc0ff36102204e1 HTTP/1.1" 200 None
2025-07-08 00:02:58,480 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:58,505 DEBUG: Request Params: {'category': 'linear', 'symbol': 'OPUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922178479', 'recv_window': '120000', 'sign': 'cc605ae09c6b258374aca8c137d2d53b9e4a9353fcbc473bfe5572e77126ace7'}
2025-07-08 00:02:58,524 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ORBSUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922178480', 'recv_window': '120000', 'sign': '11b24c2adbe4cba6a2599f68aca1ea99c933821a9fad460b9b3cd359d4e73a66'}
2025-07-08 00:02:58,563 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:58,591 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:58,596 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=OLUSDT HTTP/1.1" 200 312
2025-07-08 00:02:58,610 DEBUG: (OLUSDT) analyze_symbol returning: Signal=BUY
2025-07-08 00:02:58,613 DEBUG: (ORCAUSDT) Starting analysis for 5m...
2025-07-08 00:02:58,626 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:58,641 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ORCAUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922178626', 'recv_window': '120000', 'sign': '16e991d99d418a00373b7b37b2c1cc85a88a61fa5818a6f17c3bdf30e8879adc'}
2025-07-08 00:02:58,644 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:58,661 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=OGUSDT HTTP/1.1" 200 186
2025-07-08 00:02:58,725 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=OMNIUSDT HTTP/1.1" 200 189
2025-07-08 00:02:58,916 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=OPUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922178479&recv_window=120000&sign=cc605ae09c6b258374aca8c137d2d53b9e4a9353fcbc473bfe5572e77126ace7 HTTP/1.1" 200 None
2025-07-08 00:02:58,944 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=OGUSDT HTTP/1.1" 200 308
2025-07-08 00:02:58,944 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=ONGUSDT HTTP/1.1" 200 188
2025-07-08 00:02:58,959 DEBUG: (OGUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:02:58,960 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=ONEUSDT HTTP/1.1" 200 187
2025-07-08 00:02:58,965 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=OMUSDT HTTP/1.1" 200 187
2025-07-08 00:02:58,976 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=ONTUSDT HTTP/1.1" 200 192
2025-07-08 00:02:58,977 DEBUG: (ORDERUSDT) Starting analysis for 5m...
2025-07-08 00:02:58,984 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:58,985 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ORDERUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922178984', 'recv_window': '120000', 'sign': '41ef11889bbaa6ca9f3cf97f3dd7a8fb7777157f2af83d195f7a258041f97033'}
2025-07-08 00:02:58,986 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:58,986 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=OMNIUSDT HTTP/1.1" 200 312
2025-07-08 00:02:58,987 DEBUG: (OMNIUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:02:58,987 DEBUG: (ORDIUSDT) Starting analysis for 5m...
2025-07-08 00:02:58,987 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:58,987 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ORDIUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922178987', 'recv_window': '120000', 'sign': '0e144f5cea41c4d00bc1f718f76fa58c6e6015510c5bcfa7088241097446c6fb'}
2025-07-08 00:02:58,988 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:58,990 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=ONDOUSDT HTTP/1.1" 200 192
2025-07-08 00:02:58,999 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ORBSUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922178480&recv_window=120000&sign=11b24c2adbe4cba6a2599f68aca1ea99c933821a9fad460b9b3cd359d4e73a66 HTTP/1.1" 200 None
2025-07-08 00:02:59,034 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ORCAUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922178626&recv_window=120000&sign=16e991d99d418a00373b7b37b2c1cc85a88a61fa5818a6f17c3bdf30e8879adc HTTP/1.1" 200 None
2025-07-08 00:02:59,059 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:02:59,067 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:59,087 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:59,089 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ORBSUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922179087', 'recv_window': '120000', 'sign': 'bd095ad741942ab5014051411c4263081ce4295e71a15d7336fbce3359a63b30'}
2025-07-08 00:02:59,100 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:02:59,101 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:59,101 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:59,102 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:59,102 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ORCAUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922179102', 'recv_window': '120000', 'sign': '08f49c50a2c96ad661811a2f445b6571717f5370191419df25a52843ec84067f'}
2025-07-08 00:02:59,103 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:59,210 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=OPUSDT HTTP/1.1" 200 186
2025-07-08 00:02:59,210 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=ONEUSDT HTTP/1.1" 200 315
2025-07-08 00:02:59,211 DEBUG: (ONEUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:02:59,212 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=ONTUSDT HTTP/1.1" 200 313
2025-07-08 00:02:59,212 DEBUG: (OSMOUSDT) Starting analysis for 5m...
2025-07-08 00:02:59,213 DEBUG: (ONTUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:02:59,213 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:59,213 DEBUG: (OXTUSDT) Starting analysis for 5m...
2025-07-08 00:02:59,213 DEBUG: Request Params: {'category': 'linear', 'symbol': 'OSMOUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922179213', 'recv_window': '120000', 'sign': 'd70790804afd0eb7182d1a771fa6ff62e0bbaec4aac90275a66f822dfa8e2691'}
2025-07-08 00:02:59,214 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:59,215 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:59,215 DEBUG: Request Params: {'category': 'linear', 'symbol': 'OXTUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922179214', 'recv_window': '120000', 'sign': 'cbaf8a704152d1c0271c16e78ac0f5dd3fd8e015ab8dbc480a438c3ac0ea9d3d'}
2025-07-08 00:02:59,216 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:59,223 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=ONGUSDT HTTP/1.1" 200 311
2025-07-08 00:02:59,224 DEBUG: (ONGUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:02:59,224 DEBUG: (PAXGUSDT) Starting analysis for 5m...
2025-07-08 00:02:59,224 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:59,224 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PAXGUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922179224', 'recv_window': '120000', 'sign': '00f45e7b35223a66f6d9dc84e33cb558469eece7605d28bd97a6b98ac0826763'}
2025-07-08 00:02:59,225 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:59,231 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=OMUSDT HTTP/1.1" 200 312
2025-07-08 00:02:59,231 DEBUG: (OMUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:02:59,232 DEBUG: (PEAQUSDT) Starting analysis for 5m...
2025-07-08 00:02:59,232 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:59,232 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PEAQUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922179232', 'recv_window': '120000', 'sign': '04e7014e77c27e4bbc7f17fbb68da220e53eb42a621ab736ccc9265b828dca65'}
2025-07-08 00:02:59,233 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:59,240 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=ONDOUSDT HTTP/1.1" 200 314
2025-07-08 00:02:59,241 DEBUG: (ONDOUSDT) analyze_symbol returning: Signal=BUY
2025-07-08 00:02:59,241 DEBUG: (PENDLEUSDT) Starting analysis for 5m...
2025-07-08 00:02:59,241 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:59,241 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PENDLEUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922179241', 'recv_window': '120000', 'sign': '350bd97b5e0f923db17e90085ddc8cf6e8240188838f32972365bfc35e401a45'}
2025-07-08 00:02:59,242 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:59,311 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ORDIUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922178987&recv_window=120000&sign=0e144f5cea41c4d00bc1f718f76fa58c6e6015510c5bcfa7088241097446c6fb HTTP/1.1" 200 None
2025-07-08 00:02:59,360 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ORDERUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922178984&recv_window=120000&sign=41ef11889bbaa6ca9f3cf97f3dd7a8fb7777157f2af83d195f7a258041f97033 HTTP/1.1" 200 None
2025-07-08 00:02:59,389 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/1.80 vs Threshold: 3.5
2025-07-08 00:02:59,399 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:59,417 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ORBSUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922179087&recv_window=120000&sign=bd095ad741942ab5014051411c4263081ce4295e71a15d7336fbce3359a63b30 HTTP/1.1" 200 None
2025-07-08 00:02:59,417 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ORCAUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922179102&recv_window=120000&sign=08f49c50a2c96ad661811a2f445b6571717f5370191419df25a52843ec84067f HTTP/1.1" 200 None
2025-07-08 00:02:59,420 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:59,438 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=OPUSDT HTTP/1.1" 200 312
2025-07-08 00:02:59,438 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ORDIUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922179420', 'recv_window': '120000', 'sign': '06584b95274256ca02a2e11341e1b313d996f766fc2192fa252820733d56038b'}
2025-07-08 00:02:59,441 DEBUG: (OPUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:02:59,446 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:59,452 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/1.80 vs Threshold: 3.5
2025-07-08 00:02:59,453 DEBUG: (PENGUUSDT) Starting analysis for 5m...
2025-07-08 00:02:59,471 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:59,476 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:59,482 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:59,487 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PENGUUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922179476', 'recv_window': '120000', 'sign': '95626dedb319e561d18109d44f95fdaeb08067027662600521b485c88d8e09b4'}
2025-07-08 00:02:59,488 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ORDERUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922179482', 'recv_window': '120000', 'sign': '2b91297a26ee04a49372ef29c27fd10eba87bbef3836d2f5ee118277cb17115a'}
2025-07-08 00:02:59,526 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:59,538 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:59,589 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=OXTUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922179214&recv_window=120000&sign=cbaf8a704152d1c0271c16e78ac0f5dd3fd8e015ab8dbc480a438c3ac0ea9d3d HTTP/1.1" 200 None
2025-07-08 00:02:59,594 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=OSMOUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922179213&recv_window=120000&sign=d70790804afd0eb7182d1a771fa6ff62e0bbaec4aac90275a66f822dfa8e2691 HTTP/1.1" 200 None
2025-07-08 00:02:59,606 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PEAQUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922179232&recv_window=120000&sign=04e7014e77c27e4bbc7f17fbb68da220e53eb42a621ab736ccc9265b828dca65 HTTP/1.1" 200 None
2025-07-08 00:02:59,606 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PAXGUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922179224&recv_window=120000&sign=00f45e7b35223a66f6d9dc84e33cb558469eece7605d28bd97a6b98ac0826763 HTTP/1.1" 200 None
2025-07-08 00:02:59,615 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PENDLEUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922179241&recv_window=120000&sign=350bd97b5e0f923db17e90085ddc8cf6e8240188838f32972365bfc35e401a45 HTTP/1.1" 200 None
2025-07-08 00:02:59,731 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/1.50 vs Threshold: 3.5
2025-07-08 00:02:59,739 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:59,741 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:59,744 DEBUG: Request Params: {'category': 'linear', 'symbol': 'OXTUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922179741', 'recv_window': '120000', 'sign': '4626e7a85baf6af8e3357359f98a2adf8c571a9b74f05f5bf7a0505ecb453160'}
2025-07-08 00:02:59,759 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:59,783 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=ORCAUSDT HTTP/1.1" 200 190
2025-07-08 00:02:59,818 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=ORBSUSDT HTTP/1.1" 200 189
2025-07-08 00:02:59,832 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:02:59,839 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ORDIUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922179420&recv_window=120000&sign=06584b95274256ca02a2e11341e1b313d996f766fc2192fa252820733d56038b HTTP/1.1" 200 None
2025-07-08 00:02:59,852 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PENGUUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922179476&recv_window=120000&sign=95626dedb319e561d18109d44f95fdaeb08067027662600521b485c88d8e09b4 HTTP/1.1" 200 None
2025-07-08 00:02:59,859 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:59,865 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/1.50 vs Threshold: 3.5
2025-07-08 00:02:59,871 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:02:59,876 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/0.00 vs Threshold: 3.5
2025-07-08 00:02:59,877 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:59,879 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-07-08 00:02:59,888 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:59,897 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:02:59,908 DEBUG: Request Params: {'category': 'linear', 'symbol': 'OSMOUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922179877', 'recv_window': '120000', 'sign': '683926444d473204839ae3ae03069e06f3c3e5c7fb2af34ca52546025a6d0fb0'}
2025-07-08 00:02:59,913 DEBUG: --- Calculating Trade Plan for N/A 5m BUY ---
2025-07-08 00:02:59,918 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ORDERUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922179482&recv_window=120000&sign=2b91297a26ee04a49372ef29c27fd10eba87bbef3836d2f5ee118277cb17115a HTTP/1.1" 200 None
2025-07-08 00:02:59,920 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:59,924 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:02:59,954 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:02:59,958 DEBUG: (N/A) Last Close: 3.3580, Raw ATR: 0.0100, Valid ATR: 0.0168
2025-07-08 00:02:59,981 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:02:59,983 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PEAQUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922179920', 'recv_window': '120000', 'sign': '5a6c55e8da43adbb46f968c32d273b1eb75cf6ee646c497d4c9ceb98def858c8'}
2025-07-08 00:02:59,984 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PAXGUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922179924', 'recv_window': '120000', 'sign': '271a55c53dd3283511cac4ecafa5f84bf14492914427bc7608d10d968ede54a0'}
2025-07-08 00:02:59,999 DEBUG: (N/A) Fibs Available: True
2025-07-08 00:03:00,003 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:00,022 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:00,023 DEBUG: (N/A) Fib Levels: 0%=3.3399, 38.2%=3.3634, 61.8%=3.3779, 100%=3.4014, 161.8%=3.4394, -61.8%=3.3019
2025-07-08 00:03:00,029 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:00,032 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:00,046 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (3.3633930000000003, 3.377907), SL: 3.3357, TP: 3.4394
2025-07-08 00:03:00,046 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PENGUUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922180032', 'recv_window': '120000', 'sign': '485c78ecd2b882b7d9760b6a233671463efa691b611ba0684fc5e393071203af'}
2025-07-08 00:03:00,046 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-07-08 00:03:00,048 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:00,048 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(3.3633930000000003, 3.377907), SL=3.3357, TP=3.4394
2025-07-08 00:03:00,048 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-07-08 00:03:00,049 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-07-08 00:03:00,049 DEBUG: (N/A) Price (3.3580) is INSIDE entry zone (3.3634-3.3779).
2025-07-08 00:03:00,049 DEBUG: N/A 5m: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: ''
2025-07-08 00:03:00,050 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:00,050 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PENDLEUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922180050', 'recv_window': '120000', 'sign': '685a345a726165ad86a7163facd64c6a741ec3f3ff75817b77490b1c3f09c51c'}
2025-07-08 00:03:00,051 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:00,080 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=ORCAUSDT HTTP/1.1" 200 310
2025-07-08 00:03:00,080 DEBUG: (ORCAUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:00,081 DEBUG: (PEOPLEUSDT) Starting analysis for 5m...
2025-07-08 00:03:00,081 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:00,081 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PEOPLEUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922180081', 'recv_window': '120000', 'sign': '08f82ae3aaa6b317258ec661af132e322c3224542805a1ad9bc6ddafb150e64c'}
2025-07-08 00:03:00,083 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:00,115 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=ORBSUSDT HTTP/1.1" 200 316
2025-07-08 00:03:00,116 DEBUG: (ORBSUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:00,116 DEBUG: (PERPUSDT) Starting analysis for 5m...
2025-07-08 00:03:00,116 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:00,116 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PERPUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922180116', 'recv_window': '120000', 'sign': 'e65b2db85a0e3d22494ced7c0d095eb32a4205c72fdff95b9a110f0dcb112756'}
2025-07-08 00:03:00,117 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:00,135 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=OXTUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922179741&recv_window=120000&sign=4626e7a85baf6af8e3357359f98a2adf8c571a9b74f05f5bf7a0505ecb453160 HTTP/1.1" 200 None
2025-07-08 00:03:00,233 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=ORDIUSDT HTTP/1.1" 200 192
2025-07-08 00:03:00,290 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=ORDERUSDT HTTP/1.1" 200 189
2025-07-08 00:03:00,355 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=OSMOUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922179877&recv_window=120000&sign=683926444d473204839ae3ae03069e06f3c3e5c7fb2af34ca52546025a6d0fb0 HTTP/1.1" 200 None
2025-07-08 00:03:00,364 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PENDLEUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922180050&recv_window=120000&sign=685a345a726165ad86a7163facd64c6a741ec3f3ff75817b77490b1c3f09c51c HTTP/1.1" 200 None
2025-07-08 00:03:00,365 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PAXGUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922179924&recv_window=120000&sign=271a55c53dd3283511cac4ecafa5f84bf14492914427bc7608d10d968ede54a0 HTTP/1.1" 200 None
2025-07-08 00:03:00,366 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PEAQUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922179920&recv_window=120000&sign=5a6c55e8da43adbb46f968c32d273b1eb75cf6ee646c497d4c9ceb98def858c8 HTTP/1.1" 200 None
2025-07-08 00:03:00,405 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PEOPLEUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922180081&recv_window=120000&sign=08f82ae3aaa6b317258ec661af132e322c3224542805a1ad9bc6ddafb150e64c HTTP/1.1" 200 None
2025-07-08 00:03:00,421 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PENGUUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922180032&recv_window=120000&sign=485c78ecd2b882b7d9760b6a233671463efa691b611ba0684fc5e393071203af HTTP/1.1" 200 None
2025-07-08 00:03:00,435 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PERPUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922180116&recv_window=120000&sign=e65b2db85a0e3d22494ced7c0d095eb32a4205c72fdff95b9a110f0dcb112756 HTTP/1.1" 200 None
2025-07-08 00:03:00,446 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=OXTUSDT HTTP/1.1" 200 187
2025-07-08 00:03:00,498 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=ORDIUSDT HTTP/1.1" 200 312
2025-07-08 00:03:00,509 DEBUG: (ORDIUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:00,536 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=ORDERUSDT HTTP/1.1" 200 315
2025-07-08 00:03:00,539 DEBUG: (PHAUSDT) Starting analysis for 5m...
2025-07-08 00:03:00,556 DEBUG: (ORDERUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:00,580 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:00,603 DEBUG: (PHBUSDT) Starting analysis for 5m...
2025-07-08 00:03:00,628 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PHAUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922180580', 'recv_window': '120000', 'sign': '709bc3e6f6e661922cee3d8282ba8dc36a007d764e28b57ae36d6df4708cc4f1'}
2025-07-08 00:03:00,634 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:00,650 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:00,686 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PHBUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922180634', 'recv_window': '120000', 'sign': '23c5b4e81065cb594911a0e67d9578a45555e6f7c4789cfb0e64c232a0ff89b3'}
2025-07-08 00:03:00,706 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:00,713 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:00,718 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:00,718 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:00,719 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:00,719 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:00,720 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:00,720 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PEOPLEUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922180719', 'recv_window': '120000', 'sign': '187ee6ffd5e8c5afc7f3e5716da36c0a5600d3c993834dbb2ae6d25ea05c38ce'}
2025-07-08 00:03:00,720 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PERPUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922180720', 'recv_window': '120000', 'sign': '867115dd485b5cfe41e7882b3b626295a12af18c9dd308f4a20564d8555c6daa'}
2025-07-08 00:03:00,721 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:00,722 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:00,760 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=OXTUSDT HTTP/1.1" 200 313
2025-07-08 00:03:00,760 DEBUG: (OXTUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:00,761 DEBUG: (PIPPINUSDT) Starting analysis for 5m...
2025-07-08 00:03:00,761 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:00,761 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PIPPINUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922180761', 'recv_window': '120000', 'sign': '04f07bcf8e6b03b2c2107109d98127cda81a1e69ca24fe25fe65e21d9bf3927e'}
2025-07-08 00:03:00,762 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:00,785 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=OSMOUSDT HTTP/1.1" 200 192
2025-07-08 00:03:00,911 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=PAXGUSDT HTTP/1.1" 200 189
2025-07-08 00:03:00,914 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=PENDLEUSDT HTTP/1.1" 200 190
2025-07-08 00:03:00,919 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=PEAQUSDT HTTP/1.1" 200 188
2025-07-08 00:03:00,932 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=PENGUUSDT HTTP/1.1" 200 194
2025-07-08 00:03:01,026 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PERPUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922180720&recv_window=120000&sign=867115dd485b5cfe41e7882b3b626295a12af18c9dd308f4a20564d8555c6daa HTTP/1.1" 200 None
2025-07-08 00:03:01,036 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=OSMOUSDT HTTP/1.1" 200 312
2025-07-08 00:03:01,048 DEBUG: (OSMOUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:01,056 DEBUG: (PIRATEUSDT) Starting analysis for 5m...
2025-07-08 00:03:01,075 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:01,073 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PHAUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922180580&recv_window=120000&sign=709bc3e6f6e661922cee3d8282ba8dc36a007d764e28b57ae36d6df4708cc4f1 HTTP/1.1" 200 None
2025-07-08 00:03:01,082 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PIRATEUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922181075', 'recv_window': '120000', 'sign': '87d5ec5bd29aeb98e6c96fe9ac79dc9ddee2db3b8fa1c58a7ecec791dd67222b'}
2025-07-08 00:03:01,097 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PHBUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922180634&recv_window=120000&sign=23c5b4e81065cb594911a0e67d9578a45555e6f7c4789cfb0e64c232a0ff89b3 HTTP/1.1" 200 None
2025-07-08 00:03:01,098 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PEOPLEUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922180719&recv_window=120000&sign=187ee6ffd5e8c5afc7f3e5716da36c0a5600d3c993834dbb2ae6d25ea05c38ce HTTP/1.1" 200 None
2025-07-08 00:03:01,100 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:01,134 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PIPPINUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922180761&recv_window=120000&sign=04f07bcf8e6b03b2c2107109d98127cda81a1e69ca24fe25fe65e21d9bf3927e HTTP/1.1" 200 None
2025-07-08 00:03:01,148 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=PENDLEUSDT HTTP/1.1" 200 314
2025-07-08 00:03:01,158 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=PENGUUSDT HTTP/1.1" 200 319
2025-07-08 00:03:01,159 DEBUG: (PENDLEUSDT) analyze_symbol returning: Signal=BUY
2025-07-08 00:03:01,183 DEBUG: (PENGUUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:01,192 DEBUG: (PIXELUSDT) Starting analysis for 5m...
2025-07-08 00:03:01,200 DEBUG: (PLUMEUSDT) Starting analysis for 5m...
2025-07-08 00:03:01,220 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:01,269 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PIXELUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922181220', 'recv_window': '120000', 'sign': 'a0f5c7518d18044893d0fd08019c9977701de6d3dedb165db56351933616a4bd'}
2025-07-08 00:03:01,343 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:01,258 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:01,364 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PLUMEUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922181258', 'recv_window': '120000', 'sign': '1c42309a04904e219a6e6ef953e7732e44f43f30274c8dae059c74e762ca8326'}
2025-07-08 00:03:01,358 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:01,379 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:01,373 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:01,379 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:01,379 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=PERPUSDT HTTP/1.1" 200 189
2025-07-08 00:03:01,372 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.80 vs Threshold: 3.5
2025-07-08 00:03:01,382 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:01,380 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:01,383 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:01,384 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PHAUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922181383', 'recv_window': '120000', 'sign': '6c6a4b4bd9d385292d3c97b2df4174ddca0c4e603486e3482226830717ac6642'}
2025-07-08 00:03:01,380 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:01,385 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:01,385 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PHBUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922181380', 'recv_window': '120000', 'sign': 'd554124a06f14a712d0f0de98bd97cb48bdbad60330831560825f3e2031a72bf'}
2025-07-08 00:03:01,384 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:01,387 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:01,387 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PIPPINUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922181384', 'recv_window': '120000', 'sign': '27f203d2e095025001d1bd8d7723745e69172892cc566960952958bf16ab9434'}
2025-07-08 00:03:01,403 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:01,491 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=PAXGUSDT HTTP/1.1" 200 306
2025-07-08 00:03:01,492 DEBUG: (PAXGUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:01,492 DEBUG: (PNUTUSDT) Starting analysis for 5m...
2025-07-08 00:03:01,492 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:01,492 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PNUTUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922181492', 'recv_window': '120000', 'sign': '0a5f64a59c53ffda175b1ab3a51f0b3dc16d9671b85e0181b6e058bd7d4a3f08'}
2025-07-08 00:03:01,493 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:01,505 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PIRATEUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922181075&recv_window=120000&sign=87d5ec5bd29aeb98e6c96fe9ac79dc9ddee2db3b8fa1c58a7ecec791dd67222b HTTP/1.1" 200 128
2025-07-08 00:03:01,505 WARNING: No data returned for PIRATEUSDT on 5m. RC: 0, Msg: OK
2025-07-08 00:03:01,506 DEBUG: (PIRATEUSDT) Fetch data returned empty. Returning None.
2025-07-08 00:03:01,506 DEBUG: (PIRATEUSDT) analyze_symbol returning: Signal=None
2025-07-08 00:03:01,507 DEBUG: (POLUSDT) Starting analysis for 5m...
2025-07-08 00:03:01,507 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:01,507 DEBUG: Request Params: {'category': 'linear', 'symbol': 'POLUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922181507', 'recv_window': '120000', 'sign': '70d889d15230a7b44c2f2ab0c2158e381e5347cc5695378bc9628b559f4d3de1'}
2025-07-08 00:03:01,508 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:01,519 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=PEAQUSDT HTTP/1.1" 200 314
2025-07-08 00:03:01,519 DEBUG: (PEAQUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:01,519 DEBUG: (POLYXUSDT) Starting analysis for 5m...
2025-07-08 00:03:01,520 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:01,520 DEBUG: Request Params: {'category': 'linear', 'symbol': 'POLYXUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922181520', 'recv_window': '120000', 'sign': 'dc5ec9304f0cd48a69783b7853ab4e5f5db776d4e4d77f6fa10f7cc1a7bd1b76'}
2025-07-08 00:03:01,521 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:01,556 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=PEOPLEUSDT HTTP/1.1" 200 194
2025-07-08 00:03:01,635 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=PERPUSDT HTTP/1.1" 200 312
2025-07-08 00:03:01,635 DEBUG: (PERPUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:01,636 DEBUG: (PONKEUSDT) Starting analysis for 5m...
2025-07-08 00:03:01,636 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:01,636 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PONKEUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922181636', 'recv_window': '120000', 'sign': '555b0d11cdae106898603494d7b29b4959ef50fb06fc3ce0fdbf5b98f84ed563'}
2025-07-08 00:03:01,637 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:01,683 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PLUMEUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922181258&recv_window=120000&sign=1c42309a04904e219a6e6ef953e7732e44f43f30274c8dae059c74e762ca8326 HTTP/1.1" 200 None
2025-07-08 00:03:01,696 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PHAUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922181383&recv_window=120000&sign=6c6a4b4bd9d385292d3c97b2df4174ddca0c4e603486e3482226830717ac6642 HTTP/1.1" 200 None
2025-07-08 00:03:01,725 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PIXELUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922181220&recv_window=120000&sign=a0f5c7518d18044893d0fd08019c9977701de6d3dedb165db56351933616a4bd HTTP/1.1" 200 None
2025-07-08 00:03:01,748 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/0.00 vs Threshold: 3.5
2025-07-08 00:03:01,753 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:01,762 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:01,763 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PLUMEUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922181761', 'recv_window': '120000', 'sign': '3e2b542b11dfb5ad5cc69a2cfb09a217ddbdb98394d6d0c8a814c41a44fb9f45'}
2025-07-08 00:03:01,768 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PHBUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922181380&recv_window=120000&sign=d554124a06f14a712d0f0de98bd97cb48bdbad60330831560825f3e2031a72bf HTTP/1.1" 200 None
2025-07-08 00:03:01,781 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:01,783 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PIPPINUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922181384&recv_window=120000&sign=27f203d2e095025001d1bd8d7723745e69172892cc566960952958bf16ab9434 HTTP/1.1" 200 None
2025-07-08 00:03:01,784 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=PEOPLEUSDT HTTP/1.1" 200 318
2025-07-08 00:03:01,823 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=POLUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922181507&recv_window=120000&sign=70d889d15230a7b44c2f2ab0c2158e381e5347cc5695378bc9628b559f4d3de1 HTTP/1.1" 200 None
2025-07-08 00:03:01,823 DEBUG: (PEOPLEUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:01,847 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=POLYXUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922181520&recv_window=120000&sign=dc5ec9304f0cd48a69783b7853ab4e5f5db776d4e4d77f6fa10f7cc1a7bd1b76 HTTP/1.1" 200 None
2025-07-08 00:03:01,852 DEBUG: (POPCATUSDT) Starting analysis for 5m...
2025-07-08 00:03:01,876 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PNUTUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922181492&recv_window=120000&sign=0a5f64a59c53ffda175b1ab3a51f0b3dc16d9671b85e0181b6e058bd7d4a3f08 HTTP/1.1" 200 None
2025-07-08 00:03:01,899 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:01,944 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PONKEUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922181636&recv_window=120000&sign=555b0d11cdae106898603494d7b29b4959ef50fb06fc3ce0fdbf5b98f84ed563 HTTP/1.1" 200 None
2025-07-08 00:03:01,957 DEBUG: Request Params: {'category': 'linear', 'symbol': 'POPCATUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922181899', 'recv_window': '120000', 'sign': 'b3ba535391809fa32e5a05c3b0b1778ce34409c5622e3c16c3df2ef00f8433b0'}
2025-07-08 00:03:01,971 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/1.80 vs Threshold: 3.5
2025-07-08 00:03:02,019 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:02,022 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:02,027 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:02,082 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PIXELUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922182027', 'recv_window': '120000', 'sign': '506fe2e66efa64d69d4a85648ed0909211d4338892de1c773e86224cb298a49a'}
2025-07-08 00:03:02,126 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=PHAUSDT HTTP/1.1" 200 188
2025-07-08 00:03:02,132 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:02,135 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.50/1.50 vs Threshold: 3.5
2025-07-08 00:03:02,187 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.50/9.0 | Final Confidence: 27.8% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:02,196 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:02,202 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:02,202 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PLUMEUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922181761&recv_window=120000&sign=3e2b542b11dfb5ad5cc69a2cfb09a217ddbdb98394d6d0c8a814c41a44fb9f45 HTTP/1.1" 200 None
2025-07-08 00:03:02,204 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.00 vs Threshold: 3.5
2025-07-08 00:03:02,204 DEBUG: Request Params: {'category': 'linear', 'symbol': 'POLUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922182196', 'recv_window': '120000', 'sign': 'f1f678b7d111448ca259d202e8babebbdbbf25a0998ac9bfdef70297dfe14771'}
2025-07-08 00:03:02,212 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.50/1.00 vs Threshold: 3.5
2025-07-08 00:03:02,212 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:02,214 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:02,231 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.50/9.0 | Final Confidence: 27.8% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:02,231 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:02,234 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:02,252 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:02,260 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:02,266 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PNUTUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922182234', 'recv_window': '120000', 'sign': '23b9f84f31fe0d3d2593d5f1678ade83850cd9e1825a284df10f592742b4052d'}
2025-07-08 00:03:02,266 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PONKEUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922182252', 'recv_window': '120000', 'sign': '8d5cc395ae88a94bee57da8bab7fe33d0a49141ff3b21c2d32f261ea04108372'}
2025-07-08 00:03:02,266 DEBUG: Request Params: {'category': 'linear', 'symbol': 'POLYXUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922182259', 'recv_window': '120000', 'sign': '70826ead348dbedcf239d6f4ac2ebdc4bcef43dab03589a9d5139911158874fb'}
2025-07-08 00:03:02,267 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:02,268 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:02,269 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:02,375 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=PHAUSDT HTTP/1.1" 200 313
2025-07-08 00:03:02,376 DEBUG: (PHAUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:02,376 DEBUG: (PORTALUSDT) Starting analysis for 5m...
2025-07-08 00:03:02,376 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:02,377 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PORTALUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922182376', 'recv_window': '120000', 'sign': '9421b4fef23ce6eb9668d722bab8c9550c2a4a3c05e1f74a639f544a89baeccd'}
2025-07-08 00:03:02,378 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:02,395 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=PHBUSDT HTTP/1.1" 200 187
2025-07-08 00:03:02,434 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=PIPPINUSDT HTTP/1.1" 200 191
2025-07-08 00:03:02,457 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=POPCATUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922181899&recv_window=120000&sign=b3ba535391809fa32e5a05c3b0b1778ce34409c5622e3c16c3df2ef00f8433b0 HTTP/1.1" 200 None
2025-07-08 00:03:02,502 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=PLUMEUSDT HTTP/1.1" 200 194
2025-07-08 00:03:02,555 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/0.50 vs Threshold: 3.5
2025-07-08 00:03:02,556 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:02,556 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:02,557 DEBUG: Request Params: {'category': 'linear', 'symbol': 'POPCATUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922182556', 'recv_window': '120000', 'sign': '51a1e62bdd248b7a8723eef07a95dd21e24db456e84168685b74ab658107f9f7'}
2025-07-08 00:03:02,558 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:02,561 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PIXELUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922182027&recv_window=120000&sign=506fe2e66efa64d69d4a85648ed0909211d4338892de1c773e86224cb298a49a HTTP/1.1" 200 None
2025-07-08 00:03:02,582 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PNUTUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922182234&recv_window=120000&sign=23b9f84f31fe0d3d2593d5f1678ade83850cd9e1825a284df10f592742b4052d HTTP/1.1" 200 None
2025-07-08 00:03:02,637 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=POLUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922182196&recv_window=120000&sign=f1f678b7d111448ca259d202e8babebbdbbf25a0998ac9bfdef70297dfe14771 HTTP/1.1" 200 None
2025-07-08 00:03:02,638 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=POLYXUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922182259&recv_window=120000&sign=70826ead348dbedcf239d6f4ac2ebdc4bcef43dab03589a9d5139911158874fb HTTP/1.1" 200 None
2025-07-08 00:03:02,645 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=PHBUSDT HTTP/1.1" 200 311
2025-07-08 00:03:02,652 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PONKEUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922182252&recv_window=120000&sign=8d5cc395ae88a94bee57da8bab7fe33d0a49141ff3b21c2d32f261ea04108372 HTTP/1.1" 200 None
2025-07-08 00:03:02,663 DEBUG: (PHBUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:02,699 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=PIPPINUSDT HTTP/1.1" 200 316
2025-07-08 00:03:02,705 DEBUG: (POWRUSDT) Starting analysis for 5m...
2025-07-08 00:03:02,716 DEBUG: (PIPPINUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:02,733 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=PLUMEUSDT HTTP/1.1" 200 317
2025-07-08 00:03:02,749 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:02,755 DEBUG: (PRCLUSDT) Starting analysis for 5m...
2025-07-08 00:03:02,758 DEBUG: (PLUMEUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:02,783 DEBUG: Request Params: {'category': 'linear', 'symbol': 'POWRUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922182749', 'recv_window': '120000', 'sign': 'a3d01cb33fdbbb7ffb8c21556e8185d24de7e146103f18317ff0b49703fb70bc'}
2025-07-08 00:03:02,783 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PORTALUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922182376&recv_window=120000&sign=9421b4fef23ce6eb9668d722bab8c9550c2a4a3c05e1f74a639f544a89baeccd HTTP/1.1" 200 None
2025-07-08 00:03:02,796 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:02,841 DEBUG: (PRIMEUSDT) Starting analysis for 5m...
2025-07-08 00:03:02,853 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:02,865 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PRCLUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922182796', 'recv_window': '120000', 'sign': '1dc1277afaa9f23aeb151ec52ae00d9f96ec7075d929a66660a262b4160ae35d'}
2025-07-08 00:03:02,874 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:02,880 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=POPCATUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922182556&recv_window=120000&sign=51a1e62bdd248b7a8723eef07a95dd21e24db456e84168685b74ab658107f9f7 HTTP/1.1" 200 None
2025-07-08 00:03:02,895 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:02,896 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PRIMEUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922182874', 'recv_window': '120000', 'sign': '3ba7614a3a87462e9ebebbeca3210dacd9b675ce4ac4251426d7067638be15ed'}
2025-07-08 00:03:02,937 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.50/0.00 vs Threshold: 3.5
2025-07-08 00:03:02,938 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.50/9.0 | Final Confidence: 27.8% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:02,938 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:02,948 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:02,952 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=PIXELUSDT HTTP/1.1" 200 190
2025-07-08 00:03:02,970 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PORTALUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922182948', 'recv_window': '120000', 'sign': 'fdba8302d9f949226405555290202fae79785521ff60f1a7b0e3337c200082a6'}
2025-07-08 00:03:02,986 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:03,054 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=PNUTUSDT HTTP/1.1" 200 193
2025-07-08 00:03:03,104 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=POLUSDT HTTP/1.1" 200 188
2025-07-08 00:03:03,111 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=PONKEUSDT HTTP/1.1" 200 190
2025-07-08 00:03:03,113 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=POLYXUSDT HTTP/1.1" 200 190
2025-07-08 00:03:03,198 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=POWRUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922182749&recv_window=120000&sign=a3d01cb33fdbbb7ffb8c21556e8185d24de7e146103f18317ff0b49703fb70bc HTTP/1.1" 200 None
2025-07-08 00:03:03,237 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=POPCATUSDT HTTP/1.1" 200 191
2025-07-08 00:03:03,237 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=PIXELUSDT HTTP/1.1" 200 315
2025-07-08 00:03:03,237 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PRCLUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922182796&recv_window=120000&sign=1dc1277afaa9f23aeb151ec52ae00d9f96ec7075d929a66660a262b4160ae35d HTTP/1.1" 200 None
2025-07-08 00:03:03,252 DEBUG: (PIXELUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:03,256 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/1.80 vs Threshold: 3.5
2025-07-08 00:03:03,266 DEBUG: (PROMUSDT) Starting analysis for 5m...
2025-07-08 00:03:03,275 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:03,290 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=PNUTUSDT HTTP/1.1" 200 314
2025-07-08 00:03:03,290 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PORTALUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922182948&recv_window=120000&sign=fdba8302d9f949226405555290202fae79785521ff60f1a7b0e3337c200082a6 HTTP/1.1" 200 None
2025-07-08 00:03:03,296 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:03,304 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:03,308 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/0.00 vs Threshold: 3.5
2025-07-08 00:03:03,335 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:03,310 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PROMUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922183296', 'recv_window': '120000', 'sign': '8c2bd768545796ce60e1b30d367d9edba86cac96fcc08db35f23eb22a489e587'}
2025-07-08 00:03:03,322 DEBUG: Request Params: {'category': 'linear', 'symbol': 'POWRUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922183304', 'recv_window': '120000', 'sign': '45531556689b3d891ef0cf74b508cab6e50db46a224131c9a9f8458d2748e63a'}
2025-07-08 00:03:03,323 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PRIMEUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922182874&recv_window=120000&sign=3ba7614a3a87462e9ebebbeca3210dacd9b675ce4ac4251426d7067638be15ed HTTP/1.1" 200 None
2025-07-08 00:03:03,330 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=POLUSDT HTTP/1.1" 200 313
2025-07-08 00:03:03,309 DEBUG: (PNUTUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:03,350 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=PONKEUSDT HTTP/1.1" 200 315
2025-07-08 00:03:03,354 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:03,364 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:03,365 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:03,374 DEBUG: (POLUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:03,377 DEBUG: (PROSUSDT) Starting analysis for 5m...
2025-07-08 00:03:03,391 DEBUG: (PONKEUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:03,396 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PRCLUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922183354', 'recv_window': '120000', 'sign': 'c8a1ae500a03e0f1a55f6fa168d50c0d2af2f2bf088c0358ea100871771c0464'}
2025-07-08 00:03:03,426 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/1.00 vs Threshold: 3.5
2025-07-08 00:03:03,426 DEBUG: (PUFFERUSDT) Starting analysis for 5m...
2025-07-08 00:03:03,426 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:03,427 DEBUG: (PYRUSDT) Starting analysis for 5m...
2025-07-08 00:03:03,428 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:03,428 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:03,428 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:03,428 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PROSUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922183426', 'recv_window': '120000', 'sign': '8434f779be26b93596b13517d86f589394bbdf7f59ff7e63e8b89d8dbfdb656d'}
2025-07-08 00:03:03,429 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:03,429 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:03,430 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PUFFERUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922183428', 'recv_window': '120000', 'sign': '593bfb368adf5e47d4cde07f3960174bc7a7e18de1f238211c8d4290e8d152dd'}
2025-07-08 00:03:03,430 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:03,431 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PYRUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922183428', 'recv_window': '120000', 'sign': '6732b290a20e30f74f784d3a8dbacd4b5272904b1cf569df0ce02bc9f6e7b7ac'}
2025-07-08 00:03:03,431 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PRIMEUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922183429', 'recv_window': '120000', 'sign': '8c829505d94d840bba255b1e0fca82a2c28a6dd565953dfc9260fe3358f2c07d'}
2025-07-08 00:03:03,432 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:03,433 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:03,434 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:03,499 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=POPCATUSDT HTTP/1.1" 200 318
2025-07-08 00:03:03,500 DEBUG: (POPCATUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:03,500 DEBUG: (PYTHUSDT) Starting analysis for 5m...
2025-07-08 00:03:03,500 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:03,501 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PYTHUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922183500', 'recv_window': '120000', 'sign': '2af83b39e95ea51ca32ffe675a66a28d694da1280287e04f6115dd344482c41d'}
2025-07-08 00:03:03,501 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:03,596 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=PORTALUSDT HTTP/1.1" 200 191
2025-07-08 00:03:03,712 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=POLYXUSDT HTTP/1.1" 200 313
2025-07-08 00:03:03,712 DEBUG: (POLYXUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:03,712 DEBUG: (QIUSDT) Starting analysis for 5m...
2025-07-08 00:03:03,712 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:03,712 DEBUG: Request Params: {'category': 'linear', 'symbol': 'QIUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922183712', 'recv_window': '120000', 'sign': 'dd8cb8c068f906645816ed75806432cd1f64291db19657b95fb90c7336683080'}
2025-07-08 00:03:03,713 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:03,734 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PROMUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922183296&recv_window=120000&sign=8c2bd768545796ce60e1b30d367d9edba86cac96fcc08db35f23eb22a489e587 HTTP/1.1" 200 None
2025-07-08 00:03:03,737 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PRCLUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922183354&recv_window=120000&sign=c8a1ae500a03e0f1a55f6fa168d50c0d2af2f2bf088c0358ea100871771c0464 HTTP/1.1" 200 None
2025-07-08 00:03:03,741 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PROSUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922183426&recv_window=120000&sign=8434f779be26b93596b13517d86f589394bbdf7f59ff7e63e8b89d8dbfdb656d HTTP/1.1" 200 126
2025-07-08 00:03:03,761 WARNING: No data returned for PROSUSDT on 5m. RC: 0, Msg: OK
2025-07-08 00:03:03,772 DEBUG: (PROSUSDT) Fetch data returned empty. Returning None.
2025-07-08 00:03:03,781 DEBUG: (PROSUSDT) analyze_symbol returning: Signal=None
2025-07-08 00:03:03,786 DEBUG: (QNTUSDT) Starting analysis for 5m...
2025-07-08 00:03:03,792 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:03,802 DEBUG: Request Params: {'category': 'linear', 'symbol': 'QNTUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922183792', 'recv_window': '120000', 'sign': '47efb86a64ac5f402d2830fbd4143847cca1aa27fd7d7e69620299a61d0c0db3'}
2025-07-08 00:03:03,804 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PUFFERUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922183428&recv_window=120000&sign=593bfb368adf5e47d4cde07f3960174bc7a7e18de1f238211c8d4290e8d152dd HTTP/1.1" 200 None
2025-07-08 00:03:03,807 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PRIMEUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922183429&recv_window=120000&sign=8c829505d94d840bba255b1e0fca82a2c28a6dd565953dfc9260fe3358f2c07d HTTP/1.1" 200 None
2025-07-08 00:03:03,809 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PYTHUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922183500&recv_window=120000&sign=2af83b39e95ea51ca32ffe675a66a28d694da1280287e04f6115dd344482c41d HTTP/1.1" 200 None
2025-07-08 00:03:03,809 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:03,813 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/0.50 vs Threshold: 3.5
2025-07-08 00:03:03,814 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PYRUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922183428&recv_window=120000&sign=6732b290a20e30f74f784d3a8dbacd4b5272904b1cf569df0ce02bc9f6e7b7ac HTTP/1.1" 200 None
2025-07-08 00:03:03,818 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=POWRUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922183304&recv_window=120000&sign=45531556689b3d891ef0cf74b508cab6e50db46a224131c9a9f8458d2748e63a HTTP/1.1" 200 None
2025-07-08 00:03:03,835 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=PORTALUSDT HTTP/1.1" 200 316
2025-07-08 00:03:03,863 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:03,889 DEBUG: (PORTALUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:03,904 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:03,918 DEBUG: (QTUMUSDT) Starting analysis for 5m...
2025-07-08 00:03:03,928 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PROMUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922183904', 'recv_window': '120000', 'sign': '371f225f322482ca1a17f89a9f0453bd3421c925ba57ae8b2d842d9a28a720a3'}
2025-07-08 00:03:03,938 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:03,965 DEBUG: Request Params: {'category': 'linear', 'symbol': 'QTUMUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922183938', 'recv_window': '120000', 'sign': 'cae4362f7ce9d8a75639738b8861be5df05556823399bc0f1208044056915c02'}
2025-07-08 00:03:03,966 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:03,979 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:04,042 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=QIUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922183712&recv_window=120000&sign=dd8cb8c068f906645816ed75806432cd1f64291db19657b95fb90c7336683080 HTTP/1.1" 200 None
2025-07-08 00:03:04,146 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=PRCLUSDT HTTP/1.1" 200 188
2025-07-08 00:03:04,178 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:04,178 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:04,179 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:04,179 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PUFFERUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922184179', 'recv_window': '120000', 'sign': 'bc281eac52346202c25379ead899dc6bfbb409da5d3d464546383693787f8af2'}
2025-07-08 00:03:04,180 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:04,193 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=QNTUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922183792&recv_window=120000&sign=47efb86a64ac5f402d2830fbd4143847cca1aa27fd7d7e69620299a61d0c0db3 HTTP/1.1" 200 None
2025-07-08 00:03:04,265 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:04,282 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:04,283 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:04,316 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:04,330 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:04,331 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:04,317 DEBUG: Request Params: {'category': 'linear', 'symbol': 'QIUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922184283', 'recv_window': '120000', 'sign': 'a81902da1d5104926305006edc7b5534f0a8e54a64f04deaf12d513cc42de8c1'}
2025-07-08 00:03:04,336 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:04,343 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PROMUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922183904&recv_window=120000&sign=371f225f322482ca1a17f89a9f0453bd3421c925ba57ae8b2d842d9a28a720a3 HTTP/1.1" 200 None
2025-07-08 00:03:04,330 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:04,331 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PYRUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922184331', 'recv_window': '120000', 'sign': '14619207ba9ff85b50b304b0920a2426bf29b0c67d7e1f5a3cd508c4cd4c73ce'}
2025-07-08 00:03:04,385 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=PRCLUSDT HTTP/1.1" 200 314
2025-07-08 00:03:04,395 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:04,407 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:04,411 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:04,404 DEBUG: (PRCLUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:04,395 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:04,404 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:04,412 DEBUG: Request Params: {'category': 'linear', 'symbol': 'PYTHUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922184411', 'recv_window': '120000', 'sign': '9d890b2ecd908dd3c159b78447d9344ea18c24d9b93ce405fda421186e3643cf'}
2025-07-08 00:03:04,412 DEBUG: (QUICKUSDT) Starting analysis for 5m...
2025-07-08 00:03:04,414 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:04,414 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=QTUMUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922183938&recv_window=120000&sign=cae4362f7ce9d8a75639738b8861be5df05556823399bc0f1208044056915c02 HTTP/1.1" 200 None
2025-07-08 00:03:04,415 DEBUG: Request Params: {'category': 'linear', 'symbol': 'QUICKUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922184414', 'recv_window': '120000', 'sign': '9adf622e6484b5f57e290310367155bcb453a720e614f422f41e38d19776cd72'}
2025-07-08 00:03:04,413 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:04,468 DEBUG: Request Params: {'category': 'linear', 'symbol': 'QNTUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922184413', 'recv_window': '120000', 'sign': '92cbfd2b8439fe033f6f79d678965abb2b3aa91c5d54cbd6b8d2ae6eb6f90a87'}
2025-07-08 00:03:04,470 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:04,420 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:04,423 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:04,504 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:04,504 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:04,505 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:04,505 DEBUG: Request Params: {'category': 'linear', 'symbol': 'QTUMUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922184505', 'recv_window': '120000', 'sign': '3015bfe4bd03b49e21258f0744911004e2a78206e8eb6ff47ff1f9efb1bed317'}
2025-07-08 00:03:04,506 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:04,526 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PUFFERUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922184179&recv_window=120000&sign=bc281eac52346202c25379ead899dc6bfbb409da5d3d464546383693787f8af2 HTTP/1.1" 200 None
2025-07-08 00:03:04,576 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=PRIMEUSDT HTTP/1.1" 200 189
2025-07-08 00:03:04,637 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=PROMUSDT HTTP/1.1" 200 193
2025-07-08 00:03:04,732 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PYRUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922184331&recv_window=120000&sign=14619207ba9ff85b50b304b0920a2426bf29b0c67d7e1f5a3cd508c4cd4c73ce HTTP/1.1" 200 None
2025-07-08 00:03:04,792 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=QUICKUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922184414&recv_window=120000&sign=9adf622e6484b5f57e290310367155bcb453a720e614f422f41e38d19776cd72 HTTP/1.1" 200 None
2025-07-08 00:03:04,792 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=QNTUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922184413&recv_window=120000&sign=92cbfd2b8439fe033f6f79d678965abb2b3aa91c5d54cbd6b8d2ae6eb6f90a87 HTTP/1.1" 200 None
2025-07-08 00:03:04,819 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=PUFFERUSDT HTTP/1.1" 200 195
2025-07-08 00:03:04,841 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=PRIMEUSDT HTTP/1.1" 200 311
2025-07-08 00:03:04,856 DEBUG: (PRIMEUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:04,867 DEBUG: (RADUSDT) Starting analysis for 5m...
2025-07-08 00:03:04,867 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=PROMUSDT HTTP/1.1" 200 310
2025-07-08 00:03:04,868 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:04,884 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=QTUMUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922184505&recv_window=120000&sign=3015bfe4bd03b49e21258f0744911004e2a78206e8eb6ff47ff1f9efb1bed317 HTTP/1.1" 200 None
2025-07-08 00:03:04,900 DEBUG: (PROMUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:04,912 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RADUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922184868', 'recv_window': '120000', 'sign': '8d6334f56594bacf0beb752f5801f4104b64e16978a6ec80b12de239e51d22b4'}
2025-07-08 00:03:04,924 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=POWRUSDT HTTP/1.1" 200 193
2025-07-08 00:03:04,930 DEBUG: (RAREUSDT) Starting analysis for 5m...
2025-07-08 00:03:04,936 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/1.00 vs Threshold: 3.5
2025-07-08 00:03:04,938 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:04,946 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:04,960 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:04,991 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RAREUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922184946', 'recv_window': '120000', 'sign': '44c9e589deb17c38e237acd110c96760ecd9bf752d86f3e6bbdbe6dd362ba505'}
2025-07-08 00:03:04,995 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:04,997 DEBUG: Request Params: {'category': 'linear', 'symbol': 'QUICKUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922184995', 'recv_window': '120000', 'sign': 'cca1987317d4068580ecb8fb157753ce8bf364fd4e7b3168159a3b4a3e97a3c4'}
2025-07-08 00:03:04,997 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:04,998 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:05,062 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=PUFFERUSDT HTTP/1.1" 200 316
2025-07-08 00:03:05,062 DEBUG: (PUFFERUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:05,062 DEBUG: (RAYDIUMUSDT) Starting analysis for 5m...
2025-07-08 00:03:05,063 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:05,063 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RAYDIUMUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922185063', 'recv_window': '120000', 'sign': 'baac4ea4b7d6caad28c8d68e8a9d108687988e182118dbc5a6ae27e36edeb4bf'}
2025-07-08 00:03:05,064 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:05,087 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=PYRUSDT HTTP/1.1" 200 187
2025-07-08 00:03:05,148 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=PYTHUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922184411&recv_window=120000&sign=9d890b2ecd908dd3c159b78447d9344ea18c24d9b93ce405fda421186e3643cf HTTP/1.1" 200 None
2025-07-08 00:03:05,153 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=QIUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922184283&recv_window=120000&sign=a81902da1d5104926305006edc7b5534f0a8e54a64f04deaf12d513cc42de8c1 HTTP/1.1" 200 None
2025-07-08 00:03:05,164 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=QNTUSDT HTTP/1.1" 200 187
2025-07-08 00:03:05,202 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=POWRUSDT HTTP/1.1" 200 312
2025-07-08 00:03:05,215 DEBUG: (POWRUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:05,218 DEBUG: (RDNTUSDT) Starting analysis for 5m...
2025-07-08 00:03:05,227 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:05,237 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RDNTUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922185226', 'recv_window': '120000', 'sign': '63633835d4881faa39ee2a92cc1e137111f0c2239e890c3bb8074cdc139fb871'}
2025-07-08 00:03:05,249 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:05,256 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=QTUMUSDT HTTP/1.1" 200 188
2025-07-08 00:03:05,349 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RADUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922184868&recv_window=120000&sign=8d6334f56594bacf0beb752f5801f4104b64e16978a6ec80b12de239e51d22b4 HTTP/1.1" 200 None
2025-07-08 00:03:05,374 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RAYDIUMUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922185063&recv_window=120000&sign=baac4ea4b7d6caad28c8d68e8a9d108687988e182118dbc5a6ae27e36edeb4bf HTTP/1.1" 200 None
2025-07-08 00:03:05,382 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=QUICKUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922184995&recv_window=120000&sign=cca1987317d4068580ecb8fb157753ce8bf364fd4e7b3168159a3b4a3e97a3c4 HTTP/1.1" 200 None
2025-07-08 00:03:05,383 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RAREUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922184946&recv_window=120000&sign=44c9e589deb17c38e237acd110c96760ecd9bf752d86f3e6bbdbe6dd362ba505 HTTP/1.1" 200 None
2025-07-08 00:03:05,407 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=QNTUSDT HTTP/1.1" 200 307
2025-07-08 00:03:05,469 DEBUG: (QNTUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:05,470 DEBUG: (RENDERUSDT) Starting analysis for 5m...
2025-07-08 00:03:05,498 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:05,503 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=QIUSDT HTTP/1.1" 200 186
2025-07-08 00:03:05,503 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=PYTHUSDT HTTP/1.1" 200 189
2025-07-08 00:03:05,508 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RENDERUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922185498', 'recv_window': '120000', 'sign': '1022b998294691ff6d131cc327c7f9bc006c4e460179abcd778e60d6c08dcade'}
2025-07-08 00:03:05,526 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/0.00 vs Threshold: 3.5
2025-07-08 00:03:05,613 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:05,588 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.50/0.50 vs Threshold: 3.5
2025-07-08 00:03:05,624 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.50/9.0 | Final Confidence: 27.8% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:05,550 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=QTUMUSDT HTTP/1.1" 200 312
2025-07-08 00:03:05,614 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:05,618 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:05,611 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:05,625 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:05,625 DEBUG: (QTUMUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:05,626 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RADUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922185618', 'recv_window': '120000', 'sign': '9b019f0b07119e314a988b8a86c4476d2460a16be797e7236aadd1538238de86'}
2025-07-08 00:03:05,626 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:05,626 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RAYDIUMUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922185625', 'recv_window': '120000', 'sign': '7ed51be7c0a9de8e7b7485cdbcb6a7b648b1bc75ad8ec8fdd54819db2a4e66ae'}
2025-07-08 00:03:05,626 DEBUG: (RENUSDT) Starting analysis for 5m...
2025-07-08 00:03:05,629 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:05,630 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RENUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922185629', 'recv_window': '120000', 'sign': 'e7fdfc79b7a53db2ef833c5dbeaee895b20aec04520ec8aec8c20bd817fd4daf'}
2025-07-08 00:03:05,629 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:05,627 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:05,628 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:05,631 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:05,631 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RAREUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922185628', 'recv_window': '120000', 'sign': 'f1104a74b2900f16cbf41885e03b747a743fe34c6e5777fc29d3bfe241f997fe'}
2025-07-08 00:03:05,632 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:05,655 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RDNTUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922185226&recv_window=120000&sign=63633835d4881faa39ee2a92cc1e137111f0c2239e890c3bb8074cdc139fb871 HTTP/1.1" 200 None
2025-07-08 00:03:05,674 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=PYRUSDT HTTP/1.1" 200 309
2025-07-08 00:03:05,684 DEBUG: (PYRUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:05,704 DEBUG: (REQUSDT) Starting analysis for 5m...
2025-07-08 00:03:05,707 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:05,709 DEBUG: Request Params: {'category': 'linear', 'symbol': 'REQUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922185707', 'recv_window': '120000', 'sign': 'c67fcb995b185993a9ccdbcf963ce6283623fcbcf76339ef3d5ef81c4a1da75f'}
2025-07-08 00:03:05,719 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:05,727 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.80 vs Threshold: 3.5
2025-07-08 00:03:05,727 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:05,728 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:05,728 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RDNTUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922185728', 'recv_window': '120000', 'sign': 'b3b2f1147fdc383d0a3f02befb06ae8d27a08c41e9f44321547ec1675814619d'}
2025-07-08 00:03:05,729 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:05,779 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=QIUSDT HTTP/1.1" 200 312
2025-07-08 00:03:05,779 DEBUG: (QIUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:05,779 DEBUG: (REXUSDT) Starting analysis for 5m...
2025-07-08 00:03:05,780 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:05,780 DEBUG: Request Params: {'category': 'linear', 'symbol': 'REXUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922185780', 'recv_window': '120000', 'sign': 'abd950315cc51908da90dc346fea0508fdcbd8a5d4306de61bc9a147c64f9a4e'}
2025-07-08 00:03:05,781 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:05,835 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=PYTHUSDT HTTP/1.1" 200 314
2025-07-08 00:03:05,836 DEBUG: (PYTHUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:05,836 DEBUG: (REZUSDT) Starting analysis for 5m...
2025-07-08 00:03:05,836 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:05,836 DEBUG: Request Params: {'category': 'linear', 'symbol': 'REZUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922185836', 'recv_window': '120000', 'sign': '781aab6de72a0cae4f28a096897a13c890bf3efd27521701df68e41d5ff8129d'}
2025-07-08 00:03:05,838 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:05,941 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RAREUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922185628&recv_window=120000&sign=f1104a74b2900f16cbf41885e03b747a743fe34c6e5777fc29d3bfe241f997fe HTTP/1.1" 200 None
2025-07-08 00:03:06,009 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RENDERUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922185498&recv_window=120000&sign=1022b998294691ff6d131cc327c7f9bc006c4e460179abcd778e60d6c08dcade HTTP/1.1" 200 None
2025-07-08 00:03:06,013 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RADUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922185618&recv_window=120000&sign=9b019f0b07119e314a988b8a86c4476d2460a16be797e7236aadd1538238de86 HTTP/1.1" 200 None
2025-07-08 00:03:06,014 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RENUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922185629&recv_window=120000&sign=e7fdfc79b7a53db2ef833c5dbeaee895b20aec04520ec8aec8c20bd817fd4daf HTTP/1.1" 200 125
2025-07-08 00:03:06,029 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RAYDIUMUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922185625&recv_window=120000&sign=7ed51be7c0a9de8e7b7485cdbcb6a7b648b1bc75ad8ec8fdd54819db2a4e66ae HTTP/1.1" 200 None
2025-07-08 00:03:06,042 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RDNTUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922185728&recv_window=120000&sign=b3b2f1147fdc383d0a3f02befb06ae8d27a08c41e9f44321547ec1675814619d HTTP/1.1" 200 None
2025-07-08 00:03:06,047 WARNING: No data returned for RENUSDT on 5m. RC: 0, Msg: OK
2025-07-08 00:03:06,211 DEBUG: (RENUSDT) Fetch data returned empty. Returning None.
2025-07-08 00:03:06,214 DEBUG: (RENUSDT) analyze_symbol returning: Signal=None
2025-07-08 00:03:06,215 DEBUG: (RIFSOLUSDT) Starting analysis for 5m...
2025-07-08 00:03:06,212 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=QUICKUSDT HTTP/1.1" 200 194
2025-07-08 00:03:06,091 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=REXUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922185780&recv_window=120000&sign=abd950315cc51908da90dc346fea0508fdcbd8a5d4306de61bc9a147c64f9a4e HTTP/1.1" 200 None
2025-07-08 00:03:06,215 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:06,117 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=REQUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922185707&recv_window=120000&sign=c67fcb995b185993a9ccdbcf963ce6283623fcbcf76339ef3d5ef81c4a1da75f HTTP/1.1" 200 None
2025-07-08 00:03:06,230 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=REZUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922185836&recv_window=120000&sign=781aab6de72a0cae4f28a096897a13c890bf3efd27521701df68e41d5ff8129d HTTP/1.1" 200 None
2025-07-08 00:03:06,310 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RIFSOLUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922186215', 'recv_window': '120000', 'sign': '126c25a95b1f26dfd14516deb1dc22b4c84d7f8c81cf72245a2a611d6abec099'}
2025-07-08 00:03:06,364 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.80 vs Threshold: 3.5
2025-07-08 00:03:06,437 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:06,455 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=QUICKUSDT HTTP/1.1" 200 315
2025-07-08 00:03:06,472 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:06,525 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/0.50 vs Threshold: 3.5
2025-07-08 00:03:06,579 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:06,580 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:06,580 DEBUG: Request Params: {'category': 'linear', 'symbol': 'REXUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922186580', 'recv_window': '120000', 'sign': '11180bd340cc7edfea0b9958ce5c64a4335954dd7ccc9ab7d1d0bd4cf8f6ab96'}
2025-07-08 00:03:06,530 DEBUG: (QUICKUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:06,545 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=RAREUSDT HTTP/1.1" 200 189
2025-07-08 00:03:06,552 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=RADUSDT HTTP/1.1" 200 192
2025-07-08 00:03:06,579 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:06,581 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:06,612 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.80 vs Threshold: 3.5
2025-07-08 00:03:06,613 DEBUG: (RIFUSDT) Starting analysis for 5m...
2025-07-08 00:03:06,617 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:06,619 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RENDERUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922186579', 'recv_window': '120000', 'sign': 'd1919238df389307397df3c4dfd33b67fbc25302fa7e197b1ed03051fc69c5bf'}
2025-07-08 00:03:06,620 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:06,620 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:06,620 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:06,621 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:06,622 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:06,622 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RIFUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922186620', 'recv_window': '120000', 'sign': '291fce12482ce0f958c5ba2eb7075bb798f1b7f7a24cbf3d10bebadf4bf3ec31'}
2025-07-08 00:03:06,623 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:06,624 DEBUG: Request Params: {'category': 'linear', 'symbol': 'REZUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922186621', 'recv_window': '120000', 'sign': 'f135f83509c75c09dcd2e4fcc8b6c6b64da535ac42b6f655680f0df559f4f007'}
2025-07-08 00:03:06,626 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:06,626 DEBUG: Request Params: {'category': 'linear', 'symbol': 'REQUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922186623', 'recv_window': '120000', 'sign': 'e4a3e87c196d0ad99048e75b1aff8594883cb3edd645198b54be3b2305311964'}
2025-07-08 00:03:06,627 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:06,628 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:06,723 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=RDNTUSDT HTTP/1.1" 200 188
2025-07-08 00:03:06,758 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=RAYDIUMUSDT HTTP/1.1" 200 192
2025-07-08 00:03:06,843 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=RADUSDT HTTP/1.1" 200 311
2025-07-08 00:03:06,844 DEBUG: (RADUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:06,844 DEBUG: (RLCUSDT) Starting analysis for 5m...
2025-07-08 00:03:06,845 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:06,845 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RLCUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922186845', 'recv_window': '120000', 'sign': '65a2c9db9cb41221077f9bccdafa735ed7174653d92d05aa22c2d91968e6f7fd'}
2025-07-08 00:03:06,846 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:06,862 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=RAREUSDT HTTP/1.1" 200 314
2025-07-08 00:03:06,863 DEBUG: (RAREUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:06,863 DEBUG: (RONINUSDT) Starting analysis for 5m...
2025-07-08 00:03:06,863 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:06,864 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RONINUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922186863', 'recv_window': '120000', 'sign': '1c086a5cfe73fd0f25620b109eea2d317a64b4f7927d67e5798b25c04ecf9e50'}
2025-07-08 00:03:06,865 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:06,919 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RIFSOLUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922186215&recv_window=120000&sign=126c25a95b1f26dfd14516deb1dc22b4c84d7f8c81cf72245a2a611d6abec099 HTTP/1.1" 200 128
2025-07-08 00:03:06,920 WARNING: No data returned for RIFSOLUSDT on 5m. RC: 0, Msg: OK
2025-07-08 00:03:06,921 DEBUG: (RIFSOLUSDT) Fetch data returned empty. Returning None.
2025-07-08 00:03:06,921 DEBUG: (RIFSOLUSDT) analyze_symbol returning: Signal=None
2025-07-08 00:03:06,921 DEBUG: (ROSEUSDT) Starting analysis for 5m...
2025-07-08 00:03:06,921 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:06,922 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ROSEUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922186921', 'recv_window': '120000', 'sign': 'dc4a872769b1d0b62812e74e9ab81c5ae50f893188c1bef1a0a38cff2cb117de'}
2025-07-08 00:03:06,923 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:06,948 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=RDNTUSDT HTTP/1.1" 200 314
2025-07-08 00:03:06,948 DEBUG: (RDNTUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:06,949 DEBUG: (RPLUSDT) Starting analysis for 5m...
2025-07-08 00:03:06,949 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:06,949 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RPLUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922186949', 'recv_window': '120000', 'sign': '4f8f4f4c7d1057322f082ac1410ed3b863dbbe108f6e5fd01522f3e981aaf143'}
2025-07-08 00:03:06,950 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:06,952 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=REXUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922186580&recv_window=120000&sign=11180bd340cc7edfea0b9958ce5c64a4335954dd7ccc9ab7d1d0bd4cf8f6ab96 HTTP/1.1" 200 None
2025-07-08 00:03:06,958 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RENDERUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922186579&recv_window=120000&sign=d1919238df389307397df3c4dfd33b67fbc25302fa7e197b1ed03051fc69c5bf HTTP/1.1" 200 None
2025-07-08 00:03:06,963 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=REZUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922186621&recv_window=120000&sign=f135f83509c75c09dcd2e4fcc8b6c6b64da535ac42b6f655680f0df559f4f007 HTTP/1.1" 200 None
2025-07-08 00:03:07,016 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RIFUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922186620&recv_window=120000&sign=291fce12482ce0f958c5ba2eb7075bb798f1b7f7a24cbf3d10bebadf4bf3ec31 HTTP/1.1" 200 None
2025-07-08 00:03:07,027 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=REQUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922186623&recv_window=120000&sign=e4a3e87c196d0ad99048e75b1aff8594883cb3edd645198b54be3b2305311964 HTTP/1.1" 200 None
2025-07-08 00:03:07,180 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RONINUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922186863&recv_window=120000&sign=1c086a5cfe73fd0f25620b109eea2d317a64b4f7927d67e5798b25c04ecf9e50 HTTP/1.1" 200 None
2025-07-08 00:03:07,232 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RLCUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922186845&recv_window=120000&sign=65a2c9db9cb41221077f9bccdafa735ed7174653d92d05aa22c2d91968e6f7fd HTTP/1.1" 200 None
2025-07-08 00:03:07,232 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ROSEUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922186921&recv_window=120000&sign=dc4a872769b1d0b62812e74e9ab81c5ae50f893188c1bef1a0a38cff2cb117de HTTP/1.1" 200 5651
2025-07-08 00:03:07,321 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RPLUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922186949&recv_window=120000&sign=4f8f4f4c7d1057322f082ac1410ed3b863dbbe108f6e5fd01522f3e981aaf143 HTTP/1.1" 200 None
2025-07-08 00:03:07,376 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=RAYDIUMUSDT HTTP/1.1" 200 315
2025-07-08 00:03:07,383 DEBUG: (RAYDIUMUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:07,387 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=REXUSDT HTTP/1.1" 200 191
2025-07-08 00:03:07,402 DEBUG: (RSRUSDT) Starting analysis for 5m...
2025-07-08 00:03:07,427 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:07,433 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RSRUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922187427', 'recv_window': '120000', 'sign': '5854e16c0e84701be6fb24de7233dc84bbee1e313209e53857f384e6cccc7bea'}
2025-07-08 00:03:07,443 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:07,455 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/1.80 vs Threshold: 3.5
2025-07-08 00:03:07,463 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:07,473 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:07,475 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:07,479 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:07,496 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=RENDERUSDT HTTP/1.1" 200 191
2025-07-08 00:03:07,497 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:07,502 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:07,525 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:07,536 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/0.00 vs Threshold: 3.5
2025-07-08 00:03:07,543 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RIFUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922187497', 'recv_window': '120000', 'sign': 'd8d1b81703c9a7a0c78901a128cc6b01f1c0aaef3511cba3ca1dee55d3c20c33'}
2025-07-08 00:03:07,551 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:07,551 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RONINUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922187502', 'recv_window': '120000', 'sign': '8bba3d1e536f8a02d22679d40f617d366d0bca246d2f113f228c0c41ffa559d4'}
2025-07-08 00:03:07,552 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=REQUSDT HTTP/1.1" 200 187
2025-07-08 00:03:07,552 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:07,553 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:07,554 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:07,554 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:07,555 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:07,557 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RLCUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922187552', 'recv_window': '120000', 'sign': 'db5b20f26ede415dd8c30e2f9a1b6af9425c0fe0bca075e0930a86e83ab3fc4c'}
2025-07-08 00:03:07,557 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:07,558 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:07,560 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RPLUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922187557', 'recv_window': '120000', 'sign': '280b501623e8e0930ad45b6e8ada99c5469c2b38960886993b5710bba9a79782'}
2025-07-08 00:03:07,560 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:07,560 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ROSEUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922187558', 'recv_window': '120000', 'sign': 'f19ea2d73ac160ef628a66ca175a4548e61555f4ffb1d05bbb342a5cdaa3c786'}
2025-07-08 00:03:07,561 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:07,563 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:07,594 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=REZUSDT HTTP/1.1" 200 188
2025-07-08 00:03:07,651 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=REXUSDT HTTP/1.1" 200 315
2025-07-08 00:03:07,652 DEBUG: (REXUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:07,653 DEBUG: (RSS3USDT) Starting analysis for 5m...
2025-07-08 00:03:07,653 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:07,653 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RSS3USDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922187653', 'recv_window': '120000', 'sign': '6124c01cce3ebaac1672650d1fe1ac7eced50b34c56186a821b64746859a9d7e'}
2025-07-08 00:03:07,654 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:07,779 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=REQUSDT HTTP/1.1" 200 311
2025-07-08 00:03:07,780 DEBUG: (REQUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:07,780 DEBUG: (RUNEUSDT) Starting analysis for 5m...
2025-07-08 00:03:07,780 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:07,780 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RUNEUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922187780', 'recv_window': '120000', 'sign': 'fe6264433a55cfd47c0fd9d8348fab12dd76156234be90057a9006211936f4af'}
2025-07-08 00:03:07,781 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:07,790 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=RENDERUSDT HTTP/1.1" 200 314
2025-07-08 00:03:07,791 DEBUG: (RENDERUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:07,791 DEBUG: (RVNUSDT) Starting analysis for 5m...
2025-07-08 00:03:07,791 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:07,791 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RVNUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922187791', 'recv_window': '120000', 'sign': '37f9014e766bc24e3668344f5c981bc9ef68c0a59c34ac99cd6e72f801af603b'}
2025-07-08 00:03:07,792 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:07,819 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=REZUSDT HTTP/1.1" 200 315
2025-07-08 00:03:07,820 DEBUG: (REZUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:07,820 DEBUG: (SAFEUSDT) Starting analysis for 5m...
2025-07-08 00:03:07,820 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:07,821 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SAFEUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922187820', 'recv_window': '120000', 'sign': '73700ac856a250e21f18d0b192234319e65a1ab44eb1b0c972b015885bb9cc12'}
2025-07-08 00:03:07,821 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:07,839 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RSRUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922187427&recv_window=120000&sign=5854e16c0e84701be6fb24de7233dc84bbee1e313209e53857f384e6cccc7bea HTTP/1.1" 200 None
2025-07-08 00:03:07,862 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RLCUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922187552&recv_window=120000&sign=db5b20f26ede415dd8c30e2f9a1b6af9425c0fe0bca075e0930a86e83ab3fc4c HTTP/1.1" 200 None
2025-07-08 00:03:07,875 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RONINUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922187502&recv_window=120000&sign=8bba3d1e536f8a02d22679d40f617d366d0bca246d2f113f228c0c41ffa559d4 HTTP/1.1" 200 None
2025-07-08 00:03:07,875 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RPLUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922187557&recv_window=120000&sign=280b501623e8e0930ad45b6e8ada99c5469c2b38960886993b5710bba9a79782 HTTP/1.1" 200 None
2025-07-08 00:03:07,876 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RIFUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922187497&recv_window=120000&sign=d8d1b81703c9a7a0c78901a128cc6b01f1c0aaef3511cba3ca1dee55d3c20c33 HTTP/1.1" 200 None
2025-07-08 00:03:07,876 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ROSEUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922187558&recv_window=120000&sign=f19ea2d73ac160ef628a66ca175a4548e61555f4ffb1d05bbb342a5cdaa3c786 HTTP/1.1" 200 None
2025-07-08 00:03:07,986 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:07,995 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:08,003 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:08,011 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RSRUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922188003', 'recv_window': '120000', 'sign': 'ed9717457349c4262774c072805e57316a14f980c205da550904c4f61dcccb3e'}
2025-07-08 00:03:08,029 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RSS3USDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922187653&recv_window=120000&sign=6124c01cce3ebaac1672650d1fe1ac7eced50b34c56186a821b64746859a9d7e HTTP/1.1" 200 None
2025-07-08 00:03:08,101 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:08,167 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RUNEUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922187780&recv_window=120000&sign=fe6264433a55cfd47c0fd9d8348fab12dd76156234be90057a9006211936f4af HTTP/1.1" 200 None
2025-07-08 00:03:08,176 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RVNUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922187791&recv_window=120000&sign=37f9014e766bc24e3668344f5c981bc9ef68c0a59c34ac99cd6e72f801af603b HTTP/1.1" 200 None
2025-07-08 00:03:08,225 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SAFEUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922187820&recv_window=120000&sign=73700ac856a250e21f18d0b192234319e65a1ab44eb1b0c972b015885bb9cc12 HTTP/1.1" 200 None
2025-07-08 00:03:08,407 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=RLCUSDT HTTP/1.1" 200 187
2025-07-08 00:03:08,443 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:08,452 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:08,451 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:08,449 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:08,452 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=RPLUSDT HTTP/1.1" 200 192
2025-07-08 00:03:08,461 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:08,468 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/1.00 vs Threshold: 3.5
2025-07-08 00:03:08,469 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:08,469 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:08,470 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RSS3USDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922188461', 'recv_window': '120000', 'sign': '9f6b31e7dd3566da8db4e1c811f84791087de94d88a0eb2ebf26dffcd6b65e2c'}
2025-07-08 00:03:08,470 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:08,482 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:08,474 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=RIFUSDT HTTP/1.1" 200 188
2025-07-08 00:03:08,474 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=RONINUSDT HTTP/1.1" 200 194
2025-07-08 00:03:08,478 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:08,484 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RUNEUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922188478', 'recv_window': '120000', 'sign': 'a015262a92383110bb15dff20084a05cfc8ab5ab74e4f2535c0d7b3fb52e295b'}
2025-07-08 00:03:08,482 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SAFEUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922188481', 'recv_window': '120000', 'sign': 'db570a5e07fd8f1a359a80d92d939a60ca1697d9f679e39a362502ed948cd7a0'}
2025-07-08 00:03:08,474 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:08,486 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:08,487 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:08,488 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:08,488 DEBUG: Request Params: {'category': 'linear', 'symbol': 'RVNUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922188473', 'recv_window': '120000', 'sign': '9641ec2389f1806fcc5ede01fecd95d01afebfc83fc63f93b028c7b7c2dd9628'}
2025-07-08 00:03:08,490 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:08,526 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RSRUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922188003&recv_window=120000&sign=ed9717457349c4262774c072805e57316a14f980c205da550904c4f61dcccb3e HTTP/1.1" 200 None
2025-07-08 00:03:08,587 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=ROSEUSDT HTTP/1.1" 200 188
2025-07-08 00:03:08,652 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=RLCUSDT HTTP/1.1" 200 311
2025-07-08 00:03:08,652 DEBUG: (RLCUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:08,653 DEBUG: (SAGAUSDT) Starting analysis for 5m...
2025-07-08 00:03:08,653 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:08,653 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SAGAUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922188653', 'recv_window': '120000', 'sign': '2047e9a615c4a89e9dd0a2a35609db40efc0fdbf14bc3be71e23acb8ef6adc33'}
2025-07-08 00:03:08,654 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:08,695 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=RPLUSDT HTTP/1.1" 200 309
2025-07-08 00:03:08,696 DEBUG: (RPLUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:08,696 DEBUG: (SANDUSDT) Starting analysis for 5m...
2025-07-08 00:03:08,697 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:08,697 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SANDUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922188697', 'recv_window': '120000', 'sign': 'd85cc897770b0e73d908ea0cce302208a7079cca989b6f9b596a185eca97d61e'}
2025-07-08 00:03:08,698 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:08,708 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=RIFUSDT HTTP/1.1" 200 313
2025-07-08 00:03:08,709 DEBUG: (RIFUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:08,709 DEBUG: (SCAUSDT) Starting analysis for 5m...
2025-07-08 00:03:08,709 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:08,709 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SCAUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922188709', 'recv_window': '120000', 'sign': '0f49393f072da1fbd8d96e9d9992624709b95f4dd81a608db14ffa9ff5d3f1ba'}
2025-07-08 00:03:08,710 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:08,729 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=RONINUSDT HTTP/1.1" 200 313
2025-07-08 00:03:08,730 DEBUG: (RONINUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:08,730 DEBUG: (SCRTUSDT) Starting analysis for 5m...
2025-07-08 00:03:08,731 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:08,731 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SCRTUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922188731', 'recv_window': '120000', 'sign': '1b12a3a914bb98990905b62d3fce3b72aaace60c757b44f44e45c1ba21dd2002'}
2025-07-08 00:03:08,732 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:08,801 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SAFEUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922188481&recv_window=120000&sign=db570a5e07fd8f1a359a80d92d939a60ca1697d9f679e39a362502ed948cd7a0 HTTP/1.1" 200 None
2025-07-08 00:03:08,801 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RVNUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922188473&recv_window=120000&sign=9641ec2389f1806fcc5ede01fecd95d01afebfc83fc63f93b028c7b7c2dd9628 HTTP/1.1" 200 None
2025-07-08 00:03:08,851 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=ROSEUSDT HTTP/1.1" 200 316
2025-07-08 00:03:08,851 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=RSRUSDT HTTP/1.1" 200 192
2025-07-08 00:03:08,852 DEBUG: (ROSEUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:08,869 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RUNEUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922188478&recv_window=120000&sign=a015262a92383110bb15dff20084a05cfc8ab5ab74e4f2535c0d7b3fb52e295b HTTP/1.1" 200 None
2025-07-08 00:03:08,885 DEBUG: (SCRUSDT) Starting analysis for 5m...
2025-07-08 00:03:08,896 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=RSS3USDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922188461&recv_window=120000&sign=9f6b31e7dd3566da8db4e1c811f84791087de94d88a0eb2ebf26dffcd6b65e2c HTTP/1.1" 200 None
2025-07-08 00:03:08,897 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:08,921 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SCRUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922188897', 'recv_window': '120000', 'sign': '87622a13233934af0a2b23cf88b97e74384c4c7731ea406efc907e544de68e4e'}
2025-07-08 00:03:08,940 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:08,967 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SAGAUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922188653&recv_window=120000&sign=2047e9a615c4a89e9dd0a2a35609db40efc0fdbf14bc3be71e23acb8ef6adc33 HTTP/1.1" 200 None
2025-07-08 00:03:09,043 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SCRTUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922188731&recv_window=120000&sign=1b12a3a914bb98990905b62d3fce3b72aaace60c757b44f44e45c1ba21dd2002 HTTP/1.1" 200 None
2025-07-08 00:03:09,070 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SANDUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922188697&recv_window=120000&sign=d85cc897770b0e73d908ea0cce302208a7079cca989b6f9b596a185eca97d61e HTTP/1.1" 200 None
2025-07-08 00:03:09,081 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SCAUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922188709&recv_window=120000&sign=0f49393f072da1fbd8d96e9d9992624709b95f4dd81a608db14ffa9ff5d3f1ba HTTP/1.1" 200 None
2025-07-08 00:03:09,144 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=RSRUSDT HTTP/1.1" 200 315
2025-07-08 00:03:09,151 DEBUG: (RSRUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:09,163 DEBUG: (SCUSDT) Starting analysis for 5m...
2025-07-08 00:03:09,189 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:09,194 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SCUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922189187', 'recv_window': '120000', 'sign': 'f80cb92980278e2c7dc7156ff67ab1183d2ce56c4cc648bc5e64c89f7233e86f'}
2025-07-08 00:03:09,240 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:09,255 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.00 vs Threshold: 3.5
2025-07-08 00:03:09,283 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SCRUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922188897&recv_window=120000&sign=87622a13233934af0a2b23cf88b97e74384c4c7731ea406efc907e544de68e4e HTTP/1.1" 200 None
2025-07-08 00:03:09,288 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:09,295 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.80 vs Threshold: 3.5
2025-07-08 00:03:09,297 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=SAFEUSDT HTTP/1.1" 200 189
2025-07-08 00:03:09,300 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:09,306 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=RVNUSDT HTTP/1.1" 200 192
2025-07-08 00:03:09,311 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:09,331 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SCRTUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922189300', 'recv_window': '120000', 'sign': '5e1260cf34604176af3404554f882759aa540fd97ed383e66e7f0dba3ed4726a'}
2025-07-08 00:03:09,387 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:09,392 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:09,409 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:09,415 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.50/0.50 vs Threshold: 3.5
2025-07-08 00:03:09,418 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:09,419 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:09,419 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SAGAUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922189392', 'recv_window': '120000', 'sign': '85fbdedb5048c7477a5be2f6062b993f8a816cf0f5c9b0ecb6a400cb7569c6b3'}
2025-07-08 00:03:09,420 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.50/9.0 | Final Confidence: 27.8% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:09,420 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:09,421 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:09,422 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:09,422 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:09,423 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:09,423 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SANDUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922189421', 'recv_window': '120000', 'sign': '26c4d138cdb20ea5f1652308af3487b08b9f06c04dfc391f4b3725202e401866'}
2025-07-08 00:03:09,424 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SCAUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922189422', 'recv_window': '120000', 'sign': 'f24ce9b5dc2ea87294ca0106016584824c8503caeced61458180e6a78e1d23bd'}
2025-07-08 00:03:09,424 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SCRUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922189423', 'recv_window': '120000', 'sign': 'e129a70746609a73e2672676d0713ed4a1988c63c203ed8a54f98898f2f69c38'}
2025-07-08 00:03:09,425 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:09,426 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:09,427 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:09,486 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=RSS3USDT HTTP/1.1" 200 193
2025-07-08 00:03:09,569 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SCUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922189187&recv_window=120000&sign=f80cb92980278e2c7dc7156ff67ab1183d2ce56c4cc648bc5e64c89f7233e86f HTTP/1.1" 200 None
2025-07-08 00:03:09,591 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=SAFEUSDT HTTP/1.1" 200 312
2025-07-08 00:03:09,615 DEBUG: (SAFEUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:09,622 DEBUG: (SDUSDT) Starting analysis for 5m...
2025-07-08 00:03:09,625 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:09,633 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:09,633 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SDUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922189625', 'recv_window': '120000', 'sign': 'b11ce9ead0594b99bedca1255b24b611cf6186d64b92607a3f7d0f212a5a6457'}
2025-07-08 00:03:09,634 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:09,635 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:09,636 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:09,636 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SCUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922189636', 'recv_window': '120000', 'sign': 'e4c9add30db6471a869b700634a5994eb110950c5a91cced30ad2c732d9341d6'}
2025-07-08 00:03:09,637 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:09,644 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=RVNUSDT HTTP/1.1" 200 315
2025-07-08 00:03:09,644 DEBUG: (RVNUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:09,645 DEBUG: (SEIUSDT) Starting analysis for 5m...
2025-07-08 00:03:09,645 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:09,645 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SEIUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922189645', 'recv_window': '120000', 'sign': 'b3e3022dc10d1e783d111852e9310ad811bd7911046a467da0e2df43ab7fa486'}
2025-07-08 00:03:09,646 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:09,722 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=RUNEUSDT HTTP/1.1" 200 192
2025-07-08 00:03:09,729 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SANDUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922189421&recv_window=120000&sign=26c4d138cdb20ea5f1652308af3487b08b9f06c04dfc391f4b3725202e401866 HTTP/1.1" 200 None
2025-07-08 00:03:09,732 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SCAUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922189422&recv_window=120000&sign=f24ce9b5dc2ea87294ca0106016584824c8503caeced61458180e6a78e1d23bd HTTP/1.1" 200 None
2025-07-08 00:03:09,734 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=RSS3USDT HTTP/1.1" 200 314
2025-07-08 00:03:09,761 DEBUG: (RSS3USDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:09,773 DEBUG: (SENDUSDT) Starting analysis for 5m...
2025-07-08 00:03:09,774 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:09,784 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SENDUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922189774', 'recv_window': '120000', 'sign': 'feaf7dc7b342d709b9d778c1280a3e1582fd6997cf41360b1e307ac9b2c312cd'}
2025-07-08 00:03:09,792 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SCRTUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922189300&recv_window=120000&sign=5e1260cf34604176af3404554f882759aa540fd97ed383e66e7f0dba3ed4726a HTTP/1.1" 200 None
2025-07-08 00:03:09,792 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SAGAUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922189392&recv_window=120000&sign=85fbdedb5048c7477a5be2f6062b993f8a816cf0f5c9b0ecb6a400cb7569c6b3 HTTP/1.1" 200 None
2025-07-08 00:03:09,802 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:09,812 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SCRUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922189423&recv_window=120000&sign=e129a70746609a73e2672676d0713ed4a1988c63c203ed8a54f98898f2f69c38 HTTP/1.1" 200 None
2025-07-08 00:03:09,951 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=RUNEUSDT HTTP/1.1" 200 312
2025-07-08 00:03:09,957 DEBUG: (RUNEUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:09,967 DEBUG: (SFPUSDT) Starting analysis for 5m...
2025-07-08 00:03:09,978 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:09,984 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SFPUSDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922189978', 'recv_window': '120000', 'sign': '7c7bb751e79ae9b05986aeb03dec69aa6be2d55fb80570afd13932ec0a9fb615'}
2025-07-08 00:03:09,994 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:10,010 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SCUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922189636&recv_window=120000&sign=e4c9add30db6471a869b700634a5994eb110950c5a91cced30ad2c732d9341d6 HTTP/1.1" 200 None
2025-07-08 00:03:10,019 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SDUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922189625&recv_window=120000&sign=b11ce9ead0594b99bedca1255b24b611cf6186d64b92607a3f7d0f212a5a6457 HTTP/1.1" 200 None
2025-07-08 00:03:10,029 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SEIUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922189645&recv_window=120000&sign=b3e3022dc10d1e783d111852e9310ad811bd7911046a467da0e2df43ab7fa486 HTTP/1.1" 200 None
2025-07-08 00:03:10,100 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=SANDUSDT HTTP/1.1" 200 188
2025-07-08 00:03:10,167 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=SCRTUSDT HTTP/1.1" 200 189
2025-07-08 00:03:10,176 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SENDUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922189774&recv_window=120000&sign=feaf7dc7b342d709b9d778c1280a3e1582fd6997cf41360b1e307ac9b2c312cd HTTP/1.1" 200 None
2025-07-08 00:03:10,190 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=SCAUSDT HTTP/1.1" 200 192
2025-07-08 00:03:10,219 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:10,233 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:10,241 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:10,243 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/0.50 vs Threshold: 3.5
2025-07-08 00:03:10,251 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SEIUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922190241', 'recv_window': '120000', 'sign': '56941cabd0cd1a1c2c230d3634c06c9784b8c5e94b23a25aab9cc5dcf08f7467'}
2025-07-08 00:03:10,260 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:10,284 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:10,285 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:10,291 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/1.50 vs Threshold: 3.5
2025-07-08 00:03:10,297 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=SCRUSDT HTTP/1.1" 200 188
2025-07-08 00:03:10,297 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SDUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922190285', 'recv_window': '120000', 'sign': '5df2b6edde51b332c5c011a65bacf6d99fbb25e178964cc6b8414367de90221d'}
2025-07-08 00:03:10,298 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-07-08 00:03:10,300 DEBUG: --- Calculating Trade Plan for N/A 5m BUY ---
2025-07-08 00:03:10,300 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:10,300 DEBUG: (N/A) Last Close: 0.4290, Raw ATR: 0.0011, Valid ATR: 0.0021
2025-07-08 00:03:10,301 DEBUG: (N/A) Fibs Available: True
2025-07-08 00:03:10,301 DEBUG: (N/A) Fib Levels: 0%=0.4263, 38.2%=0.4298, 61.8%=0.4319, 100%=0.4354, 161.8%=0.4410, -61.8%=0.4207
2025-07-08 00:03:10,302 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.4297762, 0.4319238), SL: 0.4258, TP: 0.4410
2025-07-08 00:03:10,302 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-07-08 00:03:10,302 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.4297762, 0.4319238), SL=0.4258, TP=0.4410
2025-07-08 00:03:10,302 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-07-08 00:03:10,303 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-07-08 00:03:10,303 DEBUG: (N/A) Price (0.4290) is INSIDE entry zone (0.4298-0.4319).
2025-07-08 00:03:10,303 DEBUG: N/A 5m: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: ''
2025-07-08 00:03:10,304 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:10,304 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SENDUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922190303', 'recv_window': '120000', 'sign': 'e695a7a8ffcac7ce4a561dd5fd61a678988a262907ca43451139d01a19a327d0'}
2025-07-08 00:03:10,305 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:10,352 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=SANDUSDT HTTP/1.1" 200 314
2025-07-08 00:03:10,352 DEBUG: (SANDUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:10,353 DEBUG: (SHIB1000USDT) Starting analysis for 5m...
2025-07-08 00:03:10,353 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:10,353 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SHIB1000USDT', 'interval': '5', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922190353', 'recv_window': '120000', 'sign': '730ba33fbd27ff100024a2fec3229c84317c3dc999cca8fd27c1b7b00648bbe8'}
2025-07-08 00:03:10,354 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:10,366 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=SAGAUSDT HTTP/1.1" 200 189
2025-07-08 00:03:10,369 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SFPUSDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922189978&recv_window=120000&sign=7c7bb751e79ae9b05986aeb03dec69aa6be2d55fb80570afd13932ec0a9fb615 HTTP/1.1" 200 None
2025-07-08 00:03:10,426 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-07-08 00:03:10,427 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:10,427 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:10,428 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SFPUSDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922190427', 'recv_window': '120000', 'sign': '604ab525f19c416093c278c013ec41ef469326b4a3c3d77ae665dc67f6602f87'}
2025-07-08 00:03:10,428 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:10,447 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=SCRTUSDT HTTP/1.1" 200 312
2025-07-08 00:03:10,448 DEBUG: (SCRTUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:10,457 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=SCUSDT HTTP/1.1" 200 190
2025-07-08 00:03:10,477 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=SCAUSDT HTTP/1.1" 200 313
2025-07-08 00:03:10,478 DEBUG: (SCAUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:10,526 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=SCRUSDT HTTP/1.1" 200 313
2025-07-08 00:03:10,527 DEBUG: (SCRUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:10,603 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SEIUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922190241&recv_window=120000&sign=56941cabd0cd1a1c2c230d3634c06c9784b8c5e94b23a25aab9cc5dcf08f7467 HTTP/1.1" 200 None
2025-07-08 00:03:10,614 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SENDUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922190303&recv_window=120000&sign=e695a7a8ffcac7ce4a561dd5fd61a678988a262907ca43451139d01a19a327d0 HTTP/1.1" 200 None
2025-07-08 00:03:10,622 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=SAGAUSDT HTTP/1.1" 200 312
2025-07-08 00:03:10,626 DEBUG: (SAGAUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:10,668 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SDUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922190285&recv_window=120000&sign=5df2b6edde51b332c5c011a65bacf6d99fbb25e178964cc6b8414367de90221d HTTP/1.1" 200 None
2025-07-08 00:03:10,691 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=SCUSDT HTTP/1.1" 200 314
2025-07-08 00:03:10,692 DEBUG: (SCUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:10,728 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SHIB1000USDT&interval=5&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922190353&recv_window=120000&sign=730ba33fbd27ff100024a2fec3229c84317c3dc999cca8fd27c1b7b00648bbe8 HTTP/1.1" 200 None
2025-07-08 00:03:10,807 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SFPUSDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922190427&recv_window=120000&sign=604ab525f19c416093c278c013ec41ef469326b4a3c3d77ae665dc67f6602f87 HTTP/1.1" 200 None
2025-07-08 00:03:10,852 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.50/1.30 vs Threshold: 3.5
2025-07-08 00:03:10,852 DEBUG: N/A 5m: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.50/9.0 | Final Confidence: 27.8% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 00:03:10,853 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 00:03:10,862 DEBUG: Request Params: {'category': 'linear', 'symbol': 'SHIB1000USDT', 'interval': '15', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1751922190853', 'recv_window': '120000', 'sign': '213db1014e79891ab51bf773165a6b08113c3b5cc00df347a4296a550f5f0771'}
2025-07-08 00:03:10,889 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 00:03:10,961 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=SEIUSDT HTTP/1.1" 200 187
2025-07-08 00:03:10,988 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=SENDUSDT HTTP/1.1" 200 189
2025-07-08 00:03:11,045 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=SDUSDT HTTP/1.1" 200 186
2025-07-08 00:03:11,185 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=SEIUSDT HTTP/1.1" 200 315
2025-07-08 00:03:11,186 DEBUG: (SEIUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:11,192 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=SHIB1000USDT&interval=15&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1751922190853&recv_window=120000&sign=213db1014e79891ab51bf773165a6b08113c3b5cc00df347a4296a550f5f0771 HTTP/1.1" 200 None
2025-07-08 00:03:11,240 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=SENDUSDT HTTP/1.1" 200 310
2025-07-08 00:03:11,254 DEBUG: (SENDUSDT) analyze_symbol returning: Signal=BUY
2025-07-08 00:03:11,271 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=SDUSDT HTTP/1.1" 200 308
2025-07-08 00:03:11,271 DEBUG: (SDUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:11,487 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=SFPUSDT HTTP/1.1" 200 187
2025-07-08 00:03:11,501 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=SHIB1000USDT HTTP/1.1" 200 192
2025-07-08 00:03:11,740 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=SFPUSDT HTTP/1.1" 200 311
2025-07-08 00:03:11,741 DEBUG: (SFPUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:11,748 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=5min&limit=2&symbol=SHIB1000USDT HTTP/1.1" 200 322
2025-07-08 00:03:11,748 DEBUG: (SHIB1000USDT) analyze_symbol returning: Signal=HOLD
2025-07-08 00:03:11,750 INFO: Status: Analysis complete - 351/383 processed.
2025-07-08 02:43:55,092 INFO: DPG main loop ended
2025-07-08 02:43:55,092 INFO: Shutting down GUI...
2025-07-08 02:43:55,096 INFO: GUI run completed.
2025-07-08 02:43:55,098 INFO: Shutting down application...
2025-07-08 23:19:07,547 INFO: File logging configured successfully.
2025-07-08 23:19:07,547 INFO: Starting application...
2025-07-08 23:19:07,548 INFO: Initializing AdvancedTradingBot...
2025-07-08 23:19:07,549 INFO: Loaded 20 open trades from file
2025-07-08 23:19:07,550 INFO: Initializing direct Bybit client...
2025-07-08 23:19:07,552 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 23:19:07,987 DEBUG: https://api.bybit.com:443 "GET /v5/market/time HTTP/1.1" 200 134
2025-07-08 23:19:07,990 INFO: Time synchronized. Server: 1752005914000, Local: 1752005947990, Offset: -33990 ms
2025-07-08 23:19:07,990 INFO: Direct Bybit client initialized successfully with time offset: -33990 ms
2025-07-08 23:19:07,990 INFO: Initializing pybit client as fallback...
2025-07-08 23:19:07,991 DEBUG: Initializing HTTP session.
2025-07-08 23:19:07,991 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 23:19:08,642 DEBUG: https://api.bybit.com:443 "GET /v5/market/time HTTP/1.1" 200 134
2025-07-08 23:19:08,642 INFO: Pybit client initialized successfully. Server time: {'retCode': 0, 'retMsg': 'OK', 'result': {'timeSecond': '1752005915', 'timeNano': '1752005915135011876'}, 'retExtInfo': {}, 'time': 1752005915135}
2025-07-08 23:19:08,643 INFO: AdvancedTradingBot initialized.
2025-07-08 23:19:08,643 INFO: Creating AdvancedTradingGUI...
2025-07-08 23:19:08,643 INFO: AdvancedTradingGUI init started.
2025-07-08 23:19:08,644 INFO: Loaded 25 trades from history file
2025-07-08 23:19:08,644 INFO: Calculated equity curve with 26 points
2025-07-08 23:19:08,645 INFO: Loaded 383 symbols from watchlist.json
2025-07-08 23:19:08,645 INFO: AdvancedTradingGUI created successfully.
2025-07-08 23:19:08,646 INFO: Running GUI...
2025-07-08 23:19:08,646 INFO: Running GUI...
2025-07-08 23:19:08,654 INFO: DPG context created
2025-07-08 23:19:08,654 INFO: DPG viewport created
2025-07-08 23:19:08,661 INFO: Main window created
2025-07-08 23:19:08,793 INFO: Viewport shown
2025-07-08 23:19:08,793 DEBUG: Fetching fresh positions data from API
2025-07-08 23:19:08,794 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 23:19:09,100 DEBUG: https://api.bybit.com:443 "GET /v5/market/time HTTP/1.1" 200 134
2025-07-08 23:19:09,100 DEBUG: Request URL: https://api.bybit.com/v5/position/list
2025-07-08 23:19:09,101 DEBUG: Request Params: {'category': 'linear', 'settleCoin': 'USDT', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': 1752005915000, 'recv_window': 120000, 'sign': '09ebf1eadb86a1f00922339b78a7e93c3a2598540f6eff17aec53bbd6ef9251a'}
2025-07-08 23:19:09,101 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 23:19:09,476 DEBUG: https://api.bybit.com:443 "GET /v5/position/list?category=linear&settleCoin=USDT&api_key=aMKaaFNd57yeENDYF1&timestamp=1752005915000&recv_window=120000&sign=09ebf1eadb86a1f00922339b78a7e93c3a2598540f6eff17aec53bbd6ef9251a HTTP/1.1" 200 None
2025-07-08 23:19:09,480 INFO: Saved 3 positions as last known positions
2025-07-08 23:19:09,480 DEBUG: Using cached positions data (age: 0.7s)
2025-07-08 23:19:09,481 INFO: Saved 3 positions as last known positions
2025-07-08 23:19:09,481 DEBUG: Using cached positions data (age: 0.7s)
2025-07-08 23:19:09,482 DEBUG: Saved 3 positions to last_positions.json
2025-07-08 23:19:09,485 INFO: Starting DPG main loop
2025-07-08 23:19:20,646 INFO: Starting full manual analysis...
2025-07-08 23:19:20,647 DEBUG: (10000000AIDOGEUSDT) Starting analysis for 1h...
2025-07-08 23:19:20,647 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 23:19:20,647 DEBUG: (1000000BABYDOGEUSDT) Starting analysis for 1h...
2025-07-08 23:19:20,647 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000000AIDOGEUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1752005960647', 'recv_window': '120000', 'sign': '9b03282ff30827f0cc22f243f00229d445c7d9ba7f18efafe2e9f33321f91381'}
2025-07-08 23:19:20,647 DEBUG: (1000000CHEEMSUSDT) Starting analysis for 1h...
2025-07-08 23:19:20,648 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 23:19:20,649 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 23:19:20,649 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 23:19:20,649 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000000BABYDOGEUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1752005960648', 'recv_window': '120000', 'sign': '5db0e694d8b1d0671ad1e8b0a24daf593bb6c3342c48fe5864ea299ba536095e'}
2025-07-08 23:19:20,650 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000000CHEEMSUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1752005960649', 'recv_window': '120000', 'sign': '50f9767a2bd28d537e157942b3a950d976f880b7861dfcb71a8c0de243c33934'}
2025-07-08 23:19:20,651 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 23:19:20,651 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 23:19:20,955 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000000BABYDOGEUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1752005960648&recv_window=120000&sign=5db0e694d8b1d0671ad1e8b0a24daf593bb6c3342c48fe5864ea299ba536095e HTTP/1.1" 200 None
2025-07-08 23:19:20,973 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000000AIDOGEUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1752005960647&recv_window=120000&sign=9b03282ff30827f0cc22f243f00229d445c7d9ba7f18efafe2e9f33321f91381 HTTP/1.1" 200 136
2025-07-08 23:19:20,976 WARNING: No data returned for 10000000AIDOGEUSDT on 1h. RC: 0, Msg: OK
2025-07-08 23:19:20,980 DEBUG: (10000000AIDOGEUSDT) Fetch data returned empty. Returning None.
2025-07-08 23:19:20,991 DEBUG: (10000000AIDOGEUSDT) analyze_symbol returning: Signal=None
2025-07-08 23:19:21,015 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000000CHEEMSUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1752005960649&recv_window=120000&sign=50f9767a2bd28d537e157942b3a950d976f880b7861dfcb71a8c0de243c33934 HTTP/1.1" 200 None
2025-07-08 23:19:21,035 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/0.30 vs Threshold: 3.5
2025-07-08 23:19:21,050 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 23:19:21,051 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 23:19:21,072 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000000BABYDOGEUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1752005961051', 'recv_window': '120000', 'sign': 'e08b8f77aa03212cfb16b8be5be34e8b60dd0c450b0bc29bdd9bedea5d13c136'}
2025-07-08 23:19:21,086 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/0.50 vs Threshold: 3.5
2025-07-08 23:19:21,087 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 23:19:21,087 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-07-08 23:19:21,088 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-07-08 23:19:21,088 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000000CHEEMSUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1752005961088', 'recv_window': '120000', 'sign': 'a75462c89177661d56d5276922ded9119bc82ef0d20aafd7784c69d8fadf4086'}
2025-07-08 23:19:21,089 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-07-08 23:19:21,388 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000000BABYDOGEUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1752005961051&recv_window=120000&sign=e08b8f77aa03212cfb16b8be5be34e8b60dd0c450b0bc29bdd9bedea5d13c136 HTTP/1.1" 200 None
2025-07-08 23:19:21,446 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000000CHEEMSUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1752005961088&recv_window=120000&sign=a75462c89177661d56d5276922ded9119bc82ef0d20aafd7784c69d8fadf4086 HTTP/1.1" 200 4623
2025-07-08 23:19:21,502 DEBUG: Starting new HTTPS connection (2): api.bybit.com:443
2025-07-08 23:19:21,663 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000000BABYDOGEUSDT HTTP/1.1" 200 200
2025-07-08 23:19:21,888 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000000BABYDOGEUSDT HTTP/1.1" 200 329
2025-07-08 23:19:21,888 DEBUG: (1000000BABYDOGEUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 23:19:21,893 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000000CHEEMSUSDT HTTP/1.1" 200 201
2025-07-08 23:19:22,135 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000000CHEEMSUSDT HTTP/1.1" 200 321
2025-07-08 23:19:22,136 DEBUG: (1000000CHEEMSUSDT) analyze_symbol returning: Signal=HOLD
2025-07-08 23:19:22,136 INFO: Status: Analysis complete - 2/3 processed.
2025-07-08 23:19:28,633 DEBUG: Filter 'BUY' set to True
2025-07-08 23:19:28,634 DEBUG: apply_live_filters CALLED
2025-07-08 23:19:28,634 DEBUG: apply_live_filters: Found 2 valid results, 2 after sorting.
2025-07-08 23:19:28,634 DEBUG: apply_live_filters FINISHED
2025-07-08 23:19:29,189 DEBUG: Filter 'BUY' set to False
2025-07-08 23:19:29,189 DEBUG: apply_live_filters CALLED
2025-07-08 23:19:29,190 DEBUG: apply_live_filters: Found 2 valid results, 2 after sorting.
2025-07-08 23:19:29,190 DEBUG: Adding row for 1000000CHEEMSUSDT
2025-07-08 23:19:29,191 DEBUG: Adding row for 1000000BABYDOGEUSDT
2025-07-08 23:19:29,191 DEBUG: apply_live_filters FINISHED
2025-07-08 23:26:08,459 INFO: DPG main loop ended
2025-07-08 23:26:08,460 INFO: Shutting down GUI...
2025-07-08 23:26:08,471 INFO: GUI run completed.
2025-07-08 23:26:08,471 INFO: Shutting down application...
