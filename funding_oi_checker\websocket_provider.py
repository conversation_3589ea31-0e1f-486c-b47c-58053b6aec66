import asyncio
import json
import logging
import websockets
import threading
from typing import Dict, List, Callable, Optional
from datetime import datetime
import time

class BybitWebSocketProvider:
    """WebSocket-based data provider for real-time market data (no rate limits)"""
    
    def __init__(self):
        self.ws_url = "wss://stream.bybit.com/v5/public/linear"
        self.connections = {}
        self.subscriptions = {}
        self.data_callbacks = {}
        self.running = False
        self.loop = None
        self.thread = None
        
        # Data storage
        self.latest_data = {}
        self.funding_rates = {}
        self.open_interest = {}
        self.tickers = {}
        
    def start(self):
        """Start WebSocket connections in background thread"""
        if self.running:
            return
            
        self.running = True
        self.thread = threading.Thread(target=self._run_event_loop, daemon=True)
        self.thread.start()
        logging.info("WebSocket provider started")
    
    def stop(self):
        """Stop WebSocket connections"""
        self.running = False
        if self.loop:
            asyncio.run_coroutine_threadsafe(self._close_connections(), self.loop)
        logging.info("WebSocket provider stopped")
    
    def _run_event_loop(self):
        """Run asyncio event loop in background thread"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        try:
            self.loop.run_until_complete(self._maintain_connections())
        except Exception as e:
            logging.error(f"WebSocket event loop error: {e}")
        finally:
            self.loop.close()
    
    async def _maintain_connections(self):
        """Maintain WebSocket connections with auto-reconnect"""
        while self.running:
            try:
                # Connect to main stream
                async with websockets.connect(self.ws_url) as websocket:
                    logging.info("WebSocket connected")
                    
                    # Subscribe to initial channels
                    await self._subscribe_to_channels(websocket)
                    
                    # Listen for messages
                    async for message in websocket:
                        if not self.running:
                            break
                        await self._handle_message(message)
                        
            except Exception as e:
                logging.error(f"WebSocket connection error: {e}")
                if self.running:
                    await asyncio.sleep(5)  # Wait before reconnecting
    
    async def _subscribe_to_channels(self, websocket):
        """Subscribe to required data channels"""
        # Subscribe to ticker data for all symbols
        ticker_sub = {
            "op": "subscribe",
            "args": ["tickers.BTCUSDT", "tickers.ETHUSDT"]  # Start with major pairs
        }
        await websocket.send(json.dumps(ticker_sub))
        
        # Subscribe to funding rate updates
        funding_sub = {
            "op": "subscribe", 
            "args": ["instrument_info.linear"]
        }
        await websocket.send(json.dumps(funding_sub))
        
        logging.info("Subscribed to WebSocket channels")
    
    async def _handle_message(self, message):
        """Handle incoming WebSocket messages"""
        try:
            data = json.loads(message)
            
            if data.get("topic", "").startswith("tickers"):
                await self._handle_ticker_data(data)
            elif data.get("topic") == "instrument_info.linear":
                await self._handle_instrument_data(data)
                
        except Exception as e:
            logging.error(f"Error handling WebSocket message: {e}")
    
    async def _handle_ticker_data(self, data):
        """Handle ticker data updates"""
        try:
            if "data" in data:
                ticker_data = data["data"]
                symbol = ticker_data.get("symbol")
                
                if symbol:
                    self.tickers[symbol] = {
                        "price": float(ticker_data.get("lastPrice", 0)),
                        "volume": float(ticker_data.get("volume24h", 0)),
                        "timestamp": datetime.now()
                    }
                    
                    # Trigger callbacks
                    await self._trigger_callbacks("ticker", symbol, self.tickers[symbol])
                    
        except Exception as e:
            logging.error(f"Error handling ticker data: {e}")
    
    async def _handle_instrument_data(self, data):
        """Handle instrument info updates (funding rates)"""
        try:
            if "data" in data:
                for instrument in data["data"]:
                    symbol = instrument.get("symbol")
                    if symbol:
                        self.funding_rates[symbol] = {
                            "funding_rate": float(instrument.get("fundingRate", 0)),
                            "next_funding_time": instrument.get("nextFundingTime"),
                            "timestamp": datetime.now()
                        }
                        
                        # Trigger callbacks
                        await self._trigger_callbacks("funding", symbol, self.funding_rates[symbol])
                        
        except Exception as e:
            logging.error(f"Error handling instrument data: {e}")
    
    async def _trigger_callbacks(self, data_type, symbol, data):
        """Trigger registered callbacks for data updates"""
        callback_key = f"{data_type}_{symbol}"
        if callback_key in self.data_callbacks:
            try:
                callback = self.data_callbacks[callback_key]
                if callback:
                    # Run callback in thread pool to avoid blocking
                    loop = asyncio.get_event_loop()
                    await loop.run_in_executor(None, callback, symbol, data)
            except Exception as e:
                logging.error(f"Error in callback for {callback_key}: {e}")
    
    def subscribe_to_symbols(self, symbols: List[str]):
        """Subscribe to data for specific symbols"""
        if not self.running:
            self.start()
        
        # Add symbols to subscription list
        for symbol in symbols:
            if symbol not in self.subscriptions:
                self.subscriptions[symbol] = True
                
                # Send subscription via WebSocket
                if self.loop:
                    asyncio.run_coroutine_threadsafe(
                        self._add_symbol_subscription(symbol), 
                        self.loop
                    )
    
    async def _add_symbol_subscription(self, symbol):
        """Add subscription for a new symbol"""
        # This would send additional subscription messages
        # Implementation depends on current WebSocket connection state
        pass
    
    def register_callback(self, data_type: str, symbol: str, callback: Callable):
        """Register callback for data updates"""
        callback_key = f"{data_type}_{symbol}"
        self.data_callbacks[callback_key] = callback
    
    def get_latest_data(self, symbol: str) -> Dict:
        """Get latest combined data for a symbol"""
        ticker = self.tickers.get(symbol, {})
        funding = self.funding_rates.get(symbol, {})
        
        return {
            "symbol": symbol,
            "price": ticker.get("price", 0),
            "volume": ticker.get("volume", 0),
            "funding_rate": funding.get("funding_rate", 0),
            "timestamp": max(
                ticker.get("timestamp", datetime.min),
                funding.get("timestamp", datetime.min)
            )
        }
    
    def get_all_latest_data(self) -> Dict[str, Dict]:
        """Get latest data for all subscribed symbols"""
        result = {}
        all_symbols = set(self.tickers.keys()) | set(self.funding_rates.keys())
        
        for symbol in all_symbols:
            result[symbol] = self.get_latest_data(symbol)
        
        return result
    
    async def _close_connections(self):
        """Close all WebSocket connections"""
        # Close connections gracefully
        for connection in self.connections.values():
            try:
                await connection.close()
            except:
                pass
        self.connections.clear()

# Hybrid provider that uses WebSocket for real-time data and REST for historical
class HybridDataProvider:
    """Combines WebSocket real-time data with REST API for historical data"""
    
    def __init__(self, rest_provider):
        self.rest_provider = rest_provider
        self.ws_provider = BybitWebSocketProvider()
        self.use_websocket = True
        
    def start_realtime(self, symbols: List[str]):
        """Start real-time WebSocket data for symbols"""
        if self.use_websocket:
            self.ws_provider.start()
            self.ws_provider.subscribe_to_symbols(symbols)
    
    def stop_realtime(self):
        """Stop real-time data"""
        if self.use_websocket:
            self.ws_provider.stop()
    
    def get_realtime_data(self, symbols: List[str]) -> Dict[str, Dict]:
        """Get real-time data (WebSocket) or fallback to REST"""
        if self.use_websocket and self.ws_provider.running:
            # Get real-time data from WebSocket
            ws_data = self.ws_provider.get_all_latest_data()
            
            # Fill in missing data with REST API
            result = {}
            for symbol in symbols:
                if symbol in ws_data and ws_data[symbol].get("price", 0) > 0:
                    # Use WebSocket data
                    result[symbol] = self._enrich_ws_data(ws_data[symbol])
                else:
                    # Fallback to REST API
                    try:
                        result[symbol] = self.rest_provider.get_market_data(symbol)
                    except Exception as e:
                        logging.error(f"Error getting REST data for {symbol}: {e}")
            
            return result
        else:
            # Fallback to REST API
            return self.rest_provider.get_multiple_symbols_data(symbols)
    
    def _enrich_ws_data(self, ws_data: Dict) -> Dict:
        """Enrich WebSocket data with additional metrics"""
        # Add calculated fields that WebSocket doesn't provide
        symbol = ws_data["symbol"]
        
        # Get additional data from REST API if needed
        try:
            rest_data = self.rest_provider.get_market_data(symbol)
            
            # Combine WebSocket real-time data with REST calculated data
            return {
                **rest_data,  # Base data from REST
                "price": ws_data.get("price", rest_data.get("price", 0)),  # Real-time price
                "volume": ws_data.get("volume", rest_data.get("volume", 0)),  # Real-time volume
                "funding_rate": ws_data.get("funding_rate", rest_data.get("funding_rate", 0)),  # Real-time funding
                "timestamp": ws_data.get("timestamp", rest_data.get("timestamp"))
            }
        except Exception as e:
            logging.error(f"Error enriching WebSocket data for {symbol}: {e}")
            return ws_data
