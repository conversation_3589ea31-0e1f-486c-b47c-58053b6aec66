#!/usr/bin/env python3
"""
Advanced Crypto Trading Dashboard Launcher
==========================================

This script launches the comprehensive trading dashboard with:
- Real-time funding rates, open interest, CVD, and volume analysis
- Smart alert system with cooldown management
- Telegram bot integration
- Modern PyQt5 interface with dark/light themes
- Per-symbol configuration
- Alert history and export functionality

Usage:
    python run_dashboard.py

Requirements:
    - PyQt5
    - pybit
    - pandas
    - numpy
    - requests
    - python-telegram-bot

Make sure to configure your API keys in config.py before running.
"""

import sys
import os
import logging
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        'PyQt5',
        'pybit', 
        'pandas',
        'numpy',
        'requests',
        'telegram'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\nInstall missing packages with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_config():
    """Check if configuration is properly set up"""
    try:
        from config import api_key, api_secret, TELEGRAM_TOKEN, TELEGRAM_CHAT_ID
        
        if not api_key or api_key == "your_api_key_here":
            print("❌ Please configure your Bybit API key in config.py")
            return False
        
        if not api_secret or api_secret == "your_api_secret_here":
            print("❌ Please configure your Bybit API secret in config.py")
            return False
        
        print("✅ Configuration looks good")
        return True
        
    except ImportError as e:
        print(f"❌ Error importing config: {e}")
        return False

def setup_logging():
    """Setup logging configuration"""
    log_dir = current_dir / "logs"
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / 'dashboard.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def print_banner():
    """Print application banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        📊 CRYPTO FUNDING RATE & OI CHECKER 📊                ║
    ║                                                              ║
    ║  ✨ Features:                                                ║
    ║    • Real-time Funding Rates & Open Interest                ║
    ║    • Cumulative Volume Delta (CVD) Analysis                 ║
    ║    • Volume Spike Detection                                  ║
    ║    • Smart Alert Engine with Cooldowns                      ║
    ║    • Telegram Bot Integration                                ║
    ║    • Multi-Factor Signal Scanner                            ║
    ║    • Modern Dark/Light Theme UI                             ║
    ║    • Per-Symbol Alert Configuration                         ║
    ║    • Alert History & Export                                 ║
    ║                                                              ║
    ║  📊 Signal Types:                                           ║
    ║    • Bullish/Bearish Squeeze Setups                        ║
    ║    • Reversal Pattern Detection                             ║
    ║    • Volume Spike Alerts                                    ║
    ║    • CVD Divergence Signals                                 ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def main():
    """Main launcher function"""
    print_banner()
    
    print("🔍 Checking dependencies...")
    if not check_dependencies():
        sys.exit(1)
    
    print("⚙️  Checking configuration...")
    if not check_config():
        sys.exit(1)
    
    print("📝 Setting up logging...")
    setup_logging()
    
    print("📊 Starting Funding Rate & OI Checker...")
    
    try:
        # Import and run the funding/OI checker
        from trading_dashboard import main as run_checker
        run_checker()
        
    except KeyboardInterrupt:
        print("\n👋 Dashboard stopped by user")
        sys.exit(0)
        
    except Exception as e:
        logging.error(f"Fatal error starting dashboard: {e}")
        print(f"❌ Fatal error: {e}")
        print("Check the logs for more details")
        sys.exit(1)

if __name__ == "__main__":
    main()
