import sys
import time
import logging
import threading
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
    QHeaderView, QComboBox, QStatusBar, QTabWidget, QTextEdit,
    QCheckBox, QSpinBox, QGroupBox, QGridLayout, QProgressBar,
    QSplitter, QFrame, QDialog, QListWidget, QDialogButtonBox,
    QListWidgetItem
)
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QTimer
from PyQt5.QtGui import QFont, QColor, QPalette

# Import our modules
from config_manager import ConfigManager
from data_provider import DataProvider
from alert_engine import Enhanced<PERSON>lert<PERSON>ngine, MarketData, AlertType
from bot_listener import TelegramBotListener
from config import TELEGRAM_TOKEN, TELEGRAM_CHAT_ID
from performance_monitor import PerformanceContext, performance_monitor, get_performance_stats

# Try to import WebSocket provider (optional)
try:
    from websocket_provider import HybridDataProvider
    WEBSOCKET_AVAILABLE = True
except ImportError:
    WEBSOCKET_AVAILABLE = False
    logging.warning("WebSocket provider not available, using REST API only")

class SymbolSelectorDialog(QDialog):
    """Dialog for selecting symbols from available list"""

    def __init__(self, data_provider, current_symbols, parent=None):
        super().__init__(parent)
        self.data_provider = data_provider
        self.current_symbols = current_symbols
        self.selected_symbols = []

        self.setWindowTitle("Select Trading Symbols")
        self.setGeometry(200, 200, 400, 500)

        layout = QVBoxLayout(self)

        # Instructions
        layout.addWidget(QLabel("Select symbols to monitor (max 20):"))

        # Search box
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("Search symbols...")
        self.search_box.textChanged.connect(self.filter_symbols)
        layout.addWidget(self.search_box)

        # Symbol list
        self.symbol_list = QListWidget()
        self.symbol_list.setSelectionMode(QListWidget.MultiSelection)
        layout.addWidget(self.symbol_list)

        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal, self
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # Load symbols
        self.load_symbols()

    def load_symbols(self):
        """Load available symbols"""
        try:
            symbols = self.data_provider.fetch_asset_list()

            for symbol in symbols:
                item = QListWidgetItem(symbol)
                item.setCheckState(Qt.Checked if symbol in self.current_symbols else Qt.Unchecked)
                self.symbol_list.addItem(item)

        except Exception as e:
            logging.error(f"Error loading symbols: {e}")

    def filter_symbols(self, text):
        """Filter symbols based on search text"""
        for i in range(self.symbol_list.count()):
            item = self.symbol_list.item(i)
            item.setHidden(text.upper() not in item.text().upper())

    def accept(self):
        """Accept dialog and get selected symbols"""
        self.selected_symbols = []
        for i in range(self.symbol_list.count()):
            item = self.symbol_list.item(i)
            if item.checkState() == Qt.Checked:
                self.selected_symbols.append(item.text())

        # Limit to 20 symbols
        if len(self.selected_symbols) > 20:
            self.selected_symbols = self.selected_symbols[:20]

        super().accept()

class DataUpdateThread(QThread):
    """Thread for updating market data with progress updates"""
    data_updated = pyqtSignal(dict)
    progress_update = pyqtSignal(str, int)  # message, percentage

    def __init__(self, data_provider, symbols, update_interval=30):
        super().__init__()
        self.data_provider = data_provider
        self.symbols = symbols
        self.update_interval = update_interval
        self.running = True

    def run(self):
        cycle_count = 0
        logging.info(f"Auto-refresh thread started with {len(self.symbols)} symbols, {self.update_interval}s interval")

        while self.running:
            try:
                cycle_count += 1
                logging.info(f"Auto-refresh cycle {cycle_count} starting for {len(self.symbols)} symbols")
                self.progress_update.emit(f"Auto-refresh cycle {cycle_count} starting...", 0)

                # Check if we're still supposed to be running
                if not self.running:
                    logging.info(f"Auto-refresh stopped before cycle {cycle_count}")
                    break

                # Use concurrent processing for auto-refresh (faster, non-blocking)
                data = self.data_provider.get_multiple_symbols_data_concurrent(
                    self.symbols,
                    max_workers=6,  # More conservative to reduce API failures
                    progress_callback=self.progress_update.emit
                )

                if data:
                    self.data_updated.emit(data)
                    logging.info(f"Auto-refresh cycle {cycle_count} completed: {len(data)} symbols processed")
                    self.progress_update.emit(f"Auto-refresh cycle {cycle_count} completed", 100)
                else:
                    logging.warning(f"Auto-refresh cycle {cycle_count} returned no data")

                # Wait for next update with countdown logging
                logging.info(f"Auto-refresh waiting {self.update_interval} seconds until next cycle...")

                # Use smaller sleep intervals to be more responsive to stop signals
                sleep_count = 0
                while sleep_count < self.update_interval and self.running:
                    time.sleep(1)
                    sleep_count += 1

                    # Log countdown every 10 seconds
                    remaining = self.update_interval - sleep_count
                    if remaining % 10 == 0 and remaining > 0:
                        logging.debug(f"Auto-refresh next cycle in {remaining} seconds")

                if not self.running:
                    logging.info("Auto-refresh stopped during wait period")
                    break

            except Exception as e:
                logging.error(f"Error in auto-refresh cycle {cycle_count}: {e}")
                self.progress_update.emit(f"Error in cycle {cycle_count}: {str(e)}", 0)

                # Wait before retrying, but check running status
                for i in range(5):
                    if not self.running:
                        break
                    time.sleep(1)

        logging.info(f"Auto-refresh thread stopped after {cycle_count} cycles")

    def stop(self):
        logging.info("Stopping auto-refresh thread...")
        self.running = False

class ManualRefreshThread(QThread):
    """Thread for manual data refresh to prevent UI freezing"""
    data_ready = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    progress_update = pyqtSignal(str, int)  # message, percentage

    def __init__(self, data_provider, symbols, max_symbols=None):
        super().__init__()
        self.data_provider = data_provider
        self.symbols = symbols
        self.max_symbols = max_symbols or len(symbols)  # No limit by default

    def run(self):
        try:
            # Process all symbols for manual refresh (baseline collection)
            refresh_symbols = self.symbols

            self.progress_update.emit(f"Starting baseline scan for {len(refresh_symbols)} symbols...", 0)
            logging.info(f"Manual refresh starting for {len(refresh_symbols)} symbols")

            # Track performance
            with PerformanceContext(performance_monitor, "manual_scan", len(refresh_symbols)):
                # Use concurrent processing for much faster, non-blocking data fetch
                data = self.data_provider.get_multiple_symbols_data_concurrent(
                    refresh_symbols,
                    max_workers=6,  # More conservative to reduce API failures
                    progress_callback=self.progress_update.emit
                )

            if data:
                self.data_ready.emit(data)
                self.progress_update.emit(f"Baseline scan completed - {len(data)} symbols processed", 100)
                logging.info(f"Manual refresh completed successfully for {len(data)} symbols")
            else:
                self.error_occurred.emit("No data received")

        except Exception as e:
            error_msg = str(e)
            logging.error(f"Manual refresh thread error: {error_msg}")
            self.error_occurred.emit(error_msg)

class FundingOIChecker(QMainWindow):
    """Main funding rate and open interest checker application"""
    
    def __init__(self):
        super().__init__()
        
        # Initialize components
        self.config_manager = ConfigManager()

        # Initialize data provider (with WebSocket if available)
        if WEBSOCKET_AVAILABLE:
            self.data_provider = HybridDataProvider(DataProvider())
            logging.info("Using hybrid WebSocket + REST data provider")
        else:
            self.data_provider = DataProvider()
            logging.info("Using REST-only data provider")

        self.alert_engine = EnhancedAlertEngine(self.config_manager)
        
        # Initialize Telegram bot if enabled
        self.telegram_bot = None
        if (self.config_manager.get('alerts.enable_telegram', True) and 
            TELEGRAM_TOKEN and TELEGRAM_CHAT_ID):
            try:
                self.telegram_bot = TelegramBotListener(
                    TELEGRAM_TOKEN, TELEGRAM_CHAT_ID, 
                    self.alert_engine, self.data_provider
                )
                self.telegram_bot.start_listener()
            except Exception as e:
                logging.error(f"Failed to start Telegram bot: {e}")
        
        # Data storage
        self.market_data = {}
        self.symbols = self.config_manager.symbols  # Support all symbols
        self.auto_refresh_enabled = False
        
        # UI components
        self.table = None
        self.status_bar = None
        self.alert_log = None
        self.data_thread = None
        
        # Initialize UI
        self.init_ui()

        # Don't start auto-refresh by default - user must enable it
        # self.setup_data_updates()

        # Apply theme
        self.apply_theme()

        # Set initial alert status
        self.update_alert_status()

        logging.info("Funding Rate & OI Checker initialized successfully")
    
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("📊 Crypto Funding Rate & OI Checker")
        self.setGeometry(100, 100, 1400, 800)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Create top controls
        controls_layout = self.create_controls()
        main_layout.addLayout(controls_layout)
        
        # Create splitter for main content
        splitter = QSplitter(Qt.Horizontal)
        
        # Create main table
        self.create_main_table()
        splitter.addWidget(self.table)
        
        # Create side panel
        side_panel = self.create_side_panel()
        splitter.addWidget(side_panel)
        
        # Set splitter proportions
        splitter.setSizes([1000, 400])
        main_layout.addWidget(splitter)

        # Add progress bar (initially hidden)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setTextVisible(True)
        main_layout.addWidget(self.progress_bar)

        # Create status bar
        self.create_status_bar()

        # Add progress bar for refresh operations
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximum(0)  # Indeterminate progress
        main_layout.addWidget(self.progress_bar)
    
    def create_controls(self):
        """Create top control panel"""
        layout = QHBoxLayout()
        
        # Symbol input
        layout.addWidget(QLabel("Symbols:"))
        self.symbol_input = QLineEdit()
        self.symbol_input.setText(",".join(self.symbols))
        self.symbol_input.setPlaceholderText("Enter symbols separated by commas")
        layout.addWidget(self.symbol_input)

        # Symbol selector button
        select_btn = QPushButton("📋 Select")
        select_btn.clicked.connect(self.show_symbol_selector)
        layout.addWidget(select_btn)

        # Update button
        update_btn = QPushButton("Update Symbols")
        update_btn.clicked.connect(self.update_symbols)
        layout.addWidget(update_btn)
        
        # Auto-refresh controls
        self.auto_refresh_cb = QCheckBox("Auto Refresh")
        self.auto_refresh_cb.setChecked(False)
        self.auto_refresh_cb.stateChanged.connect(self.toggle_auto_refresh)
        layout.addWidget(self.auto_refresh_cb)

        # Refresh interval
        layout.addWidget(QLabel("Interval (s):"))
        self.refresh_spin = QSpinBox()
        self.refresh_spin.setRange(10, 300)
        self.refresh_spin.setValue(self.config_manager.get('ui.auto_refresh_interval', 30))
        self.refresh_spin.valueChanged.connect(self.update_refresh_interval)
        layout.addWidget(self.refresh_spin)

        # Manual refresh button
        self.refresh_btn = QPushButton("🔄 Refresh")
        self.refresh_btn.clicked.connect(self.manual_refresh)
        layout.addWidget(self.refresh_btn)

        # Cancel button (initially hidden)
        self.cancel_btn = QPushButton("❌ Cancel")
        self.cancel_btn.clicked.connect(self.cancel_operation)
        self.cancel_btn.setVisible(False)
        layout.addWidget(self.cancel_btn)

        # Load all symbols button
        load_all_btn = QPushButton("📋 Load All Assets")
        load_all_btn.clicked.connect(self.load_all_assets)
        layout.addWidget(load_all_btn)

        # Theme toggle
        theme_btn = QPushButton("🌙 Theme")
        theme_btn.clicked.connect(self.toggle_theme)
        layout.addWidget(theme_btn)
        
        layout.addStretch()
        
        return layout
    
    def create_main_table(self):
        """Create the main data table"""
        self.table = QTableWidget()
        
        # Define columns
        columns = [
            "Symbol", "Price", "Funding Rate", "Funding Time", 
            "OI Change", "CVD Trend", "Volume", "Vol Ratio", 
            "Sentiment", "Last Update"
        ]
        
        self.table.setColumnCount(len(columns))
        self.table.setHorizontalHeaderLabels(columns)
        
        # Configure table
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSortingEnabled(True)
        
        # Set column widths
        header = self.table.horizontalHeader()
        header.resizeSection(0, 100)  # Symbol
        header.resizeSection(1, 100)  # Price
        header.resizeSection(2, 120)  # Funding Rate
        header.resizeSection(3, 100)  # Funding Time
        header.resizeSection(4, 100)  # OI Change
        header.resizeSection(5, 120)  # CVD Trend
        header.resizeSection(6, 100)  # Volume
        header.resizeSection(7, 80)   # Vol Ratio
        header.resizeSection(8, 100)  # Sentiment
    
    def create_side_panel(self):
        """Create side panel with alerts and settings"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Create tabs
        tabs = QTabWidget()
        
        # Alerts tab
        alerts_tab = QWidget()
        alerts_layout = QVBoxLayout(alerts_tab)
        
        alerts_layout.addWidget(QLabel("🚨 Recent Alerts"))
        
        self.alert_log = QTextEdit()
        self.alert_log.setMaximumHeight(200)
        self.alert_log.setReadOnly(True)
        alerts_layout.addWidget(self.alert_log)
        
        # Alert controls
        alert_controls = QHBoxLayout()
        clear_alerts_btn = QPushButton("Clear")
        clear_alerts_btn.clicked.connect(self.clear_alerts)
        alert_controls.addWidget(clear_alerts_btn)
        
        export_alerts_btn = QPushButton("Export")
        export_alerts_btn.clicked.connect(self.export_alerts)
        alert_controls.addWidget(export_alerts_btn)
        
        alerts_layout.addLayout(alert_controls)
        tabs.addTab(alerts_tab, "Alerts")
        
        # Settings tab
        settings_tab = self.create_settings_tab()
        tabs.addTab(settings_tab, "Settings")
        
        # Statistics tab
        stats_tab = self.create_stats_tab()
        tabs.addTab(stats_tab, "Stats")
        
        layout.addWidget(tabs)
        
        return widget
    
    def create_settings_tab(self):
        """Create settings tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Alert settings group
        alert_group = QGroupBox("Alert Settings")
        alert_layout = QGridLayout(alert_group)
        
        # Enable alerts checkbox
        self.enable_alerts_cb = QCheckBox("Enable Alerts")
        self.enable_alerts_cb.setChecked(self.config_manager.get('alerts.enable_telegram', True))
        alert_layout.addWidget(self.enable_alerts_cb, 0, 0, 1, 2)
        
        # Cooldown setting
        alert_layout.addWidget(QLabel("Cooldown (min):"), 1, 0)
        self.cooldown_spin = QSpinBox()
        self.cooldown_spin.setRange(1, 60)
        self.cooldown_spin.setValue(self.config_manager.get('alerts.cooldown_minutes', 5))
        alert_layout.addWidget(self.cooldown_spin, 1, 1)
        
        # Sentiment threshold
        alert_layout.addWidget(QLabel("Sentiment Threshold:"), 2, 0)
        self.sentiment_spin = QSpinBox()
        self.sentiment_spin.setRange(10, 100)
        self.sentiment_spin.setValue(int(self.config_manager.get('alerts.sentiment_threshold', 0.7) * 100))
        self.sentiment_spin.setSuffix("%")
        alert_layout.addWidget(self.sentiment_spin, 2, 1)
        
        layout.addWidget(alert_group)
        
        # Data settings group
        data_group = QGroupBox("Data Settings")
        data_layout = QGridLayout(data_group)
        
        # Enable CVD
        self.enable_cvd_cb = QCheckBox("Enable CVD Analysis")
        self.enable_cvd_cb.setChecked(self.config_manager.get('data.enable_cvd', True))
        data_layout.addWidget(self.enable_cvd_cb, 0, 0, 1, 2)
        
        # Enable volume analysis
        self.enable_volume_cb = QCheckBox("Enable Volume Analysis")
        self.enable_volume_cb.setChecked(self.config_manager.get('data.enable_volume_analysis', True))
        data_layout.addWidget(self.enable_volume_cb, 1, 0, 1, 2)
        
        layout.addWidget(data_group)
        
        # Save settings button
        save_btn = QPushButton("Save Settings")
        save_btn.clicked.connect(self.save_settings)
        layout.addWidget(save_btn)
        
        layout.addStretch()
        
        return widget
    
    def create_stats_tab(self):
        """Create statistics tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Statistics labels
        self.stats_labels = {
            'total_symbols': QLabel("Total Symbols: 0"),
            'active_alerts': QLabel("Active Alerts: 0"),
            'bullish_signals': QLabel("Bullish Signals: 0"),
            'bearish_signals': QLabel("Bearish Signals: 0"),
            'volume_spikes': QLabel("Volume Spikes: 0"),
            'last_update': QLabel("Last Update: Never")
        }
        
        for label in self.stats_labels.values():
            layout.addWidget(label)
        
        layout.addStretch()
        
        return widget
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Add status indicators
        self.connection_status = QLabel("🔴 Disconnected")
        self.status_bar.addWidget(self.connection_status)

        self.alert_status = QLabel("🔕 Alerts: Off")
        self.status_bar.addPermanentWidget(self.alert_status)

        self.scan_status = QLabel("📊 Scan: Ready")
        self.status_bar.addPermanentWidget(self.scan_status)

        self.update_count = QLabel("Updates: 0")
        self.status_bar.addPermanentWidget(self.update_count)

        self.performance_status = QLabel("⚡ Performance: Ready")
        self.status_bar.addPermanentWidget(self.performance_status)
    
    def setup_data_updates(self):
        """Setup automatic data updates (only if auto-refresh is enabled)"""
        if self.auto_refresh_enabled:
            # Stop existing thread if running
            if hasattr(self, 'data_thread') and self.data_thread:
                logging.info("Stopping existing auto-refresh thread")
                self.data_thread.stop()
                self.data_thread.wait(3000)  # Wait up to 3 seconds

            refresh_interval = self.config_manager.get('ui.auto_refresh_interval', 30)
            logging.info(f"Starting auto-refresh thread: {len(self.symbols)} symbols, {refresh_interval}s interval")

            self.data_thread = DataUpdateThread(
                self.data_provider,
                self.symbols,
                refresh_interval
            )
            self.data_thread.data_updated.connect(self.update_table_data)
            self.data_thread.progress_update.connect(self.on_auto_refresh_progress)
            self.data_thread.start()

            # Update connection status
            self.connection_status.setText("🟢 Auto-Refresh Active")
            logging.info("Auto-refresh thread started successfully")
        else:
            self.connection_status.setText("🟡 Manual Mode")
    
    def update_table_data(self, data: Dict, is_manual_scan: bool = False):
        """Update table with new market data"""
        try:
            self.market_data = data

            # Check for duplicate symbols in data
            symbols = list(data.keys())
            unique_symbols = list(set(symbols))
            if len(symbols) != len(unique_symbols):
                logging.warning(f"Duplicate symbols detected in data: {len(symbols)} total, {len(unique_symbols)} unique")
                # Remove duplicates by converting to dict (keeps last occurrence)
                data = {symbol: data[symbol] for symbol in unique_symbols}
                self.market_data = data

            # Clear table completely to avoid duplicates
            self.table.clearContents()
            self.table.setRowCount(0)

            # Set new row count
            self.table.setRowCount(len(data))

            alerts_generated = 0

            for row, (symbol, market_data) in enumerate(data.items()):
                self.populate_table_row(row, market_data)

                # Process alerts (with manual scan flag)
                alerts = self.process_alerts(market_data, is_manual_scan)
                alerts_generated += len(alerts)

            # Update statistics
            self.update_statistics()

            # Update scan status
            self.update_scan_status()

            # Update performance status
            self.update_performance_status()

            # Update alert status
            self.update_alert_status()

            # Update status
            if is_manual_scan:
                self.update_count.setText(f"Baseline: {len(data)} symbols")
            else:
                self.update_count.setText(f"Updates: {len(data)} | Alerts: {alerts_generated}")

        except Exception as e:
            logging.error(f"Error updating table data: {e}")
    
    def populate_table_row(self, row: int, data: Dict):
        """Populate a single table row with market data"""
        try:
            # Symbol
            self.table.setItem(row, 0, QTableWidgetItem(data['symbol']))
            
            # Price
            price_item = QTableWidgetItem(f"${data['price']:.4f}")
            self.table.setItem(row, 1, price_item)
            
            # Funding Rate
            fr = data['funding_rate']
            fr_text = f"{fr*100:.4f}%"
            fr_item = QTableWidgetItem(fr_text)
            
            # Color code funding rate (darker colors for better contrast)
            if fr > 0.01:
                fr_item.setBackground(QColor(80, 40, 40))  # Dark red
            elif fr < -0.01:
                fr_item.setBackground(QColor(40, 80, 40))  # Dark green
            
            self.table.setItem(row, 2, fr_item)
            
            # Funding Time
            self.table.setItem(row, 3, QTableWidgetItem(data['funding_time']))
            
            # OI Change
            oi_change = data['oi_change']
            oi_text = f"{oi_change*100:.2f}%"
            oi_item = QTableWidgetItem(oi_text)
            
            # Color code OI change (darker colors for better contrast)
            if oi_change > 0.02:
                oi_item.setBackground(QColor(80, 80, 40))  # Dark yellow/orange
            elif oi_change < -0.02:
                oi_item.setBackground(QColor(40, 40, 80))  # Dark blue
            
            self.table.setItem(row, 4, oi_item)
            
            # CVD Trend
            self.table.setItem(row, 5, QTableWidgetItem(data['cvd_trend']))
            
            # Volume
            volume_text = f"{data['volume']:,.0f}"
            self.table.setItem(row, 6, QTableWidgetItem(volume_text))
            
            # Volume Ratio
            vol_ratio = data['volume_ratio']
            vol_ratio_text = f"{vol_ratio:.1f}x"
            vol_ratio_item = QTableWidgetItem(vol_ratio_text)
            
            # Highlight volume spikes (darker color for better contrast)
            if vol_ratio > 2.0:
                vol_ratio_item.setBackground(QColor(80, 40, 80))  # Dark magenta
            
            self.table.setItem(row, 7, vol_ratio_item)
            
            # Sentiment
            sentiment = data['sentiment_score']
            sentiment_text = f"{sentiment:.2f}"
            sentiment_item = QTableWidgetItem(sentiment_text)
            
            # Color code sentiment (darker colors for better contrast)
            if sentiment > 0.3:
                sentiment_item.setBackground(QColor(40, 80, 40))  # Dark green
            elif sentiment < -0.3:
                sentiment_item.setBackground(QColor(80, 40, 40))  # Dark red
            
            self.table.setItem(row, 8, sentiment_item)
            
            # Last Update
            update_time = data['timestamp'].strftime('%H:%M:%S')
            self.table.setItem(row, 9, QTableWidgetItem(update_time))
            
        except Exception as e:
            logging.error(f"Error populating table row: {e}")
    
    def process_alerts(self, market_data: Dict, is_manual_scan: bool = False) -> List:
        """Process market data for alerts"""
        try:
            # Convert to MarketData object
            data = MarketData(
                symbol=market_data['symbol'],
                price=market_data['price'],
                funding_rate=market_data['funding_rate'],
                oi_value=market_data['oi_value'],
                oi_change=market_data['oi_change'],
                cvd=market_data['cvd_value'],
                volume=market_data['volume'],
                volume_avg=market_data['volume_avg'],
                timestamp=market_data['timestamp']
            )

            # Process through alert engine with manual scan flag
            alerts = self.alert_engine.process_market_data(data, is_manual_scan)

            # Handle alerts (only if not manual scan)
            if not is_manual_scan:
                for alert in alerts:
                    self.handle_alert(alert)

            return alerts

        except Exception as e:
            logging.error(f"Error processing alerts: {e}")
            return []
    
    def handle_alert(self, alert):
        """Handle a new alert"""
        try:
            # Add to alert log
            alert_text = f"[{alert.timestamp.strftime('%H:%M:%S')}] {alert.message}\n"
            self.alert_log.append(alert_text)
            
            # Send to Telegram if enabled
            if self.telegram_bot and self.enable_alerts_cb.isChecked():
                self.telegram_bot.send_alert_notification(alert)
            
            # Update alert status
            self.alert_status.setText("🔔 Alerts: On")
            
        except Exception as e:
            logging.error(f"Error handling alert: {e}")
    
    def update_statistics(self):
        """Update statistics display"""
        try:
            total_symbols = len(self.market_data)
            bullish_count = sum(1 for data in self.market_data.values() if data['sentiment_score'] > 0.3)
            bearish_count = sum(1 for data in self.market_data.values() if data['sentiment_score'] < -0.3)
            volume_spikes = sum(1 for data in self.market_data.values() if data['volume_spike'])
            recent_alerts = len(self.alert_engine.get_recent_alerts(1))
            
            self.stats_labels['total_symbols'].setText(f"Total Symbols: {total_symbols}")
            self.stats_labels['active_alerts'].setText(f"Active Alerts: {recent_alerts}")
            self.stats_labels['bullish_signals'].setText(f"Bullish Signals: {bullish_count}")
            self.stats_labels['bearish_signals'].setText(f"Bearish Signals: {bearish_count}")
            self.stats_labels['volume_spikes'].setText(f"Volume Spikes: {volume_spikes}")
            self.stats_labels['last_update'].setText(f"Last Update: {datetime.now().strftime('%H:%M:%S')}")
            
        except Exception as e:
            logging.error(f"Error updating statistics: {e}")

    def update_scan_status(self):
        """Update scan status display"""
        try:
            scan_status = self.alert_engine.get_scan_status()

            if not scan_status['initial_scan_completed']:
                self.scan_status.setText("📊 Scan: Collecting Baseline")
            elif not scan_status['auto_refresh_enabled']:
                self.scan_status.setText("📊 Scan: Manual Mode")
            elif scan_status['should_evaluate_alerts']:
                self.scan_status.setText("📊 Scan: Alert Mode Active")
            else:
                self.scan_status.setText("📊 Scan: Ready")

        except Exception as e:
            logging.error(f"Error updating scan status: {e}")

    def update_performance_status(self):
        """Update performance status display"""
        try:
            stats = get_performance_stats()

            if stats['symbols_per_second'] > 0:
                self.performance_status.setText(
                    f"⚡ {stats['symbols_per_second']:.1f} sym/s | "
                    f"{stats['avg_duration']:.1f}s avg"
                )
            else:
                self.performance_status.setText("⚡ Performance: Ready")

        except Exception as e:
            logging.error(f"Error updating performance status: {e}")

    def update_alert_status(self):
        """Update alert status display"""
        try:
            scan_status = self.alert_engine.get_scan_status()

            # Check if alerts are enabled in config
            alerts_enabled_in_config = self.config_manager.get('alerts.enable_alerts', True)

            if not alerts_enabled_in_config:
                self.alert_status.setText("🔕 Alerts: Disabled in Config")
            elif not scan_status['initial_scan_completed']:
                self.alert_status.setText("🔕 Alerts: Waiting for Baseline")
            elif not scan_status['auto_refresh_enabled']:
                self.alert_status.setText("🔕 Alerts: Auto-Refresh Required")
            elif scan_status['should_evaluate_alerts']:
                self.alert_status.setText("🔔 Alerts: Active")
            else:
                self.alert_status.setText("🔕 Alerts: Standby")

        except Exception as e:
            logging.error(f"Error updating alert status: {e}")
            self.alert_status.setText("🔕 Alerts: Error")
    
    def apply_theme(self):
        """Apply dark theme to the application"""
        if self.config_manager.get('ui.theme') == 'dark':
            self.setStyleSheet("""
                QMainWindow {
                    background-color: #2b2b2b;
                    color: #ffffff;
                }
                QTableWidget {
                    background-color: #3c3c3c;
                    alternate-background-color: #4a4a4a;
                    color: #ffffff;
                    gridline-color: #555555;
                }
                QHeaderView::section {
                    background-color: #404040;
                    color: #ffffff;
                    border: 1px solid #555555;
                    padding: 5px;
                }
                QPushButton {
                    background-color: #404040;
                    color: #ffffff;
                    border: 1px solid #555555;
                    padding: 5px;
                    border-radius: 3px;
                }
                QPushButton:hover {
                    background-color: #505050;
                }
                QLineEdit, QSpinBox, QComboBox {
                    background-color: #404040;
                    color: #ffffff;
                    border: 1px solid #555555;
                    padding: 3px;
                }
                QTextEdit {
                    background-color: #3c3c3c;
                    color: #ffffff;
                    border: 1px solid #555555;
                }
                QGroupBox {
                    color: #ffffff;
                    border: 1px solid #555555;
                    margin: 5px;
                    padding-top: 10px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                }
                QTabWidget::pane {
                    border: 1px solid #555555;
                    background-color: #3c3c3c;
                }
                QTabBar::tab {
                    background-color: #404040;
                    color: #ffffff;
                    padding: 5px;
                    margin: 1px;
                }
                QTabBar::tab:selected {
                    background-color: #505050;
                }
                QStatusBar {
                    background-color: #404040;
                    color: #ffffff;
                }
            """)
    
    def show_symbol_selector(self):
        """Show symbol selector dialog"""
        try:
            dialog = SymbolSelectorDialog(self.data_provider, self.symbols, self)
            if dialog.exec_() == QDialog.Accepted:
                self.symbols = dialog.selected_symbols
                self.symbol_input.setText(",".join(self.symbols))
                self.config_manager.save_symbols(self.symbols)

                # Restart data thread with new symbols
                if self.data_thread:
                    self.data_thread.stop()
                    self.data_thread.wait()

                self.setup_data_updates()
                self.status_bar.showMessage(f"Updated to {len(self.symbols)} symbols", 3000)

        except Exception as e:
            logging.error(f"Error in symbol selector: {e}")
            self.status_bar.showMessage("Error loading symbol selector", 3000)

    def toggle_auto_refresh(self, state):
        """Toggle auto-refresh on/off with proper alert timing"""
        try:
            self.auto_refresh_enabled = state == 2  # Qt.Checked = 2
            logging.info(f"Auto-refresh toggle: state={state}, enabled={self.auto_refresh_enabled}")

            # Update alert engine timing
            self.alert_engine.set_auto_refresh_enabled(self.auto_refresh_enabled)

            if self.auto_refresh_enabled:
                # Start auto-refresh
                logging.info(f"Starting auto-refresh for {len(self.symbols)} symbols")
                self.setup_data_updates()
                self.status_bar.showMessage("Auto-refresh enabled - alerts will be evaluated", 3000)
                logging.info("Auto-refresh enabled - alert evaluation activated")
            else:
                # Stop auto-refresh
                logging.info("Stopping auto-refresh")
                if hasattr(self, 'data_thread') and self.data_thread:
                    self.data_thread.stop()
                    # Don't wait too long to avoid UI freeze
                    if not self.data_thread.wait(1000):  # Wait max 1 second
                        logging.warning("Auto-refresh thread didn't stop gracefully, terminating...")
                        self.data_thread.terminate()
                        self.data_thread.wait(500)  # Wait another 0.5 seconds
                    self.data_thread = None
                    logging.info("Auto-refresh thread stopped")

                self.connection_status.setText("🟡 Manual Mode")
                self.status_bar.showMessage("Auto-refresh disabled - alerts paused", 3000)
                logging.info("Auto-refresh disabled - alert evaluation paused")

            # Update alert status display
            self.update_alert_status()

        except Exception as e:
            logging.error(f"Error toggling auto-refresh: {e}")

    def load_all_assets(self):
        """Load all available trading assets"""
        try:
            self.status_bar.showMessage("Loading all available assets...", 0)

            # Fetch all available symbols
            all_symbols = self.data_provider.fetch_asset_list()

            if all_symbols:
                # Remove duplicates while preserving order
                unique_symbols = []
                seen = set()
                for symbol in all_symbols:
                    if symbol not in seen:
                        unique_symbols.append(symbol)
                        seen.add(symbol)

                if len(all_symbols) != len(unique_symbols):
                    logging.warning(f"Removed {len(all_symbols) - len(unique_symbols)} duplicate symbols from asset list")

                self.symbols = unique_symbols
                self.symbol_input.setText(",".join(unique_symbols[:50]) + f"... (+{len(unique_symbols)-50} more)")
                self.config_manager.save_symbols(unique_symbols)

                # Restart data thread if auto-refresh is enabled
                if self.auto_refresh_enabled:
                    if hasattr(self, 'data_thread') and self.data_thread:
                        self.data_thread.stop()
                        self.data_thread.wait()
                    self.setup_data_updates()

                self.status_bar.showMessage(f"Loaded {len(unique_symbols)} assets successfully", 5000)
                logging.info(f"Loaded {len(unique_symbols)} assets")
            else:
                self.status_bar.showMessage("Failed to load assets", 3000)

        except Exception as e:
            logging.error(f"Error loading all assets: {e}")
            self.status_bar.showMessage("Error loading assets", 3000)

    def update_symbols(self):
        """Update the symbols list"""
        try:
            symbols_text = self.symbol_input.text().strip()
            if symbols_text:
                new_symbols = [s.strip().upper() for s in symbols_text.split(',') if s.strip()]
                self.symbols = new_symbols  # No limit on symbols
                self.config_manager.save_symbols(self.symbols)

                # Restart data thread with new symbols if auto-refresh is enabled
                if self.auto_refresh_enabled:
                    if hasattr(self, 'data_thread') and self.data_thread:
                        self.data_thread.stop()
                        self.data_thread.wait()
                    self.setup_data_updates()

                self.status_bar.showMessage(f"Updated to {len(self.symbols)} symbols", 3000)

        except Exception as e:
            logging.error(f"Error updating symbols: {e}")
    
    def update_refresh_interval(self):
        """Update refresh interval"""
        try:
            interval = self.refresh_spin.value()
            self.config_manager.set('ui.auto_refresh_interval', interval)
            
            # Restart data thread with new interval
            if self.data_thread:
                self.data_thread.stop()
                self.data_thread.wait()
            
            self.setup_data_updates()
            
        except Exception as e:
            logging.error(f"Error updating refresh interval: {e}")
    
    def manual_refresh(self):
        """Manually refresh data (non-blocking) - baseline collection only"""
        try:
            # Update UI for scanning state
            self.refresh_btn.setEnabled(False)
            self.refresh_btn.setText("🔄 Scanning...")
            self.cancel_btn.setVisible(True)

            # Start manual scan in alert engine (resets baseline)
            self.alert_engine.start_manual_scan()

            # Update status
            self.status_bar.showMessage("Manual scan in progress - collecting baseline data...", 0)

            # Create a single-use thread for manual refresh
            self.manual_refresh_thread = ManualRefreshThread(self.data_provider, self.symbols)
            self.manual_refresh_thread.data_ready.connect(self.on_manual_refresh_complete)
            self.manual_refresh_thread.error_occurred.connect(self.on_manual_refresh_error)
            self.manual_refresh_thread.progress_update.connect(self.on_manual_refresh_progress)
            self.manual_refresh_thread.start()

        except Exception as e:
            logging.error(f"Error starting manual refresh: {e}")
            self.status_bar.showMessage("Error starting refresh", 3000)
            self._reset_refresh_button()

    def cancel_operation(self):
        """Cancel current operation"""
        try:
            if hasattr(self, 'manual_refresh_thread') and self.manual_refresh_thread:
                self.manual_refresh_thread.terminate()
                self.manual_refresh_thread.wait(1000)  # Wait up to 1 second

            self.status_bar.showMessage("Operation cancelled", 3000)
            self._reset_refresh_button()

        except Exception as e:
            logging.error(f"Error cancelling operation: {e}")

    def on_manual_refresh_complete(self, data):
        """Handle manual refresh completion"""
        try:
            # Process data as manual scan (no alerts)
            self.update_table_data(data, is_manual_scan=True)

            # Complete initial scan
            self.alert_engine.complete_initial_scan()

            # Get scan status
            scan_status = self.alert_engine.get_scan_status()

            self.status_bar.showMessage(
                f"Baseline scan completed - {scan_status['baseline_symbols_count']} symbols processed",
                5000
            )

            logging.info(f"Manual scan completed: {scan_status}")

        except Exception as e:
            logging.error(f"Error processing manual refresh data: {e}")
            self.status_bar.showMessage("Error processing refresh data", 3000)
        finally:
            # Re-enable refresh button
            self._reset_refresh_button()

    def on_manual_refresh_error(self, error_msg):
        """Handle manual refresh error"""
        logging.error(f"Manual refresh error: {error_msg}")
        self.status_bar.showMessage(f"Refresh failed: {error_msg}", 5000)
        self._reset_refresh_button()

    def on_manual_refresh_progress(self, message, percentage):
        """Handle manual refresh progress updates"""
        self.status_bar.showMessage(message, 0)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(percentage)
        self.progress_bar.setFormat(f"{percentage}% - {message}")

        # Hide progress bar when complete
        if percentage >= 100:
            QTimer.singleShot(2000, lambda: self.progress_bar.setVisible(False))

    def on_auto_refresh_progress(self, message, percentage):
        """Handle auto-refresh progress updates"""
        # For auto-refresh, just update status bar (no progress bar)
        self.status_bar.showMessage(message, 2000)

    def _reset_refresh_button(self):
        """Reset refresh button state"""
        try:
            self.refresh_btn.setEnabled(True)
            self.refresh_btn.setText("🔄 Refresh")
            self.cancel_btn.setVisible(False)
            self.progress_bar.setVisible(False)
        except Exception as e:
            logging.error(f"Error resetting refresh button: {e}")
    
    def toggle_theme(self):
        """Toggle between dark and light theme"""
        current_theme = self.config_manager.get('ui.theme', 'dark')
        new_theme = 'light' if current_theme == 'dark' else 'dark'
        self.config_manager.set('ui.theme', new_theme)
        self.apply_theme()
    
    def save_settings(self):
        """Save current settings"""
        try:
            self.config_manager.set('alerts.enable_telegram', self.enable_alerts_cb.isChecked())
            self.config_manager.set('alerts.cooldown_minutes', self.cooldown_spin.value())
            self.config_manager.set('alerts.sentiment_threshold', self.sentiment_spin.value() / 100)
            self.config_manager.set('data.enable_cvd', self.enable_cvd_cb.isChecked())
            self.config_manager.set('data.enable_volume_analysis', self.enable_volume_cb.isChecked())
            
            self.status_bar.showMessage("Settings saved successfully", 3000)
            
        except Exception as e:
            logging.error(f"Error saving settings: {e}")
            self.status_bar.showMessage("Error saving settings", 3000)
    
    def clear_alerts(self):
        """Clear alert log"""
        self.alert_log.clear()
    
    def export_alerts(self):
        """Export alerts to file"""
        try:
            alerts = self.alert_engine.get_recent_alerts(24)  # Last 24 hours
            
            filename = f"alerts_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            export_data = []
            for alert in alerts:
                export_data.append({
                    'timestamp': alert.timestamp.isoformat(),
                    'symbol': alert.symbol,
                    'type': alert.alert_type.value,
                    'message': alert.message,
                    'confidence': alert.confidence,
                    'data': alert.data
                })
            
            with open(filename, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            self.status_bar.showMessage(f"Alerts exported to {filename}", 3000)
            
        except Exception as e:
            logging.error(f"Error exporting alerts: {e}")
            self.status_bar.showMessage("Error exporting alerts", 3000)
    
    def closeEvent(self, event):
        """Handle application close"""
        try:
            # Stop data thread
            if self.data_thread:
                self.data_thread.stop()
                self.data_thread.wait()
            
            # Stop Telegram bot
            if self.telegram_bot:
                self.telegram_bot.stop_listener()
            
            event.accept()
            
        except Exception as e:
            logging.error(f"Error during close: {e}")
            event.accept()

def main():
    """Main funding rate & OI checker entry point"""
    # Setup logging with UTF-8 encoding to handle Unicode characters
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('funding_oi_checker.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    # Create application
    app = QApplication(sys.argv)
    app.setApplicationName("Crypto Funding Rate & OI Checker")
    
    # Create and show main window
    checker = FundingOIChecker()
    checker.show()
    
    # Run application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
