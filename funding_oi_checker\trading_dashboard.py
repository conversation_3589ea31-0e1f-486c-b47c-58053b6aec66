import sys
import time
import logging
import threading
import j<PERSON>
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
    QHeaderView, QComboBox, QStatusBar, QTabWidget, QTextEdit,
    QCheckBox, QSpinBox, QGroupBox, QGridLayout, QProgressBar,
    QSplitter, QFrame
)
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QTimer
from PyQt5.QtGui import QFont, QColor, QPalette

# Import our modules
from config_manager import ConfigManager
from data_provider import DataProvider
from alert_engine import AlertEngine, MarketData, AlertType
from bot_listener import TelegramBotListener
from config import TELEGRAM_TOKEN, <PERSON>ELEGRAM_CHAT_ID

class DataUpdateThread(QThread):
    """Thread for updating market data"""
    data_updated = pyqtSignal(dict)
    
    def __init__(self, data_provider, symbols, update_interval=30):
        super().__init__()
        self.data_provider = data_provider
        self.symbols = symbols
        self.update_interval = update_interval
        self.running = True
    
    def run(self):
        while self.running:
            try:
                data = self.data_provider.get_multiple_symbols_data(self.symbols)
                self.data_updated.emit(data)
                time.sleep(self.update_interval)
            except Exception as e:
                logging.error(f"Error in data update thread: {e}")
                time.sleep(5)  # Wait before retrying
    
    def stop(self):
        self.running = False

class TradingDashboard(QMainWindow):
    """Main trading dashboard application"""
    
    def __init__(self):
        super().__init__()
        
        # Initialize components
        self.config_manager = ConfigManager()
        self.data_provider = DataProvider()
        self.alert_engine = AlertEngine(self.config_manager)
        
        # Initialize Telegram bot if enabled
        self.telegram_bot = None
        if (self.config_manager.get('alerts.enable_telegram', True) and 
            TELEGRAM_TOKEN and TELEGRAM_CHAT_ID):
            try:
                self.telegram_bot = TelegramBotListener(
                    TELEGRAM_TOKEN, TELEGRAM_CHAT_ID, 
                    self.alert_engine, self.data_provider
                )
                self.telegram_bot.start_listener()
            except Exception as e:
                logging.error(f"Failed to start Telegram bot: {e}")
        
        # Data storage
        self.market_data = {}
        self.symbols = self.config_manager.symbols[:20]  # Limit to 20 symbols for performance
        
        # UI components
        self.table = None
        self.status_bar = None
        self.alert_log = None
        self.data_thread = None
        
        # Initialize UI
        self.init_ui()
        self.setup_data_updates()
        
        # Apply theme
        self.apply_theme()
        
        logging.info("Trading Dashboard initialized successfully")
    
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("🚀 Advanced Crypto Trading Dashboard")
        self.setGeometry(100, 100, 1400, 800)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Create top controls
        controls_layout = self.create_controls()
        main_layout.addLayout(controls_layout)
        
        # Create splitter for main content
        splitter = QSplitter(Qt.Horizontal)
        
        # Create main table
        self.create_main_table()
        splitter.addWidget(self.table)
        
        # Create side panel
        side_panel = self.create_side_panel()
        splitter.addWidget(side_panel)
        
        # Set splitter proportions
        splitter.setSizes([1000, 400])
        main_layout.addWidget(splitter)
        
        # Create status bar
        self.create_status_bar()
    
    def create_controls(self):
        """Create top control panel"""
        layout = QHBoxLayout()
        
        # Symbol input
        layout.addWidget(QLabel("Symbols:"))
        self.symbol_input = QLineEdit()
        self.symbol_input.setText(",".join(self.symbols))
        self.symbol_input.setPlaceholderText("Enter symbols separated by commas")
        layout.addWidget(self.symbol_input)
        
        # Update button
        update_btn = QPushButton("Update Symbols")
        update_btn.clicked.connect(self.update_symbols)
        layout.addWidget(update_btn)
        
        # Refresh interval
        layout.addWidget(QLabel("Refresh (s):"))
        self.refresh_spin = QSpinBox()
        self.refresh_spin.setRange(10, 300)
        self.refresh_spin.setValue(self.config_manager.get('ui.auto_refresh_interval', 30))
        self.refresh_spin.valueChanged.connect(self.update_refresh_interval)
        layout.addWidget(self.refresh_spin)
        
        # Manual refresh button
        refresh_btn = QPushButton("🔄 Refresh")
        refresh_btn.clicked.connect(self.manual_refresh)
        layout.addWidget(refresh_btn)
        
        # Theme toggle
        theme_btn = QPushButton("🌙 Theme")
        theme_btn.clicked.connect(self.toggle_theme)
        layout.addWidget(theme_btn)
        
        layout.addStretch()
        
        return layout
    
    def create_main_table(self):
        """Create the main data table"""
        self.table = QTableWidget()
        
        # Define columns
        columns = [
            "Symbol", "Price", "Funding Rate", "Funding Time", 
            "OI Change", "CVD Trend", "Volume", "Vol Ratio", 
            "Sentiment", "Last Update"
        ]
        
        self.table.setColumnCount(len(columns))
        self.table.setHorizontalHeaderLabels(columns)
        
        # Configure table
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSortingEnabled(True)
        
        # Set column widths
        header = self.table.horizontalHeader()
        header.resizeSection(0, 100)  # Symbol
        header.resizeSection(1, 100)  # Price
        header.resizeSection(2, 120)  # Funding Rate
        header.resizeSection(3, 100)  # Funding Time
        header.resizeSection(4, 100)  # OI Change
        header.resizeSection(5, 120)  # CVD Trend
        header.resizeSection(6, 100)  # Volume
        header.resizeSection(7, 80)   # Vol Ratio
        header.resizeSection(8, 100)  # Sentiment
    
    def create_side_panel(self):
        """Create side panel with alerts and settings"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Create tabs
        tabs = QTabWidget()
        
        # Alerts tab
        alerts_tab = QWidget()
        alerts_layout = QVBoxLayout(alerts_tab)
        
        alerts_layout.addWidget(QLabel("🚨 Recent Alerts"))
        
        self.alert_log = QTextEdit()
        self.alert_log.setMaximumHeight(200)
        self.alert_log.setReadOnly(True)
        alerts_layout.addWidget(self.alert_log)
        
        # Alert controls
        alert_controls = QHBoxLayout()
        clear_alerts_btn = QPushButton("Clear")
        clear_alerts_btn.clicked.connect(self.clear_alerts)
        alert_controls.addWidget(clear_alerts_btn)
        
        export_alerts_btn = QPushButton("Export")
        export_alerts_btn.clicked.connect(self.export_alerts)
        alert_controls.addWidget(export_alerts_btn)
        
        alerts_layout.addLayout(alert_controls)
        tabs.addTab(alerts_tab, "Alerts")
        
        # Settings tab
        settings_tab = self.create_settings_tab()
        tabs.addTab(settings_tab, "Settings")
        
        # Statistics tab
        stats_tab = self.create_stats_tab()
        tabs.addTab(stats_tab, "Stats")
        
        layout.addWidget(tabs)
        
        return widget
    
    def create_settings_tab(self):
        """Create settings tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Alert settings group
        alert_group = QGroupBox("Alert Settings")
        alert_layout = QGridLayout(alert_group)
        
        # Enable alerts checkbox
        self.enable_alerts_cb = QCheckBox("Enable Alerts")
        self.enable_alerts_cb.setChecked(self.config_manager.get('alerts.enable_telegram', True))
        alert_layout.addWidget(self.enable_alerts_cb, 0, 0, 1, 2)
        
        # Cooldown setting
        alert_layout.addWidget(QLabel("Cooldown (min):"), 1, 0)
        self.cooldown_spin = QSpinBox()
        self.cooldown_spin.setRange(1, 60)
        self.cooldown_spin.setValue(self.config_manager.get('alerts.cooldown_minutes', 5))
        alert_layout.addWidget(self.cooldown_spin, 1, 1)
        
        # Sentiment threshold
        alert_layout.addWidget(QLabel("Sentiment Threshold:"), 2, 0)
        self.sentiment_spin = QSpinBox()
        self.sentiment_spin.setRange(10, 100)
        self.sentiment_spin.setValue(int(self.config_manager.get('alerts.sentiment_threshold', 0.7) * 100))
        self.sentiment_spin.setSuffix("%")
        alert_layout.addWidget(self.sentiment_spin, 2, 1)
        
        layout.addWidget(alert_group)
        
        # Data settings group
        data_group = QGroupBox("Data Settings")
        data_layout = QGridLayout(data_group)
        
        # Enable CVD
        self.enable_cvd_cb = QCheckBox("Enable CVD Analysis")
        self.enable_cvd_cb.setChecked(self.config_manager.get('data.enable_cvd', True))
        data_layout.addWidget(self.enable_cvd_cb, 0, 0, 1, 2)
        
        # Enable volume analysis
        self.enable_volume_cb = QCheckBox("Enable Volume Analysis")
        self.enable_volume_cb.setChecked(self.config_manager.get('data.enable_volume_analysis', True))
        data_layout.addWidget(self.enable_volume_cb, 1, 0, 1, 2)
        
        layout.addWidget(data_group)
        
        # Save settings button
        save_btn = QPushButton("Save Settings")
        save_btn.clicked.connect(self.save_settings)
        layout.addWidget(save_btn)
        
        layout.addStretch()
        
        return widget
    
    def create_stats_tab(self):
        """Create statistics tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Statistics labels
        self.stats_labels = {
            'total_symbols': QLabel("Total Symbols: 0"),
            'active_alerts': QLabel("Active Alerts: 0"),
            'bullish_signals': QLabel("Bullish Signals: 0"),
            'bearish_signals': QLabel("Bearish Signals: 0"),
            'volume_spikes': QLabel("Volume Spikes: 0"),
            'last_update': QLabel("Last Update: Never")
        }
        
        for label in self.stats_labels.values():
            layout.addWidget(label)
        
        layout.addStretch()
        
        return widget
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Add status indicators
        self.connection_status = QLabel("🔴 Disconnected")
        self.status_bar.addWidget(self.connection_status)
        
        self.alert_status = QLabel("🔕 Alerts: Off")
        self.status_bar.addPermanentWidget(self.alert_status)
        
        self.update_count = QLabel("Updates: 0")
        self.status_bar.addPermanentWidget(self.update_count)
    
    def setup_data_updates(self):
        """Setup automatic data updates"""
        self.data_thread = DataUpdateThread(
            self.data_provider, 
            self.symbols, 
            self.config_manager.get('ui.auto_refresh_interval', 30)
        )
        self.data_thread.data_updated.connect(self.update_table_data)
        self.data_thread.start()
        
        # Update connection status
        self.connection_status.setText("🟢 Connected")
    
    def update_table_data(self, data: Dict):
        """Update table with new market data"""
        try:
            self.market_data = data
            
            # Update table
            self.table.setRowCount(len(data))
            
            for row, (symbol, market_data) in enumerate(data.items()):
                self.populate_table_row(row, market_data)
                
                # Process alerts
                self.process_alerts(market_data)
            
            # Update statistics
            self.update_statistics()
            
            # Update status
            self.update_count.setText(f"Updates: {len(data)}")
            
        except Exception as e:
            logging.error(f"Error updating table data: {e}")
    
    def populate_table_row(self, row: int, data: Dict):
        """Populate a single table row with market data"""
        try:
            # Symbol
            self.table.setItem(row, 0, QTableWidgetItem(data['symbol']))
            
            # Price
            price_item = QTableWidgetItem(f"${data['price']:.4f}")
            self.table.setItem(row, 1, price_item)
            
            # Funding Rate
            fr = data['funding_rate']
            fr_text = f"{fr*100:.4f}%"
            fr_item = QTableWidgetItem(fr_text)
            
            # Color code funding rate
            if fr > 0.01:
                fr_item.setBackground(QColor(255, 200, 200))  # Light red
            elif fr < -0.01:
                fr_item.setBackground(QColor(200, 255, 200))  # Light green
            
            self.table.setItem(row, 2, fr_item)
            
            # Funding Time
            self.table.setItem(row, 3, QTableWidgetItem(data['funding_time']))
            
            # OI Change
            oi_change = data['oi_change']
            oi_text = f"{oi_change*100:.2f}%"
            oi_item = QTableWidgetItem(oi_text)
            
            # Color code OI change
            if oi_change > 0.02:
                oi_item.setBackground(QColor(255, 255, 200))  # Light yellow
            elif oi_change < -0.02:
                oi_item.setBackground(QColor(200, 200, 255))  # Light blue
            
            self.table.setItem(row, 4, oi_item)
            
            # CVD Trend
            self.table.setItem(row, 5, QTableWidgetItem(data['cvd_trend']))
            
            # Volume
            volume_text = f"{data['volume']:,.0f}"
            self.table.setItem(row, 6, QTableWidgetItem(volume_text))
            
            # Volume Ratio
            vol_ratio = data['volume_ratio']
            vol_ratio_text = f"{vol_ratio:.1f}x"
            vol_ratio_item = QTableWidgetItem(vol_ratio_text)
            
            # Highlight volume spikes
            if vol_ratio > 2.0:
                vol_ratio_item.setBackground(QColor(255, 200, 255))  # Light magenta
            
            self.table.setItem(row, 7, vol_ratio_item)
            
            # Sentiment
            sentiment = data['sentiment_score']
            sentiment_text = f"{sentiment:.2f}"
            sentiment_item = QTableWidgetItem(sentiment_text)
            
            # Color code sentiment
            if sentiment > 0.3:
                sentiment_item.setBackground(QColor(200, 255, 200))  # Light green
            elif sentiment < -0.3:
                sentiment_item.setBackground(QColor(255, 200, 200))  # Light red
            
            self.table.setItem(row, 8, sentiment_item)
            
            # Last Update
            update_time = data['timestamp'].strftime('%H:%M:%S')
            self.table.setItem(row, 9, QTableWidgetItem(update_time))
            
        except Exception as e:
            logging.error(f"Error populating table row: {e}")
    
    def process_alerts(self, market_data: Dict):
        """Process market data for alerts"""
        try:
            # Convert to MarketData object
            data = MarketData(
                symbol=market_data['symbol'],
                price=market_data['price'],
                funding_rate=market_data['funding_rate'],
                oi_change=market_data['oi_change'],
                cvd=market_data['cvd_value'],
                volume=market_data['volume'],
                volume_avg=market_data['volume_avg'],
                timestamp=market_data['timestamp']
            )
            
            # Process through alert engine
            alerts = self.alert_engine.process_market_data(data)
            
            # Handle alerts
            for alert in alerts:
                self.handle_alert(alert)
                
        except Exception as e:
            logging.error(f"Error processing alerts: {e}")
    
    def handle_alert(self, alert):
        """Handle a new alert"""
        try:
            # Add to alert log
            alert_text = f"[{alert.timestamp.strftime('%H:%M:%S')}] {alert.message}\n"
            self.alert_log.append(alert_text)
            
            # Send to Telegram if enabled
            if self.telegram_bot and self.enable_alerts_cb.isChecked():
                self.telegram_bot.send_alert_notification(alert)
            
            # Update alert status
            self.alert_status.setText("🔔 Alerts: On")
            
        except Exception as e:
            logging.error(f"Error handling alert: {e}")
    
    def update_statistics(self):
        """Update statistics display"""
        try:
            total_symbols = len(self.market_data)
            bullish_count = sum(1 for data in self.market_data.values() if data['sentiment_score'] > 0.3)
            bearish_count = sum(1 for data in self.market_data.values() if data['sentiment_score'] < -0.3)
            volume_spikes = sum(1 for data in self.market_data.values() if data['volume_spike'])
            recent_alerts = len(self.alert_engine.get_recent_alerts(1))
            
            self.stats_labels['total_symbols'].setText(f"Total Symbols: {total_symbols}")
            self.stats_labels['active_alerts'].setText(f"Active Alerts: {recent_alerts}")
            self.stats_labels['bullish_signals'].setText(f"Bullish Signals: {bullish_count}")
            self.stats_labels['bearish_signals'].setText(f"Bearish Signals: {bearish_count}")
            self.stats_labels['volume_spikes'].setText(f"Volume Spikes: {volume_spikes}")
            self.stats_labels['last_update'].setText(f"Last Update: {datetime.now().strftime('%H:%M:%S')}")
            
        except Exception as e:
            logging.error(f"Error updating statistics: {e}")
    
    def apply_theme(self):
        """Apply dark theme to the application"""
        if self.config_manager.get('ui.theme') == 'dark':
            self.setStyleSheet("""
                QMainWindow {
                    background-color: #2b2b2b;
                    color: #ffffff;
                }
                QTableWidget {
                    background-color: #3c3c3c;
                    alternate-background-color: #4a4a4a;
                    color: #ffffff;
                    gridline-color: #555555;
                }
                QHeaderView::section {
                    background-color: #404040;
                    color: #ffffff;
                    border: 1px solid #555555;
                    padding: 5px;
                }
                QPushButton {
                    background-color: #404040;
                    color: #ffffff;
                    border: 1px solid #555555;
                    padding: 5px;
                    border-radius: 3px;
                }
                QPushButton:hover {
                    background-color: #505050;
                }
                QLineEdit, QSpinBox, QComboBox {
                    background-color: #404040;
                    color: #ffffff;
                    border: 1px solid #555555;
                    padding: 3px;
                }
                QTextEdit {
                    background-color: #3c3c3c;
                    color: #ffffff;
                    border: 1px solid #555555;
                }
                QGroupBox {
                    color: #ffffff;
                    border: 1px solid #555555;
                    margin: 5px;
                    padding-top: 10px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                }
                QTabWidget::pane {
                    border: 1px solid #555555;
                    background-color: #3c3c3c;
                }
                QTabBar::tab {
                    background-color: #404040;
                    color: #ffffff;
                    padding: 5px;
                    margin: 1px;
                }
                QTabBar::tab:selected {
                    background-color: #505050;
                }
                QStatusBar {
                    background-color: #404040;
                    color: #ffffff;
                }
            """)
    
    def update_symbols(self):
        """Update the symbols list"""
        try:
            symbols_text = self.symbol_input.text().strip()
            if symbols_text:
                new_symbols = [s.strip().upper() for s in symbols_text.split(',') if s.strip()]
                self.symbols = new_symbols[:20]  # Limit to 20 symbols
                self.config_manager.save_symbols(self.symbols)
                
                # Restart data thread with new symbols
                if self.data_thread:
                    self.data_thread.stop()
                    self.data_thread.wait()
                
                self.setup_data_updates()
                
        except Exception as e:
            logging.error(f"Error updating symbols: {e}")
    
    def update_refresh_interval(self):
        """Update refresh interval"""
        try:
            interval = self.refresh_spin.value()
            self.config_manager.set('ui.auto_refresh_interval', interval)
            
            # Restart data thread with new interval
            if self.data_thread:
                self.data_thread.stop()
                self.data_thread.wait()
            
            self.setup_data_updates()
            
        except Exception as e:
            logging.error(f"Error updating refresh interval: {e}")
    
    def manual_refresh(self):
        """Manually refresh data"""
        try:
            if self.data_thread:
                data = self.data_provider.get_multiple_symbols_data(self.symbols)
                self.update_table_data(data)
        except Exception as e:
            logging.error(f"Error in manual refresh: {e}")
    
    def toggle_theme(self):
        """Toggle between dark and light theme"""
        current_theme = self.config_manager.get('ui.theme', 'dark')
        new_theme = 'light' if current_theme == 'dark' else 'dark'
        self.config_manager.set('ui.theme', new_theme)
        self.apply_theme()
    
    def save_settings(self):
        """Save current settings"""
        try:
            self.config_manager.set('alerts.enable_telegram', self.enable_alerts_cb.isChecked())
            self.config_manager.set('alerts.cooldown_minutes', self.cooldown_spin.value())
            self.config_manager.set('alerts.sentiment_threshold', self.sentiment_spin.value() / 100)
            self.config_manager.set('data.enable_cvd', self.enable_cvd_cb.isChecked())
            self.config_manager.set('data.enable_volume_analysis', self.enable_volume_cb.isChecked())
            
            self.status_bar.showMessage("Settings saved successfully", 3000)
            
        except Exception as e:
            logging.error(f"Error saving settings: {e}")
            self.status_bar.showMessage("Error saving settings", 3000)
    
    def clear_alerts(self):
        """Clear alert log"""
        self.alert_log.clear()
    
    def export_alerts(self):
        """Export alerts to file"""
        try:
            alerts = self.alert_engine.get_recent_alerts(24)  # Last 24 hours
            
            filename = f"alerts_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            export_data = []
            for alert in alerts:
                export_data.append({
                    'timestamp': alert.timestamp.isoformat(),
                    'symbol': alert.symbol,
                    'type': alert.alert_type.value,
                    'message': alert.message,
                    'confidence': alert.confidence,
                    'data': alert.data
                })
            
            with open(filename, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            self.status_bar.showMessage(f"Alerts exported to {filename}", 3000)
            
        except Exception as e:
            logging.error(f"Error exporting alerts: {e}")
            self.status_bar.showMessage("Error exporting alerts", 3000)
    
    def closeEvent(self, event):
        """Handle application close"""
        try:
            # Stop data thread
            if self.data_thread:
                self.data_thread.stop()
                self.data_thread.wait()
            
            # Stop Telegram bot
            if self.telegram_bot:
                self.telegram_bot.stop_listener()
            
            event.accept()
            
        except Exception as e:
            logging.error(f"Error during close: {e}")
            event.accept()

def main():
    """Main application entry point"""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('trading_dashboard.log'),
            logging.StreamHandler()
        ]
    )
    
    # Create application
    app = QApplication(sys.argv)
    app.setApplicationName("Advanced Crypto Trading Dashboard")
    
    # Create and show main window
    dashboard = TradingDashboard()
    dashboard.show()
    
    # Run application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
