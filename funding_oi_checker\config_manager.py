import json
import os
from typing import Dict, Any, Optional
import logging

class ConfigManager:
    """Manages configuration settings for the trading dashboard"""

    def __init__(self, config_dir: str = "funding_oi_checker"):
        self.config_dir = config_dir
        self.config_file = os.path.join(config_dir, "dashboard_config.json")
        self.alert_config_file = os.path.join(config_dir, "alert_config.json")
        self.symbols_file = os.path.join(config_dir, "symbols.json")

        # Default configurations
        self.default_config = {
            "ui": {
                "theme": "dark",
                "font_family": "Inter",
                "font_size": 12,
                "auto_refresh_interval": 30,
                "table_row_height": 35,
                "enable_animations": True,
                "compact_mode": False
            },
            "data": {
                "cache_duration": 30,
                "max_history_points": 100,
                "timeframe": "1h",
                "enable_cvd": True,
                "enable_volume_analysis": True
            },
            "alerts": {
                "enable_telegram": True,
                "enable_sound": True,
                "cooldown_minutes": 5,
                "max_alerts_per_hour": 20,
                "sentiment_threshold": 0.7
            },
            "api": {
                "rate_limit_per_minute": 120,
                "timeout_seconds": 10,
                "retry_attempts": 3
            }
        }

        self.default_alert_config = {
            "global_defaults": {
                "oi_threshold": 0.02,
                "fr_threshold": 0.01,
                "volume_threshold_multiplier": 2.0,
                "cvd_threshold": 0.05,
                "enable_alerts": True
            },
            "symbol_specific": {}
        }

        self.config = self.load_config()
        self.alert_config = self.load_alert_config()
        self.symbols = self.load_symbols()

    def load_config(self) -> Dict[str, Any]:
        """Load main configuration"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    loaded_config = json.load(f)
                return self._merge_configs(self.default_config, loaded_config)
            else:
                self.save_config(self.default_config)
                return self.default_config.copy()
        except Exception as e:
            logging.error(f"Error loading config: {e}")
            return self.default_config.copy()

    def load_alert_config(self) -> Dict[str, Any]:
        """Load alert configuration"""
        try:
            if os.path.exists(self.alert_config_file):
                with open(self.alert_config_file, 'r') as f:
                    loaded_config = json.load(f)
                return self._merge_configs(self.default_alert_config, loaded_config)
            else:
                self.save_alert_config(self.default_alert_config)
                return self.default_alert_config.copy()
        except Exception as e:
            logging.error(f"Error loading alert config: {e}")
            return self.default_alert_config.copy()

    def load_symbols(self) -> list:
        """Load symbols list"""
        try:
            if os.path.exists(self.symbols_file):
                with open(self.symbols_file, 'r') as f:
                    return json.load(f)
            else:
                default_symbols = ["BTCUSDT", "ETHUSDT", "SOLUSDT", "ADAUSDT", "DOTUSDT"]
                self.save_symbols(default_symbols)
                return default_symbols
        except Exception as e:
            logging.error(f"Error loading symbols: {e}")
            return ["BTCUSDT", "ETHUSDT", "SOLUSDT"]

    def save_config(self, config: Dict[str, Any] = None) -> bool:
        """Save main configuration"""
        try:
            config_to_save = config or self.config
            os.makedirs(self.config_dir, exist_ok=True)
            with open(self.config_file, 'w') as f:
                json.dump(config_to_save, f, indent=2)
            if config:
                self.config = config
            return True
        except Exception as e:
            logging.error(f"Error saving config: {e}")
            return False

    def save_alert_config(self, alert_config: Dict[str, Any] = None) -> bool:
        """Save alert configuration"""
        try:
            config_to_save = alert_config or self.alert_config
            os.makedirs(self.config_dir, exist_ok=True)
            with open(self.alert_config_file, 'w') as f:
                json.dump(config_to_save, f, indent=2)
            if alert_config:
                self.alert_config = alert_config
            return True
        except Exception as e:
            logging.error(f"Error saving alert config: {e}")
            return False

    def save_symbols(self, symbols: list) -> bool:
        """Save symbols list"""
        try:
            os.makedirs(self.config_dir, exist_ok=True)
            with open(self.symbols_file, 'w') as f:
                json.dump(symbols, f, indent=2)
            self.symbols = symbols
            return True
        except Exception as e:
            logging.error(f"Error saving symbols: {e}")
            return False

    def get_symbol_alert_config(self, symbol: str) -> Dict[str, Any]:
        """Get alert configuration for a specific symbol"""
        symbol_config = self.alert_config.get("symbol_specific", {}).get(symbol, {})
        global_defaults = self.alert_config.get("global_defaults", {})
        return {**global_defaults, **symbol_config}

    def set_symbol_alert_config(self, symbol: str, config: Dict[str, Any]) -> bool:
        """Set alert configuration for a specific symbol"""
        try:
            if "symbol_specific" not in self.alert_config:
                self.alert_config["symbol_specific"] = {}
            self.alert_config["symbol_specific"][symbol] = config
            return self.save_alert_config()
        except Exception as e:
            logging.error(f"Error setting symbol alert config: {e}")
            return False

    def _merge_configs(self, default: Dict[str, Any], loaded: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively merge configurations"""
        result = default.copy()
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        return result

    def get(self, key_path: str, default: Any = None) -> Any:
        """Get configuration value using dot notation"""
        keys = key_path.split('.')
        value = self.config
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default

    def set(self, key_path: str, value: Any) -> bool:
        """Set configuration value using dot notation"""
        keys = key_path.split('.')
        config = self.config
        try:
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            config[keys[-1]] = value
            return self.save_config()
        except Exception as e:
            logging.error(f"Error setting config value: {e}")
            return False
