import dearpygui.dearpygui as dpg
import logging
import sys
import os
import datetime
import threading
import queue
import time
import json
from huinav2 import AdvancedTradingBot, configure_logging

# Import the fixed Bybit API
import sys
sys.path.append("C:\\Users\\<USER>\\Desktop\\SCri")
import bybit_fixed

# Configure logging
configure_logging()
logging.info("Starting Fixed SignalCheck Pro...")

class FixedTradingBot(AdvancedTradingBot):
    """Fixed version of AdvancedTradingBot that uses the fixed Bybit API"""

    def __init__(self):
        super().__init__()

        # Replace the direct client with our fixed version
        try:
            logging.info("Initializing fixed Bybit client...")
            self.direct_client = bybit_fixed.BybitDirect(self.api_key, self.api_secret)
            self.time_offset = self.direct_client.sync_time()
            logging.info(f"Fixed Bybit client initialized successfully with time offset: {self.time_offset} ms")
        except Exception as e:
            logging.error(f"Could not initialize fixed Bybit client: {e}")
            self.direct_client = None

def main():
    try:
        # Initialize the bot
        logging.info("Initializing FixedTradingBot...")
        bot = FixedTradingBot()
        logging.info("FixedTradingBot initialized.")

        # Check if bot was initialized successfully
        if not bot.session:
            logging.critical("Failed Bybit connection. Check API keys/network. Exiting.")
            print("CRITICAL: Failed Bybit connection. Check API keys/network. Exiting.")
            return

        # Create a new GUI instance
        from huinav2 import AdvancedTradingGUI

        # Create GUI instance but don't initialize it yet
        gui = AdvancedTradingGUI(bot)

        # Manually run the GUI with proper initialization
        run_gui(gui)

    except Exception as e:
        logging.error(f"Error in main: {e}", exc_info=True)
    finally:
        logging.info("Shutting down application...")

def run_gui(gui):
    """Run the GUI application with proper initialization"""
    logging.info("Running GUI...")

    try:
        # Create context
        dpg.create_context()
        logging.info("DPG context created")

        # Create viewport
        dpg.create_viewport(title="SignalCheck Pro", width=1600, height=900)
        dpg.set_viewport_clear_color(gui.theme['background'])
        logging.info("DPG viewport created")

        # Set up the process_gui_queue callback to run on the first frame
        dpg.set_frame_callback(1, callback=gui.process_gui_queue)

        # Create the main window and UI elements
        gui.create_main_window()
        logging.info("Main window created")

        # Set primary window
        dpg.set_primary_window("main_window", True)

        # Setup and show
        dpg.setup_dearpygui()
        dpg.show_viewport()
        logging.info("Viewport shown")

        # Initial refresh of positions
        try:
            gui.refresh_positions()
        except Exception as e:
            logging.error(f"Error during initial position refresh: {e}")
            # Continue even if position refresh fails

        # Start DPG
        logging.info("Starting DPG main loop")
        dpg.start_dearpygui()

        # This point is reached when the GUI is closed
        logging.info("DPG main loop ended")
    except Exception as e:
        logging.error(f"Error running GUI: {e}", exc_info=True)
    finally:
        # Cleanup
        logging.info("Shutting down GUI...")
        gui.stop_event.set()
        try:
            if dpg.is_dearpygui_running():
                dpg.stop_dearpygui()
            dpg.destroy_context()
        except Exception as e:
            logging.error(f"Error during cleanup: {e}", exc_info=True)

if __name__ == "__main__":
    main()
