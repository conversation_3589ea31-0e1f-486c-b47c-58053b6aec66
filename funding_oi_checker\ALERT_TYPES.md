# 🚨 Complete Alert Types Documentation

## Overview
The Funding Rate & OI Checker implements **20 distinct alert types** across 6 categories, designed to detect specific market conditions in crypto perpetual futures.

## 📊 Alert Categories & Types

### 1. **Funding Rate Alerts (5 Types)**

#### `FUNDING_EXTREME_POSITIVE`
- **Condition**: Funding rate > 1.5%
- **Meaning**: Longs paying heavily, potential long squeeze risk
- **Example**: "🔴 EXTREME POSITIVE FUNDING: BTCUSDT - Funding: 1.234% (longs paying)"

#### `FUNDING_EXTREME_NEGATIVE`
- **Condition**: Funding rate < -1.5%
- **Meaning**: Shorts paying heavily, potential short squeeze setup
- **Example**: "🟢 EXTREME NEGATIVE FUNDING: ETHUSDT - Funding: -1.567% (shorts paying)"

#### `FUNDING_REVERSAL_BULLISH`
- **Condition**: Funding shifts from positive to negative (>0.8% change)
- **Meaning**: Market sentiment shifting bullish
- **Example**: "🟢 FUNDING REVERSAL (BULLISH): SOLUSDT - Shifted to negative funding"

#### `FUNDING_REVERSAL_BEARISH`
- **Condition**: Funding shifts from negative to positive (>0.8% change)
- **Meaning**: Market sentiment shifting bearish
- **Example**: "🔴 FUNDING REVERSAL (BEARISH): ADAUSDT - Shifted to positive funding"

#### `FUNDING_PRICE_DIVERGENCE`
- **Condition**: Funding rate and price moving in opposite directions
- **Meaning**: Potential trend reversal or market inefficiency
- **Example**: "⚠️ FUNDING-PRICE DIVERGENCE: DOTUSDT - Price rising, funding falling"

---

### 2. **Open Interest Alerts (5 Types)**

#### `OI_SURGE_BULLISH`
- **Condition**: OI increase >15% + positive funding
- **Meaning**: New longs entering, bullish trend
- **Example**: "🟢 BULLISH OI SURGE: BTCUSDT - OI: +18.5% (new longs entering)"

#### `OI_SURGE_BEARISH`
- **Condition**: OI increase >15% + negative funding
- **Meaning**: New shorts entering, potential squeeze setup
- **Example**: "🔴 BEARISH OI SURGE: ETHUSDT - OI: +22.1% (new shorts entering)"

#### `OI_DUMP_LIQUIDATION`
- **Condition**: OI decrease >20%
- **Meaning**: Major liquidation event
- **Example**: "💥 LIQUIDATION EVENT: SOLUSDT - OI: -25.3% (major closures)"

#### `OI_TREND_REVERSAL`
- **Condition**: Significant change in OI trend direction
- **Meaning**: Shift in market positioning
- **Example**: "🔄 OI TREND REVERSAL: ADAUSDT - OI trend changed to falling"

#### `OI_PRICE_DIVERGENCE`
- **Condition**: OI and price moving in opposite directions
- **Meaning**: Potential trend weakness or reversal
- **Example**: "⚠️ OI-PRICE DIVERGENCE: DOTUSDT - Price rising, OI falling"

---

### 3. **Combined Squeeze Alerts (2 Types)**

#### `BULLISH_SQUEEZE`
- **Condition**: Negative funding + Rising OI + Rising CVD
- **Meaning**: Perfect short squeeze setup
- **Example**: "🟢 BULLISH SQUEEZE SETUP: BTCUSDT - Shorts paying + New positions + Buying pressure"

#### `BEARISH_SQUEEZE`
- **Condition**: Positive funding + Rising OI + Falling CVD
- **Meaning**: Perfect long squeeze setup
- **Example**: "🔴 BEARISH SQUEEZE SETUP: ETHUSDT - Longs paying + New positions + Selling pressure"

---

### 4. **Reversal Pattern Alerts (2 Types)**

#### `BULLISH_REVERSAL`
- **Condition**: Falling OI + Extreme negative funding + CVD momentum shift
- **Meaning**: Shorts covering, potential bottom
- **Example**: "🟢 BULLISH REVERSAL PATTERN: SOLUSDT - OI falling + Extreme funding + CVD shift"

#### `BEARISH_REVERSAL`
- **Condition**: Falling OI + Extreme positive funding + CVD momentum shift
- **Meaning**: Longs covering, potential top
- **Example**: "🔴 BEARISH REVERSAL PATTERN: ADAUSDT - OI falling + Extreme funding + CVD shift"

---

### 5. **CVD Alerts (3 Types)**

#### `CVD_BULLISH_DIVERGENCE`
- **Condition**: Price falling while CVD rising
- **Meaning**: Hidden buying pressure, potential reversal
- **Example**: "🟢 BULLISH CVD DIVERGENCE: DOTUSDT - Price down, CVD up (hidden buying)"

#### `CVD_BEARISH_DIVERGENCE`
- **Condition**: Price rising while CVD falling
- **Meaning**: Hidden selling pressure, potential reversal
- **Example**: "🔴 BEARISH CVD DIVERGENCE: BTCUSDT - Price up, CVD down (hidden selling)"

#### `CVD_MOMENTUM_SHIFT`
- **Condition**: Significant change in CVD trend direction
- **Meaning**: Order flow momentum changing
- **Example**: "🔄 CVD MOMENTUM SHIFT: ETHUSDT - CVD trend reversed to bullish"

---

### 6. **Multi-Factor Alerts (2 Types)**

#### `MULTI_BULLISH_ALIGNMENT`
- **Condition**: 3+ bullish factors aligned simultaneously
- **Meaning**: High-confidence bullish signal
- **Example**: "🟢 MULTI-FACTOR BULLISH: SOLUSDT - 4/5 factors aligned (funding, OI, CVD, volume)"

#### `MULTI_BEARISH_ALIGNMENT`
- **Condition**: 3+ bearish factors aligned simultaneously
- **Meaning**: High-confidence bearish signal
- **Example**: "🔴 MULTI-FACTOR BEARISH: ADAUSDT - 4/5 factors aligned (funding, OI, CVD, sentiment)"

---

### 7. **Volume Alerts (1 Type)**

#### `VOLUME_SPIKE`
- **Condition**: Volume exceeds threshold multiplier (default 3x average)
- **Meaning**: Unusual trading activity
- **Example**: "📊 VOLUME SPIKE: DOTUSDT - Volume: 4.2x average"

---

## 🎯 Alert Timing & Behavior

### **Initial Scan (Baseline Collection)**
- **Purpose**: Establish baseline values for all metrics
- **Behavior**: NO alerts generated during this phase
- **Status**: "📊 Scan: Collecting Baseline"

### **Auto-Refresh Mode (Alert Active)**
- **Purpose**: Monitor for changes vs baseline data
- **Behavior**: Alerts generated when conditions met AND data changed
- **Status**: "📊 Scan: Alert Mode Active"

### **Change Detection Thresholds**
- **Funding Rate**: 0.1% change required
- **OI Change**: 2% change required
- **CVD**: 5% relative change required
- **Volume Ratio**: 30% change required
- **Price**: 0.5% change required

### **Cooldown System**
- **Default**: 5 minutes between same alert type for same symbol
- **Purpose**: Prevent alert spam
- **Configurable**: Per symbol and per alert type

---

## 📈 Confidence Scoring

Each alert includes a confidence score (0-100%) based on:
- **Signal Strength**: How extreme the condition is
- **Multiple Factor Alignment**: More factors = higher confidence
- **Historical Context**: Comparison to recent patterns
- **Volume Confirmation**: Higher volume = higher confidence

**Example Confidence Levels:**
- **90%+**: Extreme conditions with multiple confirmations
- **70-89%**: Strong signals with good confirmation
- **50-69%**: Moderate signals, watch for confirmation
- **<50%**: Weak signals, use caution

---

## 🔧 Configuration

### **Global Thresholds** (in `alert_config.json`)
```json
{
  "global_defaults": {
    "oi_threshold": 0.02,
    "fr_threshold": 0.01,
    "volume_threshold_multiplier": 2.0,
    "cvd_threshold": 0.05,
    "enable_alerts": true
  }
}
```

### **Per-Symbol Overrides**
```json
{
  "symbol_specific": {
    "BTCUSDT": {
      "oi_threshold": 0.03,
      "fr_threshold": 0.015,
      "cooldown_minutes": 10
    }
  }
}
```

This comprehensive alert system provides detailed monitoring of crypto perpetual futures markets with intelligent timing and change detection to minimize false signals while capturing significant market movements.
