import logging
import requests
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from threading import Thread, Event

class TelegramBotListener:
    """Simple Telegram notification system (send-only for now)"""

    def __init__(self, token: str, chat_id: str, alert_engine=None, data_provider=None):
        self.token = token
        self.chat_id = chat_id
        self.alert_engine = alert_engine
        self.data_provider = data_provider
        self.is_running = False
        self.muted_symbols = set()
        self.muted_until = None

        # Test connection
        self._test_connection()
    
    def _test_connection(self):
        """Test Telegram bot connection"""
        try:
            url = f"https://api.telegram.org/bot{self.token}/getMe"
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                bot_info = response.json()
                if bot_info.get('ok'):
                    logging.info(f"Telegram bot connected: {bot_info['result']['username']}")
                    return True
            logging.warning("Telegram bot connection test failed")
            return False
        except Exception as e:
            logging.error(f"Error testing Telegram connection: {e}")
            return False

    def start_listener(self):
        """Start the Telegram notification system"""
        self.is_running = True
        logging.info("Telegram notification system started (send-only mode)")

    def stop_listener(self):
        """Stop the Telegram notification system"""
        self.is_running = False
        logging.info("Telegram notification system stopped")
    
    def send_status_update(self):
        """Send status update to Telegram"""
        try:
            status_msg = "📊 **Trading Dashboard Status**\n\n"
            status_msg += f"🟢 System: Online\n"
            status_msg += f"⏰ Time: {datetime.now().strftime('%H:%M:%S UTC')}\n"

            if self.alert_engine:
                recent_alerts = self.alert_engine.get_recent_alerts(1)
                status_msg += f"🚨 Alerts (1h): {len(recent_alerts)}\n"

            return self.send_message(status_msg)

        except Exception as e:
            logging.error(f"Error sending status update: {e}")
            return False
    
    def send_summary(self):
        """Send market summary to Telegram"""
        try:
            summary_msg = "📈 **Market Summary**\n\n"

            if self.alert_engine:
                recent_alerts = self.alert_engine.get_recent_alerts(6)
                if recent_alerts:
                    summary_msg += f"🚨 **Recent Alerts ({len(recent_alerts)}):**\n"
                    for alert in recent_alerts[-3:]:
                        time_str = alert.timestamp.strftime('%H:%M')
                        summary_msg += f"• {time_str} {alert.symbol}: {alert.alert_type.value}\n"
                else:
                    summary_msg += "🚨 **No recent alerts**\n"

            return self.send_message(summary_msg)

        except Exception as e:
            logging.error(f"Error sending summary: {e}")
            return False
    
    def mute_symbol(self, symbol: str):
        """Mute alerts for a specific symbol"""
        self.muted_symbols.add(symbol.upper())
        logging.info(f"Muted alerts for {symbol}")

    def unmute_symbol(self, symbol: str):
        """Unmute alerts for a specific symbol"""
        self.muted_symbols.discard(symbol.upper())
        logging.info(f"Unmuted alerts for {symbol}")

    def mute_all(self, minutes: int = 30):
        """Mute all alerts for specified minutes"""
        self.muted_until = datetime.now() + timedelta(minutes=minutes)
        logging.info(f"Muted all alerts for {minutes} minutes")

    def unmute_all(self):
        """Unmute all alerts"""
        self.muted_symbols.clear()
        self.muted_until = None
        logging.info("Unmuted all alerts")
    
    def send_alert_notification(self, alert) -> bool:
        """Send alert notification to Telegram"""
        try:
            # Check if alerts are muted
            if self.is_muted(alert.symbol):
                return False
            
            # Format alert message
            message = self._format_alert_message(alert)
            
            # Send message
            return self.send_message(message)
            
        except Exception as e:
            logging.error(f"Error sending alert notification: {e}")
            return False
    
    def send_message(self, message: str, parse_mode: str = 'HTML') -> bool:
        """Send a message to Telegram"""
        try:
            url = f"https://api.telegram.org/bot{self.token}/sendMessage"
            payload = {
                'chat_id': self.chat_id,
                'text': message,
                'parse_mode': parse_mode
            }
            
            response = requests.post(url, json=payload, timeout=10)
            
            if response.status_code == 200:
                logging.info("Telegram message sent successfully")
                return True
            else:
                logging.error(f"Failed to send Telegram message: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logging.error(f"Error sending Telegram message: {e}")
            return False
    
    def is_muted(self, symbol: str) -> bool:
        """Check if alerts are muted for a symbol"""
        # Check global mute
        if self.muted_until and datetime.now() < self.muted_until:
            return True
        
        # Check symbol-specific mute
        return symbol in self.muted_symbols
    
    def _format_alert_message(self, alert) -> str:
        """Format alert for Telegram"""
        emoji_map = {
            'bullish_squeeze': '🟢',
            'bearish_squeeze': '🔴',
            'bullish_reversal': '🟢',
            'bearish_reversal': '🔴',
            'volume_spike': '📊',
            'cvd_divergence': '📈',
            'funding_extreme': '⚡',
            'oi_surge': '🔥'
        }
        
        emoji = emoji_map.get(alert.alert_type.value, '🚨')
        confidence_bar = '█' * int(alert.confidence * 10) if alert.confidence else ''
        
        message = f"{emoji} <b>ALERT</b>\n\n"
        message += f"<b>{alert.symbol}</b>\n"
        message += f"Type: {alert.alert_type.value.replace('_', ' ').title()}\n"
        
        if alert.confidence:
            message += f"Confidence: {alert.confidence:.1%} {confidence_bar}\n"
        
        message += f"Time: {alert.timestamp.strftime('%H:%M:%S')}\n\n"
        message += f"{alert.message}"
        
        return message
