import logging
import requests
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from threading import Thread, Event
import telegram
from telegram.ext import Application, CommandHandler, MessageHandler, filters

class TelegramBotListener:
    """Handles Telegram bot commands and notifications"""
    
    def __init__(self, token: str, chat_id: str, alert_engine=None, data_provider=None):
        self.token = token
        self.chat_id = chat_id
        self.alert_engine = alert_engine
        self.data_provider = data_provider
        self.bot = telegram.Bot(token=token)
        self.application = None
        self.is_running = False
        self.stop_event = Event()
        self.muted_symbols = set()
        self.muted_until = None
        
        # Command handlers
        self.commands = {
            'status': self._handle_status,
            'summary': self._handle_summary,
            'mute': self._handle_mute,
            'unmute': self._handle_unmute,
            'alerts': self._handle_alerts,
            'symbols': self._handle_symbols,
            'help': self._handle_help,
            'config': self._handle_config
        }
    
    def start_listener(self):
        """Start the Telegram bot listener"""
        if self.is_running:
            return
        
        try:
            self.application = Application.builder().token(self.token).build()
            
            # Add command handlers
            for command, handler in self.commands.items():
                self.application.add_handler(CommandHandler(command, handler))
            
            # Add message handler for unknown commands
            self.application.add_handler(
                MessageHandler(filters.TEXT & ~filters.COMMAND, self._handle_unknown)
            )
            
            # Start the bot in a separate thread
            self.bot_thread = Thread(target=self._run_bot, daemon=True)
            self.bot_thread.start()
            
            self.is_running = True
            logging.info("Telegram bot listener started")
            
        except Exception as e:
            logging.error(f"Error starting Telegram bot listener: {e}")
    
    def stop_listener(self):
        """Stop the Telegram bot listener"""
        if not self.is_running:
            return
        
        self.stop_event.set()
        self.is_running = False
        
        if self.application:
            self.application.stop()
        
        logging.info("Telegram bot listener stopped")
    
    def _run_bot(self):
        """Run the bot polling loop"""
        try:
            self.application.run_polling(stop_signals=None)
        except Exception as e:
            logging.error(f"Error in bot polling: {e}")
    
    async def _handle_status(self, update, context):
        """Handle /status command"""
        try:
            if not self.data_provider:
                await update.message.reply_text("❌ Data provider not available")
                return
            
            # Get current status
            status_msg = "📊 **Trading Dashboard Status**\n\n"
            
            # System status
            status_msg += f"🟢 System: Online\n"
            status_msg += f"⏰ Time: {datetime.now().strftime('%H:%M:%S UTC')}\n"
            
            # Alert status
            if self.alert_engine:
                recent_alerts = self.alert_engine.get_recent_alerts(1)  # Last hour
                status_msg += f"🚨 Alerts (1h): {len(recent_alerts)}\n"
            
            # Mute status
            if self.muted_symbols:
                status_msg += f"🔇 Muted symbols: {', '.join(list(self.muted_symbols)[:5])}\n"
            
            if self.muted_until and datetime.now() < self.muted_until:
                remaining = (self.muted_until - datetime.now()).total_seconds() / 60
                status_msg += f"🔇 All alerts muted for {remaining:.0f} minutes\n"
            
            await update.message.reply_text(status_msg, parse_mode='Markdown')
            
        except Exception as e:
            logging.error(f"Error handling status command: {e}")
            await update.message.reply_text("❌ Error getting status")
    
    async def _handle_summary(self, update, context):
        """Handle /summary command"""
        try:
            if not self.data_provider:
                await update.message.reply_text("❌ Data provider not available")
                return
            
            # Get market summary
            summary_msg = "📈 **Market Summary**\n\n"
            
            # Top symbols by activity (placeholder)
            summary_msg += "🔥 **Most Active:**\n"
            summary_msg += "• BTCUSDT: 📈 +2.3%\n"
            summary_msg += "• ETHUSDT: 📉 -1.1%\n"
            summary_msg += "• SOLUSDT: 📈 +5.7%\n\n"
            
            # Recent alerts summary
            if self.alert_engine:
                recent_alerts = self.alert_engine.get_recent_alerts(6)  # Last 6 hours
                if recent_alerts:
                    summary_msg += f"🚨 **Recent Alerts ({len(recent_alerts)}):**\n"
                    for alert in recent_alerts[-3:]:  # Show last 3
                        time_str = alert.timestamp.strftime('%H:%M')
                        summary_msg += f"• {time_str} {alert.symbol}: {alert.alert_type.value}\n"
                else:
                    summary_msg += "🚨 **No recent alerts**\n"
            
            await update.message.reply_text(summary_msg, parse_mode='Markdown')
            
        except Exception as e:
            logging.error(f"Error handling summary command: {e}")
            await update.message.reply_text("❌ Error getting summary")
    
    async def _handle_mute(self, update, context):
        """Handle /mute command"""
        try:
            args = context.args
            
            if not args:
                # Mute all alerts for 30 minutes
                self.muted_until = datetime.now() + timedelta(minutes=30)
                await update.message.reply_text("🔇 All alerts muted for 30 minutes")
                return
            
            if args[0].lower() == 'all':
                # Mute all alerts
                duration = int(args[1]) if len(args) > 1 else 30
                self.muted_until = datetime.now() + timedelta(minutes=duration)
                await update.message.reply_text(f"🔇 All alerts muted for {duration} minutes")
            else:
                # Mute specific symbol
                symbol = args[0].upper()
                self.muted_symbols.add(symbol)
                await update.message.reply_text(f"🔇 Alerts muted for {symbol}")
            
        except Exception as e:
            logging.error(f"Error handling mute command: {e}")
            await update.message.reply_text("❌ Error processing mute command")
    
    async def _handle_unmute(self, update, context):
        """Handle /unmute command"""
        try:
            args = context.args
            
            if not args or args[0].lower() == 'all':
                # Unmute all
                self.muted_symbols.clear()
                self.muted_until = None
                await update.message.reply_text("🔊 All alerts unmuted")
            else:
                # Unmute specific symbol
                symbol = args[0].upper()
                self.muted_symbols.discard(symbol)
                await update.message.reply_text(f"🔊 Alerts unmuted for {symbol}")
            
        except Exception as e:
            logging.error(f"Error handling unmute command: {e}")
            await update.message.reply_text("❌ Error processing unmute command")
    
    async def _handle_alerts(self, update, context):
        """Handle /alerts command"""
        try:
            if not self.alert_engine:
                await update.message.reply_text("❌ Alert engine not available")
                return
            
            hours = 6  # Default to last 6 hours
            if context.args and context.args[0].isdigit():
                hours = min(int(context.args[0]), 24)  # Max 24 hours
            
            recent_alerts = self.alert_engine.get_recent_alerts(hours)
            
            if not recent_alerts:
                await update.message.reply_text(f"📭 No alerts in the last {hours} hours")
                return
            
            alerts_msg = f"🚨 **Alerts ({hours}h) - {len(recent_alerts)} total**\n\n"
            
            # Group alerts by type
            alert_groups = {}
            for alert in recent_alerts:
                alert_type = alert.alert_type.value
                if alert_type not in alert_groups:
                    alert_groups[alert_type] = []
                alert_groups[alert_type].append(alert)
            
            # Show summary by type
            for alert_type, alerts in alert_groups.items():
                alerts_msg += f"**{alert_type.replace('_', ' ').title()}:** {len(alerts)}\n"
            
            alerts_msg += "\n**Recent:**\n"
            
            # Show last 5 alerts
            for alert in recent_alerts[-5:]:
                time_str = alert.timestamp.strftime('%H:%M')
                confidence_str = f"({alert.confidence:.1%})" if alert.confidence else ""
                alerts_msg += f"• {time_str} {alert.symbol}: {alert.alert_type.value} {confidence_str}\n"
            
            await update.message.reply_text(alerts_msg, parse_mode='Markdown')
            
        except Exception as e:
            logging.error(f"Error handling alerts command: {e}")
            await update.message.reply_text("❌ Error getting alerts")
    
    async def _handle_symbols(self, update, context):
        """Handle /symbols command"""
        try:
            if not self.data_provider:
                await update.message.reply_text("❌ Data provider not available")
                return
            
            # Get active symbols (placeholder)
            symbols_msg = "📋 **Active Symbols**\n\n"
            symbols_msg += "🔥 **Top Volume:**\n"
            symbols_msg += "• BTCUSDT\n• ETHUSDT\n• SOLUSDT\n• ADAUSDT\n• DOTUSDT\n\n"
            symbols_msg += f"📊 Total symbols monitored: 50+\n"
            
            await update.message.reply_text(symbols_msg, parse_mode='Markdown')
            
        except Exception as e:
            logging.error(f"Error handling symbols command: {e}")
            await update.message.reply_text("❌ Error getting symbols")
    
    async def _handle_config(self, update, context):
        """Handle /config command"""
        try:
            config_msg = "⚙️ **Configuration**\n\n"
            config_msg += "🔔 **Alert Settings:**\n"
            config_msg += f"• Cooldown: 5 minutes\n"
            config_msg += f"• Max per hour: 20\n"
            config_msg += f"• Sentiment threshold: 70%\n\n"
            
            config_msg += "📊 **Data Settings:**\n"
            config_msg += f"• Timeframe: 1h\n"
            config_msg += f"• Cache duration: 30s\n"
            config_msg += f"• CVD enabled: Yes\n"
            
            await update.message.reply_text(config_msg, parse_mode='Markdown')
            
        except Exception as e:
            logging.error(f"Error handling config command: {e}")
            await update.message.reply_text("❌ Error getting config")
    
    async def _handle_help(self, update, context):
        """Handle /help command"""
        help_msg = """🤖 **Trading Dashboard Bot Commands**

📊 **Status & Info:**
• `/status` - System status
• `/summary` - Market summary
• `/symbols` - Active symbols
• `/config` - Configuration

🚨 **Alerts:**
• `/alerts [hours]` - Recent alerts
• `/mute [symbol|all] [minutes]` - Mute alerts
• `/unmute [symbol|all]` - Unmute alerts

❓ **Help:**
• `/help` - This help message

**Examples:**
• `/mute BTCUSDT` - Mute BTCUSDT alerts
• `/mute all 60` - Mute all alerts for 1 hour
• `/alerts 12` - Show alerts from last 12 hours
"""
        
        await update.message.reply_text(help_msg, parse_mode='Markdown')
    
    async def _handle_unknown(self, update, context):
        """Handle unknown messages"""
        await update.message.reply_text(
            "❓ Unknown command. Use /help to see available commands."
        )
    
    def send_alert_notification(self, alert) -> bool:
        """Send alert notification to Telegram"""
        try:
            # Check if alerts are muted
            if self.is_muted(alert.symbol):
                return False
            
            # Format alert message
            message = self._format_alert_message(alert)
            
            # Send message
            return self.send_message(message)
            
        except Exception as e:
            logging.error(f"Error sending alert notification: {e}")
            return False
    
    def send_message(self, message: str, parse_mode: str = 'HTML') -> bool:
        """Send a message to Telegram"""
        try:
            url = f"https://api.telegram.org/bot{self.token}/sendMessage"
            payload = {
                'chat_id': self.chat_id,
                'text': message,
                'parse_mode': parse_mode
            }
            
            response = requests.post(url, json=payload, timeout=10)
            
            if response.status_code == 200:
                logging.info("Telegram message sent successfully")
                return True
            else:
                logging.error(f"Failed to send Telegram message: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logging.error(f"Error sending Telegram message: {e}")
            return False
    
    def is_muted(self, symbol: str) -> bool:
        """Check if alerts are muted for a symbol"""
        # Check global mute
        if self.muted_until and datetime.now() < self.muted_until:
            return True
        
        # Check symbol-specific mute
        return symbol in self.muted_symbols
    
    def _format_alert_message(self, alert) -> str:
        """Format alert for Telegram"""
        emoji_map = {
            'bullish_squeeze': '🟢',
            'bearish_squeeze': '🔴',
            'bullish_reversal': '🟢',
            'bearish_reversal': '🔴',
            'volume_spike': '📊',
            'cvd_divergence': '📈',
            'funding_extreme': '⚡',
            'oi_surge': '🔥'
        }
        
        emoji = emoji_map.get(alert.alert_type.value, '🚨')
        confidence_bar = '█' * int(alert.confidence * 10) if alert.confidence else ''
        
        message = f"{emoji} <b>ALERT</b>\n\n"
        message += f"<b>{alert.symbol}</b>\n"
        message += f"Type: {alert.alert_type.value.replace('_', ' ').title()}\n"
        
        if alert.confidence:
            message += f"Confidence: {alert.confidence:.1%} {confidence_bar}\n"
        
        message += f"Time: {alert.timestamp.strftime('%H:%M:%S')}\n\n"
        message += f"{alert.message}"
        
        return message
