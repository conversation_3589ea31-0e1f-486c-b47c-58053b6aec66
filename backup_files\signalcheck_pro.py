import dearpygui.dearpygui as dpg
import logging
import sys
import os
import datetime
import threading
import queue
import time
import json
import requests
import hmac
import hashlib
import configparser

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("signalcheck_pro.log")
    ]
)

class BybitAPI:
    """Direct implementation of Bybit API with proper timestamp synchronization"""

    def __init__(self, api_key, api_secret):
        self.api_key = api_key
        self.api_secret = api_secret
        self.base_url = "https://api.bybit.com"

    def get_server_time(self):
        """Get Bybit server time"""
        url = f"{self.base_url}/v5/market/time"
        response = requests.get(url)
        data = response.json()

        if data['retCode'] != 0:
            logging.error(f"Error getting server time: {data['retMsg']}")
            return None

        return data['result']

    def sync_time(self):
        """Synchronize time with Bybit server and return offset"""
        server_time = self.get_server_time()
        if not server_time:
            return 0

        server_timestamp = int(server_time['timeSecond']) * 1000
        local_timestamp = int(time.time() * 1000)
        time_offset = server_timestamp - local_timestamp

        logging.info(f"Time synchronized. Server: {server_timestamp}, Local: {local_timestamp}, Offset: {time_offset} ms")
        return time_offset

    def get_positions(self, category, symbol=None, settle_coin=None):
        """Get positions"""
        try:
            # Get server time for timestamp
            server_time = self.get_server_time()
            if not server_time:
                return {"retCode": -1, "retMsg": "Failed to get server time"}

            server_timestamp = int(server_time['timeSecond']) * 1000
            timestamp = server_timestamp
            recv_window = 120000  # 120 seconds (2 minutes) to handle large time differences

            # Create parameters dictionary
            params = {
                "category": category,
                "timestamp": timestamp,
                "recv_window": recv_window,
                "api_key": self.api_key
            }

            # Add optional parameters
            if symbol:
                params["symbol"] = symbol
            if settle_coin:
                params["settleCoin"] = settle_coin

            # Sort parameters alphabetically as required by Bybit
            sorted_params = dict(sorted(params.items()))

            # Create query string
            query_string = "&".join([f"{key}={value}" for key, value in sorted_params.items()])

            # Generate signature
            signature = hmac.new(
                bytes(self.api_secret, "utf-8"),
                bytes(query_string, "utf-8"),
                hashlib.sha256
            ).hexdigest()

            # Add signature to headers
            headers = {
                "X-BAPI-API-KEY": self.api_key,
                "X-BAPI-SIGN": signature,
                "X-BAPI-SIGN-TYPE": "2",
                "X-BAPI-TIMESTAMP": str(timestamp),
                "X-BAPI-RECV-WINDOW": str(recv_window)
            }

            # Build URL
            url = f"{self.base_url}/v5/position/list"

            # Log request details
            logging.debug(f"Request URL: {url}")
            logging.debug(f"Request Params: {sorted_params}")
            logging.debug(f"Request Headers: {headers}")
            logging.debug(f"Signature String: {query_string}")

            # Make request
            response = requests.get(url, params=sorted_params, headers=headers)
            return response.json()
        except Exception as e:
            logging.error(f"Error getting positions: {e}")
            return {"retCode": -1, "retMsg": str(e)}

    def get_tickers(self, category, symbol):
        """Get ticker information for a symbol"""
        url = f"{self.base_url}/v5/market/tickers?category={category}&symbol={symbol}"
        response = requests.get(url)
        return response.json()

class SignalCheckPro:
    def __init__(self):
        logging.info("Initializing SignalCheckPro")
        self.gui_queue = queue.Queue()
        self.stop_event = threading.Event()
        self.current_theme = "dark"
        self.themes = {
            "dark": {
                'background': (32, 32, 32),
                'panel': (45, 45, 48),
                'foreground': (255, 255, 255),
                'accent': (0, 120, 215),
                'success': (0, 200, 0),
                'warning': (255, 165, 0),
                'error': (255, 0, 0),
                'muted': (150, 150, 150),
                'highlight': (70, 70, 80),
                'border': (60, 60, 70)
            },
            "light": {
                'background': (240, 240, 240),
                'panel': (225, 225, 225),
                'foreground': (20, 20, 20),
                'accent': (0, 120, 215),
                'success': (0, 150, 0),
                'warning': (200, 120, 0),
                'error': (200, 0, 0),
                'muted': (100, 100, 100),
                'highlight': (210, 210, 220),
                'border': (180, 180, 190)
            }
        }
        self.theme = self.themes[self.current_theme]
        self.positions_data = []
        self.trade_history = []
        self.custom_symbols = ["BTCUSDT", "ETHUSDT", "SOLUSDT", "BNBUSDT", "ADAUSDT"]
        
        # Initialize Bybit API
        self.initialize_api()
        
    def initialize_api(self):
        """Initialize Bybit API"""
        try:
            # Load API keys from config file
            config = configparser.ConfigParser()
            config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.py")
            
            if os.path.exists(config_file):
                with open(config_file, "r") as f:
                    config_content = f.read()
                    
                # Extract API keys from config content
                api_key_match = re.search(r"api_key\s*=\s*['\"]([^'\"]+)['\"]", config_content)
                api_secret_match = re.search(r"api_secret\s*=\s*['\"]([^'\"]+)['\"]", config_content)
                
                if api_key_match and api_secret_match:
                    api_key = api_key_match.group(1)
                    api_secret = api_secret_match.group(1)
                    
                    # Initialize Bybit API
                    self.bybit_api = BybitAPI(api_key, api_secret)
                    self.time_offset = self.bybit_api.sync_time()
                    logging.info(f"Bybit API initialized successfully with time offset: {self.time_offset} ms")
                else:
                    logging.error("API keys not found in config file")
                    self.bybit_api = None
            else:
                logging.error("Config file not found")
                self.bybit_api = None
        except Exception as e:
            logging.error(f"Error initializing Bybit API: {e}")
            self.bybit_api = None
    
    def toggle_theme(self):
        """Toggle between dark and light themes"""
        try:
            # Toggle theme
            self.current_theme = "light" if self.current_theme == "dark" else "dark"
            self.theme = self.themes[self.current_theme]
            
            # Apply theme to viewport
            dpg.set_viewport_clear_color(self.theme['background'])
            
            logging.info(f"Theme changed to {self.current_theme}")
            
            # Show confirmation message
            with dpg.window(label="Theme Changed", modal=True, width=300, height=100, pos=[250, 200]):
                dpg.add_text(f"Theme changed to {self.current_theme.capitalize()} Mode")
                dpg.add_button(label="OK", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))
        except Exception as e:
            logging.error(f"Error toggling theme: {e}", exc_info=True)
    
    def show_about(self):
        """Show about dialog"""
        try:
            with dpg.window(label="About SignalCheck Pro", modal=True, width=500, height=300, pos=[150, 150]):
                dpg.add_text("SignalCheck Pro", color=(255, 255, 0))
                dpg.add_text("Version 1.0.0")
                dpg.add_spacer(height=10)
                dpg.add_text("A professional trading signal scanner and position management tool.")
                dpg.add_spacer(height=10)
                dpg.add_text("Features:")
                dpg.add_text("- Advanced signal scanning")
                dpg.add_text("- Position management")
                dpg.add_text("- Performance analytics")
                dpg.add_text("- Risk management")
                dpg.add_text("- Customizable UI")
                dpg.add_spacer(height=10)
                dpg.add_text("© 2023 SignalCheck Pro. All rights reserved.")
                dpg.add_spacer(height=10)
                dpg.add_button(label="OK", width=100, callback=lambda: dpg.delete_item(dpg.last_container()))
        except Exception as e:
            logging.error(f"Error showing about dialog: {e}", exc_info=True)
    
    def refresh_positions(self):
        """Refresh positions"""
        logging.info("Refreshing positions")
        
        try:
            # Update status
            if dpg.does_item_exist("positions_status"):
                dpg.set_value("positions_status", "Refreshing positions...")
            
            # Get positions from Bybit
            if self.bybit_api:
                positions_response = self.bybit_api.get_positions("linear")
                
                if positions_response['retCode'] == 0 and 'result' in positions_response and 'list' in positions_response['result']:
                    positions = positions_response['result']['list']
                    self.positions_data = positions
                    
                    # Clear existing rows
                    if dpg.does_item_exist("positions_table"):
                        children = dpg.get_item_children("positions_table", 1)
                        if children and len(children) > 0:
                            for child in children:
                                dpg.delete_item(child)
                    
                    # Add new rows
                    for i, position in enumerate(positions):
                        if float(position.get('size', 0)) > 0:  # Only show non-zero positions
                            with dpg.table_row(parent="positions_table"):
                                dpg.add_text(position.get('symbol', 'N/A'))
                                
                                # Determine side
                                side = "Buy" if position.get('side', '') == "Buy" else "Sell"
                                side_color = self.theme['success'] if side == "Buy" else self.theme['error']
                                dpg.add_text(side, color=side_color)
                                
                                # Add other position details
                                dpg.add_text(position.get('size', 'N/A'))
                                dpg.add_text(position.get('avgPrice', 'N/A'))
                                dpg.add_text(position.get('markPrice', 'N/A'))
                                
                                # Calculate P&L
                                pnl = float(position.get('unrealisedPnl', 0))
                                pnl_color = self.theme['success'] if pnl >= 0 else self.theme['error']
                                dpg.add_text(f"${pnl:.2f}", color=pnl_color)
                                
                                # Calculate P&L percent
                                entry_price = float(position.get('avgPrice', 0))
                                current_price = float(position.get('markPrice', 0))
                                if entry_price > 0:
                                    if side == "Buy":
                                        pnl_percent = ((current_price - entry_price) / entry_price) * 100
                                    else:
                                        pnl_percent = ((entry_price - current_price) / entry_price) * 100
                                    dpg.add_text(f"{pnl_percent:.2f}%", color=pnl_color)
                                else:
                                    dpg.add_text("N/A")
                    
                    # Update status
                    if dpg.does_item_exist("positions_status"):
                        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
                        dpg.set_value("positions_status", f"Positions refreshed at {timestamp}")
                else:
                    logging.error(f"Error getting positions: {positions_response.get('retMsg', 'Unknown error')}")
                    if dpg.does_item_exist("positions_status"):
                        dpg.set_value("positions_status", f"Error: {positions_response.get('retMsg', 'Unknown error')}")
            else:
                logging.error("Bybit API not initialized")
                if dpg.does_item_exist("positions_status"):
                    dpg.set_value("positions_status", "Error: Bybit API not initialized")
        except Exception as e:
            logging.error(f"Error refreshing positions: {e}", exc_info=True)
            if dpg.does_item_exist("positions_status"):
                dpg.set_value("positions_status", f"Error: {str(e)}")
    
    def create_main_window(self):
        """Create the main window and UI elements"""
        with dpg.window(tag="main_window", no_close=True, no_title_bar=True):
            # Main Menu Bar
            with dpg.menu_bar(tag="main_menu_bar"):
                with dpg.menu(label="File"):
                    dpg.add_menu_item(label="Exit", callback=lambda: dpg.stop_dearpygui())
                
                with dpg.menu(label="View"):
                    dpg.add_menu_item(label="Toggle Theme", callback=self.toggle_theme)
                
                with dpg.menu(label="Help"):
                    dpg.add_menu_item(label="About", callback=self.show_about)
            
            # Create tabs for different sections
            with dpg.tab_bar(tag="main_tab_bar"):
                # Positions Tab
                with dpg.tab(label="Positions", tag="positions_tab"):
                    with dpg.group(horizontal=True):
                        dpg.add_button(label="Refresh Positions", callback=self.refresh_positions, width=150)
                        dpg.add_spacer(width=20)
                        dpg.add_text("Status: Ready", tag="positions_status")
                    
                    dpg.add_spacer(height=20)
                    with dpg.table(tag="positions_table", header_row=True, resizable=True,
                                   row_background=True, borders_outerH=True, borders_innerV=True, 
                                   borders_innerH=True, borders_outerV=True, height=300):
                        dpg.add_table_column(label="Symbol")
                        dpg.add_table_column(label="Side")
                        dpg.add_table_column(label="Size")
                        dpg.add_table_column(label="Entry Price")
                        dpg.add_table_column(label="Current Price")
                        dpg.add_table_column(label="P&L")
                        dpg.add_table_column(label="P&L %")
    
    def run(self):
        """Run the GUI application"""
        logging.info("Running GUI...")
        
        try:
            # Create context
            dpg.create_context()
            logging.info("DPG context created")
            
            # Create viewport
            dpg.create_viewport(title="SignalCheck Pro", width=1200, height=800)
            dpg.set_viewport_clear_color(self.theme['background'])
            logging.info("DPG viewport created")
            
            # Create the main window and UI elements
            self.create_main_window()
            logging.info("Main window created")
            
            # Set primary window
            dpg.set_primary_window("main_window", True)
            
            # Setup and show
            dpg.setup_dearpygui()
            dpg.show_viewport()
            logging.info("Viewport shown")
            
            # Initial refresh of positions
            try:
                self.refresh_positions()
            except Exception as e:
                logging.error(f"Error during initial position refresh: {e}", exc_info=True)
            
            # Start DPG
            logging.info("Starting DPG main loop")
            dpg.start_dearpygui()
            
            # This point is reached when the GUI is closed
            logging.info("DPG main loop ended")
        except Exception as e:
            logging.error(f"Error running GUI: {e}", exc_info=True)
        finally:
            # Cleanup
            logging.info("Shutting down GUI...")
            self.stop_event.set()
            try:
                if dpg.is_dearpygui_running():
                    dpg.stop_dearpygui()
                dpg.destroy_context()
            except Exception as e:
                logging.error(f"Error during cleanup: {e}", exc_info=True)

def main():
    import re  # Import here to avoid IDE warnings
    app = SignalCheckPro()
    app.run()

if __name__ == "__main__":
    main()
