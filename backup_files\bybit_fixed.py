"""
Direct Bybit API implementation with proper timestamp synchronization
"""

import requests
import hmac
import hashlib
import json
import time
import logging

class BybitDirect:
    """
    Direct implementation of Bybit API with proper timestamp synchronization
    """

    def __init__(self, api_key, api_secret):
        self.api_key = api_key
        self.api_secret = api_secret
        self.base_url = "https://api.bybit.com"

    def get_server_time(self):
        """Get Bybit server time"""
        url = f"{self.base_url}/v5/market/time"
        response = requests.get(url)
        data = response.json()

        if data['retCode'] != 0:
            logging.error(f"Error getting server time: {data['retMsg']}")
            return None

        return data['result']

    def sync_time(self):
        """Synchronize time with Bybit server and return offset"""
        server_time = self.get_server_time()
        if not server_time:
            return 0

        server_timestamp = int(server_time['timeSecond']) * 1000
        local_timestamp = int(time.time() * 1000)
        time_offset = server_timestamp - local_timestamp

        logging.info(f"Time synchronized. Server: {server_timestamp}, Local: {local_timestamp}, Offset: {time_offset} ms")
        return time_offset

    def get_positions(self, category, symbol=None, settle_coin=None):
        """
        Get positions

        Args:
            category: Category (e.g., 'linear')
            symbol: Symbol (optional)
            settle_coin: Settle coin (optional)

        Returns:
            API response as dictionary
        """
        try:
            # Get server time for timestamp
            server_time = self.get_server_time()
            if not server_time:
                return {"retCode": -1, "retMsg": "Failed to get server time"}

            server_timestamp = int(server_time['timeSecond']) * 1000
            timestamp = server_timestamp
            recv_window = 120000  # 120 seconds (2 minutes) to handle large time differences

            # Create parameters dictionary
            params = {
                "category": category,
                "timestamp": timestamp,
                "recv_window": recv_window,
                "api_key": self.api_key
            }

            # Add optional parameters
            if symbol:
                params["symbol"] = symbol
            if settle_coin:
                params["settleCoin"] = settle_coin

            # Sort parameters alphabetically as required by Bybit
            sorted_params = dict(sorted(params.items()))

            # Create query string
            query_string = "&".join([f"{key}={value}" for key, value in sorted_params.items()])

            # Generate signature
            signature = hmac.new(
                bytes(self.api_secret, "utf-8"),
                bytes(query_string, "utf-8"),
                hashlib.sha256
            ).hexdigest()

            # Add signature to headers
            headers = {
                "X-BAPI-API-KEY": self.api_key,
                "X-BAPI-SIGN": signature,
                "X-BAPI-SIGN-TYPE": "2",
                "X-BAPI-TIMESTAMP": str(timestamp),
                "X-BAPI-RECV-WINDOW": str(recv_window)
            }

            # Build URL
            url = f"{self.base_url}/v5/position/list"

            # Log request details
            logging.debug(f"Request URL: {url}")
            logging.debug(f"Request Params: {sorted_params}")
            logging.debug(f"Request Headers: {headers}")
            logging.debug(f"Signature String: {query_string}")

            # Make request
            response = requests.get(url, params=sorted_params, headers=headers)
            return response.json()
        except Exception as e:
            logging.error(f"Error getting positions: {e}")
            return {"retCode": -1, "retMsg": str(e)}

    def place_order(self, **order_params):
        """
        Place an order

        Args:
            **order_params: Order parameters

        Returns:
            API response as dictionary
        """
        try:
            # Format quantity if needed
            if 'qty' in order_params and 'symbol' in order_params:
                qty = order_params['qty']
                symbol = order_params['symbol']
                
                # Get instrument info to format quantity correctly
                instrument_info = self.get_instruments_info('linear', symbol)
                if instrument_info['retCode'] == 0 and 'result' in instrument_info and 'list' in instrument_info['result'] and len(instrument_info['result']['list']) > 0:
                    instrument = instrument_info['result']['list'][0]
                    
                    # Get min quantity and quantity step
                    if 'lotSizeFilter' in instrument:
                        min_qty = float(instrument['lotSizeFilter'].get('minOrderQty', 0))
                        qty_step = float(instrument['lotSizeFilter'].get('qtyStep', 0))
                        
                        if min_qty > 0 and qty_step > 0:
                            try:
                                # Format quantity to match step size
                                formatted_qty = max(min_qty, round(float(qty) / qty_step) * qty_step)
                                formatted_qty = "{:.8f}".format(formatted_qty).rstrip('0').rstrip('.')
                                order_params['qty'] = formatted_qty
                                logging.info(f"Formatted quantity from {qty} to {formatted_qty} (min: {min_qty}, step: {qty_step})")
                            except (ValueError, TypeError) as e:
                                logging.warning(f"Could not format quantity: {e}")

            # Set positionIdx based on the side of the order
            if 'side' in order_params and order_params.get('category') == 'linear':
                side = order_params['side']
                if side == 'Buy':
                    order_params['positionIdx'] = 1  # hedge-mode Buy side
                    logging.info("Set positionIdx=1 for Buy order (hedge-mode Buy side)")
                elif side == 'Sell':
                    order_params['positionIdx'] = 2  # hedge-mode Sell side
                    logging.info("Set positionIdx=2 for Sell order (hedge-mode Sell side)")
                else:
                    # Default to one-way mode if side is not recognized
                    order_params['positionIdx'] = 0  # one-way mode
                    logging.info(f"Set positionIdx=0 for unknown side: {side} (one-way mode)")

            # Get server time for timestamp
            server_time = self.get_server_time()
            if not server_time:
                return {"retCode": -1, "retMsg": "Failed to get server time"}

            server_timestamp = int(server_time['timeSecond']) * 1000
            timestamp = server_timestamp
            recv_window = 120000  # 120 seconds (2 minutes) to handle large time differences

            # Prepare the request body (order parameters)
            request_body = json.dumps(order_params)

            # Prepare the signature string according to Bybit documentation
            # Format: timestamp + api_key + recv_window + request_body
            signature_string = f"{timestamp}{self.api_key}{recv_window}{request_body}"

            # Generate the signature
            signature = hmac.new(
                bytes(self.api_secret, "utf-8"),
                bytes(signature_string, "utf-8"),
                hashlib.sha256
            ).hexdigest()

            # Add signature to headers
            headers = {
                "X-BAPI-API-KEY": self.api_key,
                "X-BAPI-SIGN": signature,
                "X-BAPI-SIGN-TYPE": "2",
                "X-BAPI-TIMESTAMP": str(timestamp),
                "X-BAPI-RECV-WINDOW": str(recv_window),
                "Content-Type": "application/json"
            }

            # Log the request details for debugging
            url = f"{self.base_url}/v5/order/create"
            logging.debug(f"Request URL: {url}")
            logging.debug(f"Request Headers: {headers}")
            logging.debug(f"Request Body: {request_body}")

            # Make the request
            response = requests.post(url, headers=headers, data=request_body)
            return response.json()
        except Exception as e:
            logging.error(f"Error placing order: {e}")
            return {"retCode": -1, "retMsg": str(e)}

    def get_tickers(self, category, symbol):
        """Get ticker information for a symbol"""
        url = f"{self.base_url}/v5/market/tickers?category={category}&symbol={symbol}"
        response = requests.get(url)
        return response.json()

    def get_instruments_info(self, category, symbol):
        """Get instrument information for a symbol"""
        url = f"{self.base_url}/v5/market/instruments-info?category={category}&symbol={symbol}"
        response = requests.get(url)
        return response.json()

    def set_trading_stop(self, **params):
        """
        Set take profit, stop loss, and trailing stop for a position

        Args:
            **params: Parameters for setting trading stop

        Returns:
            API response as dictionary
        """
        try:
            # Get server time for timestamp
            server_time = self.get_server_time()
            if not server_time:
                return {"retCode": -1, "retMsg": "Failed to get server time"}

            server_timestamp = int(server_time['timeSecond']) * 1000
            timestamp = server_timestamp
            recv_window = 120000  # 120 seconds (2 minutes) to handle large time differences

            # Prepare the request body
            request_body = json.dumps(params)

            # Prepare the signature string
            signature_string = f"{timestamp}{self.api_key}{recv_window}{request_body}"

            # Generate the signature
            signature = hmac.new(
                bytes(self.api_secret, "utf-8"),
                bytes(signature_string, "utf-8"),
                hashlib.sha256
            ).hexdigest()

            # Add signature to headers
            headers = {
                "X-BAPI-API-KEY": self.api_key,
                "X-BAPI-SIGN": signature,
                "X-BAPI-SIGN-TYPE": "2",
                "X-BAPI-TIMESTAMP": str(timestamp),
                "X-BAPI-RECV-WINDOW": str(recv_window),
                "Content-Type": "application/json"
            }

            # Log the request details for debugging
            url = f"{self.base_url}/v5/position/trading-stop"
            logging.debug(f"Request URL: {url}")
            logging.debug(f"Request Headers: {headers}")
            logging.debug(f"Request Body: {request_body}")

            # Make the request
            response = requests.post(url, headers=headers, data=request_body)
            return response.json()
        except Exception as e:
            logging.error(f"Error setting trading stop: {e}")
            return {"retCode": -1, "retMsg": str(e)}

    def close_position(self, **params):
        """
        Close a position

        Args:
            **params: Parameters for closing position

        Returns:
            API response as dictionary
        """
        try:
            # Get server time for timestamp
            server_time = self.get_server_time()
            if not server_time:
                return {"retCode": -1, "retMsg": "Failed to get server time"}

            server_timestamp = int(server_time['timeSecond']) * 1000
            timestamp = server_timestamp
            recv_window = 120000  # 120 seconds (2 minutes) to handle large time differences

            # Prepare the request body
            request_body = json.dumps(params)

            # Prepare the signature string
            signature_string = f"{timestamp}{self.api_key}{recv_window}{request_body}"

            # Generate the signature
            signature = hmac.new(
                bytes(self.api_secret, "utf-8"),
                bytes(signature_string, "utf-8"),
                hashlib.sha256
            ).hexdigest()

            # Add signature to headers
            headers = {
                "X-BAPI-API-KEY": self.api_key,
                "X-BAPI-SIGN": signature,
                "X-BAPI-SIGN-TYPE": "2",
                "X-BAPI-TIMESTAMP": str(timestamp),
                "X-BAPI-RECV-WINDOW": str(recv_window),
                "Content-Type": "application/json"
            }

            # Log the request details for debugging
            url = f"{self.base_url}/v5/position/close"
            logging.debug(f"Request URL: {url}")
            logging.debug(f"Request Headers: {headers}")
            logging.debug(f"Request Body: {request_body}")

            # Make the request
            response = requests.post(url, headers=headers, data=request_body)
            return response.json()
        except Exception as e:
            logging.error(f"Error closing position: {e}")
            return {"retCode": -1, "retMsg": str(e)}
