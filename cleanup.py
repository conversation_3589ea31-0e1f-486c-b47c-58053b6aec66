import os
import shutil

# Directory path
directory = os.path.dirname(os.path.abspath(__file__))

# Files to remove
files_to_remove = [
    # Log files
    "trading_bot_fibzone_debug.log",
    "trading_bot_fibzone_debug.log.1",
    "trading_bot_fibzone_debug.log.2",
    "signalcheck_pro.log",
    
    # Backup/reference copies
    "huinav2_copy.py",
    "huinav2_copy_for_reference.py",
    
    # Older versions
    "fixed_huinav.py",
    "bybit_fixed.py",
    "signalcheck_pro.py"
]

# Create a backup directory
backup_dir = os.path.join(directory, "backup_files")
os.makedirs(backup_dir, exist_ok=True)

print("Cleaning up unrelated files...")

# Move files to backup directory first (safer than direct deletion)
for filename in files_to_remove:
    file_path = os.path.join(directory, filename)
    if os.path.exists(file_path):
        backup_path = os.path.join(backup_dir, filename)
        try:
            shutil.move(file_path, backup_path)
            print(f"Moved {filename} to backup directory")
        except Exception as e:
            print(f"Error moving {filename}: {e}")
    else:
        print(f"File not found: {filename}")

print("\nCleanup complete!")
print(f"Files have been moved to: {backup_dir}")
print("If everything works correctly, you can delete the backup directory later.")
