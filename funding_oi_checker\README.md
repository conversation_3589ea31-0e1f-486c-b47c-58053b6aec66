# 📊 Crypto Funding Rate & Open Interest Checker

A comprehensive cryptocurrency funding rate and open interest monitoring system that scans 400+ crypto perpetual futures for specific market conditions and generates intelligent alerts based on funding rate extremes, OI changes, CVD patterns, and volume analysis.

## ✨ Features

### 📊 **Comprehensive Market Scanning**
- **400+ Symbol Monitoring**: Scans all major crypto perpetual futures
- **Multi-Timeframe Analysis**: 5m, 15m, 30m, 1h, 4h, 1d tracking
- **Baseline Data Collection**: Initial scan establishes baseline values
- **Change Detection**: Only alerts when metrics actually change
- **Auto-Refresh Control**: User-controlled alert activation via checkbox

### 🚨 **Advanced Alert System (20 Alert Types)**

#### **Funding Rate Alerts (5 Types)**
- **Extreme Positive Funding**: Longs paying heavily (>1.5%) - potential long squeeze
- **Extreme Negative Funding**: Shorts paying heavily (<-1.5%) - potential short squeeze
- **Funding Reversal Bullish**: Funding shifts from positive to negative
- **Funding Reversal Bearish**: Funding shifts from negative to positive
- **Funding-Price Divergence**: Funding and price moving in opposite directions

#### **Open Interest Alerts (5 Types)**
- **OI Surge Bullish**: Rising OI + positive funding (new longs entering)
- **OI Surge Bearish**: Rising OI + negative funding (new shorts entering)
- **OI Dump/Liquidation**: Major OI drops (>20%) indicating liquidations
- **OI Trend Reversal**: Significant changes in OI trend direction
- **OI-Price Divergence**: OI and price moving in opposite directions

#### **Combined Squeeze Alerts (2 Types)**
- **Bullish Squeeze**: Negative funding + Rising OI + Rising CVD
- **Bearish Squeeze**: Positive funding + Rising OI + Falling CVD

#### **Reversal Pattern Alerts (2 Types)**
- **Bullish Reversal**: Falling OI + Extreme negative funding + CVD momentum shift
- **Bearish Reversal**: Falling OI + Extreme positive funding + CVD momentum shift

#### **CVD Alerts (3 Types)**
- **CVD Bullish Divergence**: Price falling while CVD rising (hidden buying)
- **CVD Bearish Divergence**: Price rising while CVD falling (hidden selling)
- **CVD Momentum Shift**: Significant changes in CVD trend direction

#### **Multi-Factor Alerts (2 Types)**
- **Multi-Bullish Alignment**: 3+ bullish factors aligned simultaneously
- **Multi-Bearish Alignment**: 3+ bearish factors aligned simultaneously

#### **Volume Alerts (1 Type)**
- **Volume Spike**: Volume exceeding configurable threshold multiplier

### 🎯 **Alert Timing System**
- **Initial Scan Protection**: No alerts during baseline data collection
- **Auto-Refresh Trigger**: Alerts only active when auto-refresh is enabled
- **Change Detection**: Only evaluates alerts when data actually changes
- **Cooldown Management**: Prevents alert spam with configurable cooldowns
- **Warmup Period**: Requires 3+ data cycles before alerting begins

## 🛠️ Installation

### Prerequisites
```bash
pip install PyQt5 pybit pandas numpy requests python-telegram-bot
```

### Setup
1. **Clone/Download** the project files
2. **Configure API Keys** in `config.py`:
   ```python
   api_key = "your_bybit_api_key"
   api_secret = "your_bybit_api_secret"
   TELEGRAM_TOKEN = "your_telegram_bot_token"  # Optional
   TELEGRAM_CHAT_ID = "your_telegram_chat_id"  # Optional
   ```
3. **Run the Dashboard**:
   ```bash
   python run_dashboard.py
   ```

## 📋 Usage

### **Step 1: Initial Setup**
1. **Configure API Keys** in `config.py`:
   ```python
   api_key = "your_bybit_api_key"
   api_secret = "your_bybit_api_secret"
   TELEGRAM_TOKEN = "your_telegram_bot_token"  # Optional
   TELEGRAM_CHAT_ID = "your_telegram_chat_id"  # Optional
   ```

2. **Start the Application**:
   ```bash
   python run_dashboard.py
   ```

### **Step 2: Baseline Data Collection**
1. **Load Symbols**: Click "📋 Load All Assets" to get 400+ symbols
2. **Manual Scan**: Click "🔄 Refresh" to collect baseline data
3. **Wait for Completion**: Status shows "📊 Scan: Collecting Baseline"
4. **No Alerts**: During this phase, NO alerts are generated

### **Step 3: Enable Alert Monitoring**
1. **Enable Auto-Refresh**: Check the "Auto Refresh" checkbox
2. **Set Interval**: Adjust refresh interval (recommended: 30-60 seconds)
3. **Alert Activation**: Status shows "📊 Scan: Alert Mode Active"
4. **Monitor Alerts**: Alerts appear in the side panel and via Telegram

### **Key Controls**
- **Auto Refresh Checkbox**: Controls when alerts are evaluated
- **Manual Refresh**: Collects baseline data only (no alerts)
- **Symbol Input**: Add/remove specific symbols to monitor
- **Refresh Interval**: Time between auto-refresh cycles

### **Alert Behavior**
- **Manual Scans**: Never generate alerts (baseline collection only)
- **Auto-Refresh**: Compares new data vs baseline, alerts on changes
- **Change Detection**: Only alerts when funding/OI/CVD actually changes
- **Cooldown Protection**: Prevents duplicate alerts within time window

## 🔧 Configuration

### Alert Thresholds
```json
{
  "global_defaults": {
    "oi_threshold": 0.02,        // 2% OI change threshold
    "fr_threshold": 0.01,        // 1% funding rate threshold  
    "volume_threshold_multiplier": 2.0,  // 2x average volume
    "cvd_threshold": 0.05,       // CVD change threshold
    "enable_alerts": true
  },
  "symbol_specific": {
    "BTCUSDT": {
      "oi_threshold": 0.03,      // Custom threshold for BTC
      "fr_min": -0.01,
      "volume_threshold": 5000000
    }
  }
}
```

### UI Settings
```json
{
  "ui": {
    "theme": "dark",
    "auto_refresh_interval": 30,
    "table_row_height": 35,
    "enable_animations": true
  },
  "alerts": {
    "cooldown_minutes": 5,
    "max_alerts_per_hour": 20,
    "sentiment_threshold": 0.7
  }
}
```

## 📊 Signal Interpretation & Market Context

### **Funding Rate & Open Interest Combinations**
Based on your research, here are the key signal patterns:

#### **🟢 Bullish Patterns**
- **OI ↑ + Funding ↑**: New longs entering (bullish trend continuation)
- **OI ↑ + Funding ↓**: New shorts entering (potential short squeeze setup)
- **OI ↓ + Funding ↓**: Short covering (bullish reversal signal)
- **Extreme Negative Funding**: Shorts paying heavily (squeeze potential)

#### **🔴 Bearish Patterns**
- **OI ↑ + Funding ↑**: Overheated longs (potential long squeeze)
- **OI ↓ + Funding ↑**: Long liquidations (bearish reversal)
- **Extreme Positive Funding**: Longs paying heavily (squeeze risk)

### **CVD & Volume Confirmation Signals**

#### **🟢 Bullish Confirmations**
- **Price ↑ + CVD ↑ + Volume ↑**: Strong bullish trend confirmation
- **Price ↓ + CVD ↑**: Hidden buying pressure (bullish divergence)
- **CVD Rising + Negative Funding**: Buying pressure + short squeeze setup

#### **🔴 Bearish Confirmations**
- **Price ↓ + CVD ↓ + Volume ↑**: Strong bearish trend confirmation
- **Price ↑ + CVD ↓**: Hidden selling pressure (bearish divergence)
- **CVD Falling + Positive Funding**: Selling pressure + long squeeze setup

### **Multi-Factor Signal Strength**
- **3+ Aligned Factors**: High confidence signals
- **Divergence Patterns**: Early reversal warnings
- **Volume Confirmation**: Validates directional moves
- **Extreme Funding**: Indicates potential squeeze conditions

## 📁 File Structure

```
funding_oi_checker/
├── run_dashboard.py          # Main launcher
├── trading_dashboard.py      # Main GUI application
├── data_provider.py          # Data fetching and processing
├── alert_engine.py           # Alert logic and management
├── config_manager.py         # Configuration management
├── bot_listener.py           # Telegram bot integration
├── ui_theme.py              # UI styling and themes
├── config.py                # API keys and basic config
├── symbols.json             # Monitored symbols list
├── dashboard_config.json    # UI and system settings
├── alert_config.json        # Alert thresholds and rules
├── alerts.log              # Alert history log
└── README.md               # This file
```

## 🔍 Troubleshooting

### Common Issues
1. **API Connection Errors**: Check API keys and network connection
2. **Missing Dependencies**: Run `pip install -r requirements.txt`
3. **Telegram Bot Issues**: Verify bot token and chat ID
4. **Performance Issues**: Reduce symbol count or increase refresh interval

### Logging
- Application logs: `logs/dashboard.log`
- Alert history: `alerts.log`
- Debug mode: Set logging level to DEBUG in `run_dashboard.py`

## 🚀 Advanced Usage

### Custom Signal Development
Extend the `AlertEngine` class to add custom signal detection:

```python
def _check_custom_pattern(self, data: MarketData, config: Dict) -> List[Alert]:
    # Your custom signal logic here
    pass
```

### Data Export
- **Alert Export**: Use the "Export" button in the Alerts tab
- **Market Data**: Access via `self.market_data` in the main application
- **Historical Analysis**: Extend `TrendContextMemory` for longer history

## 📞 Support

For issues, feature requests, or questions:
1. Check the logs for error details
2. Verify configuration settings
3. Test with a smaller symbol set
4. Review the alert thresholds

## ⚠️ Disclaimer

This software is for educational and informational purposes only. It is not financial advice. Trading cryptocurrencies involves substantial risk and may result in significant losses. Always do your own research and consider your risk tolerance before making trading decisions.

## 📄 License

This project is provided as-is for educational purposes. Use at your own risk.
