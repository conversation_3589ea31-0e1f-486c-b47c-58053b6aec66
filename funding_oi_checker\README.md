# 🚀 Advanced Crypto Trading Dashboard

A comprehensive, real-time cryptocurrency trading dashboard that combines funding rates, open interest, cumulative volume delta (CVD), and volume analysis to generate intelligent trading signals.

## ✨ Features

### 📊 **Smart Data & Alert Engine**
- **Per-Symbol Settings**: Configurable alert thresholds for each trading pair
- **Trend Context Memory**: Stores last 3-5 snapshots to detect momentum & trend reversals
- **Cooldown System**: Prevents repeated alerts within configurable time windows
- **Alert History**: All alerts stored in local `alerts.log` file, viewable in GUI
- **Sentiment Scoring**: Multi-factor sentiment analysis with visual indicators
- **Telegram Bot Integration**: Accept commands like `/status`, `/summary`, `/mute`

### 🎨 **Modern UI/UX Design**
- **Trading-Grade Dashboard**: Professional interface optimized for traders
- **Beautiful Table Styling**: Color-coded values, icons, zebra striping
- **Dark/Light Themes**: Toggle between themes with modern styling
- **Real-time Updates**: Live data refresh with configurable intervals
- **Responsive Layout**: Resizable panels and tables
- **Visual Indicators**: Arrows, progress bars, and color coding

### 📈 **Advanced Market Analysis**
- **Funding Rate Analysis**: Real-time funding rates with trend detection
- **Open Interest Tracking**: OI changes with percentage calculations
- **CVD (Cumulative Volume Delta)**: Order flow analysis for buy/sell pressure
- **Volume Spike Detection**: Identifies unusual volume activity
- **Multi-Factor Signals**: Combines all metrics for comprehensive analysis

### 🚨 **Intelligent Alert System**
- **Bullish Squeeze**: Negative funding + Rising OI + Rising CVD
- **Bearish Squeeze**: Positive funding + Rising OI + Falling CVD  
- **Reversal Patterns**: Falling OI + Extreme funding + CVD momentum shift
- **Volume Spikes**: Configurable volume threshold alerts
- **CVD Divergence**: Price vs CVD divergence detection

## 🛠️ Installation

### Prerequisites
```bash
pip install PyQt5 pybit pandas numpy requests python-telegram-bot
```

### Setup
1. **Clone/Download** the project files
2. **Configure API Keys** in `config.py`:
   ```python
   api_key = "your_bybit_api_key"
   api_secret = "your_bybit_api_secret"
   TELEGRAM_TOKEN = "your_telegram_bot_token"  # Optional
   TELEGRAM_CHAT_ID = "your_telegram_chat_id"  # Optional
   ```
3. **Run the Dashboard**:
   ```bash
   python run_dashboard.py
   ```

## 📋 Usage

### Main Interface
- **Symbol Input**: Add/remove trading pairs (comma-separated)
- **Refresh Controls**: Manual refresh and auto-refresh interval
- **Theme Toggle**: Switch between dark and light modes
- **Real-time Table**: Live market data with color-coded indicators

### Alert Configuration
- **Global Settings**: Default thresholds for all symbols
- **Per-Symbol Config**: Custom settings for specific trading pairs
- **Cooldown Management**: Prevent alert spam
- **Telegram Integration**: Remote monitoring and control

### Telegram Bot Commands
- `/status` - System and alert status
- `/summary` - Market summary with top movers
- `/alerts [hours]` - Recent alerts (default: 6 hours)
- `/mute [symbol|all] [minutes]` - Mute alerts
- `/unmute [symbol|all]` - Unmute alerts
- `/symbols` - List active symbols
- `/config` - Show current configuration
- `/help` - Command help

## 🔧 Configuration

### Alert Thresholds
```json
{
  "global_defaults": {
    "oi_threshold": 0.02,        // 2% OI change threshold
    "fr_threshold": 0.01,        // 1% funding rate threshold  
    "volume_threshold_multiplier": 2.0,  // 2x average volume
    "cvd_threshold": 0.05,       // CVD change threshold
    "enable_alerts": true
  },
  "symbol_specific": {
    "BTCUSDT": {
      "oi_threshold": 0.03,      // Custom threshold for BTC
      "fr_min": -0.01,
      "volume_threshold": 5000000
    }
  }
}
```

### UI Settings
```json
{
  "ui": {
    "theme": "dark",
    "auto_refresh_interval": 30,
    "table_row_height": 35,
    "enable_animations": true
  },
  "alerts": {
    "cooldown_minutes": 5,
    "max_alerts_per_hour": 20,
    "sentiment_threshold": 0.7
  }
}
```

## 📊 Signal Interpretation

### 🟢 **Bullish Signals**
- **Squeeze Setup**: Negative funding (shorts paying) + Rising OI + Rising CVD
- **Reversal**: Falling OI + Extreme negative funding + CVD momentum shift
- **Volume Confirmation**: High volume supporting bullish moves

### 🔴 **Bearish Signals**  
- **Squeeze Setup**: Positive funding (longs paying) + Rising OI + Falling CVD
- **Reversal**: Falling OI + Extreme positive funding + CVD momentum shift
- **Distribution**: High volume with falling CVD (selling pressure)

### 📈 **Market Context**
- **OI ↑ + Funding ↑**: New longs entering (bullish trend)
- **OI ↑ + Funding ↓**: New shorts entering (potential squeeze)
- **OI ↓ + Funding ↑**: Long liquidations (bearish reversal)
- **OI ↓ + Funding ↓**: Short covering (bullish reversal)

## 📁 File Structure

```
funding_oi_checker/
├── run_dashboard.py          # Main launcher
├── trading_dashboard.py      # Main GUI application
├── data_provider.py          # Data fetching and processing
├── alert_engine.py           # Alert logic and management
├── config_manager.py         # Configuration management
├── bot_listener.py           # Telegram bot integration
├── ui_theme.py              # UI styling and themes
├── config.py                # API keys and basic config
├── symbols.json             # Monitored symbols list
├── dashboard_config.json    # UI and system settings
├── alert_config.json        # Alert thresholds and rules
├── alerts.log              # Alert history log
└── README.md               # This file
```

## 🔍 Troubleshooting

### Common Issues
1. **API Connection Errors**: Check API keys and network connection
2. **Missing Dependencies**: Run `pip install -r requirements.txt`
3. **Telegram Bot Issues**: Verify bot token and chat ID
4. **Performance Issues**: Reduce symbol count or increase refresh interval

### Logging
- Application logs: `logs/dashboard.log`
- Alert history: `alerts.log`
- Debug mode: Set logging level to DEBUG in `run_dashboard.py`

## 🚀 Advanced Usage

### Custom Signal Development
Extend the `AlertEngine` class to add custom signal detection:

```python
def _check_custom_pattern(self, data: MarketData, config: Dict) -> List[Alert]:
    # Your custom signal logic here
    pass
```

### Data Export
- **Alert Export**: Use the "Export" button in the Alerts tab
- **Market Data**: Access via `self.market_data` in the main application
- **Historical Analysis**: Extend `TrendContextMemory` for longer history

## 📞 Support

For issues, feature requests, or questions:
1. Check the logs for error details
2. Verify configuration settings
3. Test with a smaller symbol set
4. Review the alert thresholds

## ⚠️ Disclaimer

This software is for educational and informational purposes only. It is not financial advice. Trading cryptocurrencies involves substantial risk and may result in significant losses. Always do your own research and consider your risk tolerance before making trading decisions.

## 📄 License

This project is provided as-is for educational purposes. Use at your own risk.
