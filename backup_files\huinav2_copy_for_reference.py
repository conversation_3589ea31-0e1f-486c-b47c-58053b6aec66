# -*- coding: utf-8 -*-
import dearpygui.dearpygui as dpg
import requests
import pandas as pd
import numpy as np
import ta
import re
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import time
import json
from pathlib import Path
import logging
from logging.handlers import RotatingFileHandler
from typing import Dict, Optional, Tuple, List, Any
from scipy.signal import argrelextrema
import datetime
from pybit.unified_trading import HTTP
import sys
import html # Import html for escaping
import queue # For thread-safe GUI updates

# --- Logging Configuration ---
def configure_logging(log_file: str = "trading_bot_fibzone_debug.log") -> None:
    logger = logging.getLogger()
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
        handler.close()

    logger.setLevel(logging.DEBUG)
    formatter = logging.Formatter("%(asctime)s %(levelname)s: %(message)s")

    # Console handler
    ch = logging.StreamHandler(stream=sys.stdout)
    ch.setLevel(logging.DEBUG)
    ch.setFormatter(formatter)
    logger.addHandler(ch)
    logging.info("Console logging enabled.")

    # File handler
    try:
        log_path = Path(log_file).resolve()
        log_path.parent.mkdir(parents=True, exist_ok=True)
        fh = RotatingFileHandler(log_path, maxBytes=5 * 1024 * 1024, backupCount=2, encoding='utf-8')
        fh.setLevel(logging.DEBUG)
        fh.setFormatter(formatter)
        logger.addHandler(fh)
        logging.info("File logging configured successfully.")
    except Exception as e:
        logging.warning(f"Failed to configure file logging: {e}")

# --- TTL Cache ---
class TTLCache:
    def __init__(self, ttl: float):
        self.ttl = ttl
        self.cache: Dict[str, Tuple[float, Any]] = {}
        self.lock = threading.Lock()

    def get(self, key: str) -> Optional[Any]:
        with self.lock:
            entry = self.cache.get(key)
            if entry and time.time() - entry[0] < self.ttl:
                return entry[1]
            if entry: del self.cache[key]
        return None

    def set(self, key: str, value: Any) -> None:
        with self.lock:
            self.cache[key] = (time.time(), value)

    def clear(self) -> None:
        with self.lock:
            self.cache.clear()
            logging.debug("Cache cleared.")

# --- Trading Bot ---
class AdvancedTradingBot:
    def __init__(self):
        # --- REPLACE CREDENTIALS ---
        self.api_key = "aMKaaFNd57yeENDYF1"
        self.api_secret = "bKzraCkh1tqEXQBuX0wVobuwz3cyhQEBxnrQ"
        # --- END CREDENTIALS ---
        try:
            from pybit.unified_trading import HTTP
            self.session = HTTP(api_key=self.api_key, api_secret=self.api_secret, recv_window=20000)
            server_time = self.session.get_server_time()
            logging.info(f"Bybit session initialized successfully. Server Time: {server_time.get('timeNano', 'N/A')}")
        except Exception as e:
            logging.error(f"Failed to initialize Bybit session: {e}")
            self.session = None

        self.cache = TTLCache(ttl=60)
        self.higher_timeframes = {'5m': '15m', '15m': '1h', '1h': '4h', '4h': '1d', '1d': '1w'}
        self.timeframe_to_interval = {'5m': '5', '15m': '15', '1h': '60', '4h': '240', '1d': 'D', '1w': 'W'}
        self.timeframe_to_oi_interval = {'5m': '5min', '15m': '15min', '1h': '1h', '4h': '4h', '1d': '1d'}
        self.atr_multipliers = {
            '5m': {'sl': 1.8, 'tp': 3.0}, '15m': {'sl': 2.0, 'tp': 3.5}, '1h': {'sl': 2.2, 'tp': 4.0},
            '4h': {'sl': 2.5, 'tp': 4.5}, '1d': {'sl': 3.0, 'tp': 5.0}, '1w': {'sl': 3.5, 'tp': 6.0}
        }
        self.tci_length = 20; self.tci_smooth_length = 5
        self.overbought_level = 60; self.oversold_level = -60
        self.rsi_length = 14; self.stoch_length = 14; self.stoch_smooth_k = 3; self.stoch_smooth_d = 3
        self.mfi_length = 14
        self.supertrend_atr_length = 10; self.supertrend_multiplier = 3.0

    # --- Data Fetching Methods ---
    def fetch_data(self, symbol: str, timeframe: str = '1h', limit: int = 70) -> pd.DataFrame:
        if not self.session:
             logging.error("Bybit session not initialized. Cannot fetch data.")
             return pd.DataFrame(columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        cache_key = f"{symbol}_{timeframe}_ohlcv_{limit}"
        cached = self.cache.get(cache_key)
        if cached is not None: return cached.copy()
        try:
            interval = self.timeframe_to_interval.get(timeframe)
            if not interval: raise ValueError(f"Unsupported timeframe for OHLCV: {timeframe}")
            response = self.session.get_kline(category="linear", symbol=symbol, interval=interval, limit=limit)
            if response['retCode'] == 0 and response['result'] and response['result']['list']:
                data = response['result']['list']
                df = pd.DataFrame(data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume', 'turnover'])
                df['timestamp'] = pd.to_datetime(df['timestamp'].astype(np.int64), unit='ms')
                numeric_cols = ['open', 'high', 'low', 'close', 'volume']
                df[numeric_cols] = df[numeric_cols].apply(pd.to_numeric, errors='coerce')
                df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']]
                df = df.sort_values('timestamp', ascending=True).reset_index(drop=True)
                df.dropna(inplace=True)
                if not df.empty:
                    self.cache.set(cache_key, df)
                    return df.copy()
                else:
                     logging.warning(f"No valid data rows after processing for {symbol} on {timeframe}")
                     return pd.DataFrame(columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            else:
                ret_msg = response.get('retMsg', 'No message')
                if "symbol param invalid" in ret_msg: logging.warning(f"Invalid symbol for Bybit: {symbol}. Skipping.")
                else: logging.warning(f"No data returned for {symbol} on {timeframe}. RC: {response.get('retCode')}, Msg: {ret_msg}")
                return pd.DataFrame(columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        except ValueError as ve:
             logging.error(f"Value error fetching OHLCV for {symbol}: {ve}")
             return pd.DataFrame(columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        except Exception as e:
            logging.error(f"Generic error fetching OHLCV for {symbol} ({timeframe}): {type(e).__name__} - {e}", exc_info=False)
            return pd.DataFrame(columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

    def fetch_open_interest(self, symbol: str, timeframe: str) -> List[float]:
        if not self.session: return []
        cache_key = f"{symbol}_{timeframe}_oi"; cached = self.cache.get(cache_key)
        if cached is not None: return cached
        try:
            interval_time = self.timeframe_to_oi_interval.get(timeframe, '1h')
            response = self.session.get_open_interest(category="linear", symbol=symbol, intervalTime=interval_time, limit=2)
            if response['retCode'] == 0 and response['result'] and response['result']['list']:
                data = response['result']['list']; data.reverse()
                if len(data) >= 2:
                    oi_values = [float(item['openInterest']) for item in data]
                    self.cache.set(cache_key, oi_values); return oi_values
            return []
        except Exception as e:
            logging.error(f"Error fetching OI for {symbol} ({timeframe}): {type(e).__name__} - {e}", exc_info=False)
            return []

    def fetch_funding_rate(self, symbol: str) -> Tuple[Optional[float], Optional[str]]:
        if not self.session: return None, None
        cache_key = f"{symbol}_fr"; cached = self.cache.get(cache_key)
        if cached is not None: return cached
        try:
            response = self.session.get_funding_rate_history(category="linear", symbol=symbol, limit=1)
            if response['retCode'] == 0 and response['result'] and response['result']['list']:
                data = response['result']['list']
                if data:
                    latest = data[0]; fr = float(latest['fundingRate'])
                    ts_ms = int(latest['fundingRateTimestamp'])
                    ts_str = datetime.datetime.fromtimestamp(ts_ms / 1000, tz=datetime.timezone.utc).strftime('%H:%M UTC')
                    self.cache.set(cache_key, (fr, ts_str)); return fr, ts_str
            return None, None
        except Exception as e:
            logging.error(f"Error fetching funding rate for {symbol}: {type(e).__name__} - {e}", exc_info=False)
            return None, None

    # --- Indicator Calculations ---
    def calculate_supertrend(self, df: pd.DataFrame, atr_length: int = 10, multiplier: float = 3.0) -> pd.DataFrame:
        df = df.copy()
        if not all(col in df.columns for col in ['high', 'low', 'close']):
            logging.warning("Missing HLC columns for SuperTrend calculation."); df['supertrend'] = np.nan; df['supertrend_signal'] = 0; return df
        try:
            st_atr = ta.volatility.average_true_range(df['high'], df['low'], df['close'], window=atr_length, fillna=True)
            upper_basic = (df['high'] + df['low']) / 2 + multiplier * st_atr
            lower_basic = (df['high'] + df['low']) / 2 - multiplier * st_atr
            upper_final = upper_basic.copy(); lower_final = lower_basic.copy()
            supertrend = pd.Series(np.nan, index=df.index); signal = pd.Series(0, index=df.index)
            close = df['close']; prev_close = close.shift(1)
            prev_upper_final = upper_final.shift(1); prev_lower_final = lower_final.shift(1)
            prev_supertrend = supertrend.shift(1)
            for i in range(1, len(df)):
                if (upper_basic.iloc[i] < prev_upper_final.iloc[i]) or (prev_close.iloc[i] > prev_upper_final.iloc[i]): upper_final.iloc[i] = upper_basic.iloc[i]
                else: upper_final.iloc[i] = prev_upper_final.iloc[i]
                if (lower_basic.iloc[i] > prev_lower_final.iloc[i]) or (prev_close.iloc[i] < prev_lower_final.iloc[i]): lower_final.iloc[i] = lower_basic.iloc[i]
                else: lower_final.iloc[i] = prev_lower_final.iloc[i]
                if prev_supertrend.iloc[i] == prev_upper_final.iloc[i]:
                    if close.iloc[i] <= upper_final.iloc[i]: supertrend.iloc[i] = upper_final.iloc[i]; signal.iloc[i] = -1
                    else: supertrend.iloc[i] = lower_final.iloc[i]; signal.iloc[i] = 1
                elif prev_supertrend.iloc[i] == prev_lower_final.iloc[i]:
                    if close.iloc[i] >= lower_final.iloc[i]: supertrend.iloc[i] = lower_final.iloc[i]; signal.iloc[i] = 1
                    else: supertrend.iloc[i] = upper_final.iloc[i]; signal.iloc[i] = -1
                else:
                    if close.iloc[i] >= lower_final.iloc[i]: supertrend.iloc[i] = lower_final.iloc[i]; signal.iloc[i] = 1
                    else: supertrend.iloc[i] = upper_final.iloc[i]; signal.iloc[i] = -1
            df['supertrend'] = supertrend; df['supertrend_signal'] = signal.fillna(0).astype(int)
            if len(df) > 1 and df['supertrend_signal'].iloc[0] == 0: df.loc[df.index[0], 'supertrend_signal'] = df['supertrend_signal'].iloc[1]
        except Exception as e:
            logging.error(f"SuperTrend calc error: {type(e).__name__} - {e}", exc_info=False)
            if 'supertrend' not in df.columns: df['supertrend'] = np.nan
            if 'supertrend_signal' not in df.columns: df['supertrend_signal'] = 0
        return df

    # <<< THIS METHOD WAS MISSING OR MISPLACED >>>
    def find_divergences(self, df: pd.DataFrame, lookback: int = 5) -> pd.DataFrame:
        df = df.copy(); indicators = {'rsi': 'rsi', 'stoch': 'stoch_rsi_k', 'wt1': 'tci', 'wt2': 'wt2'}
        for k in indicators: df[f'bullish_div_{k}'] = False; df[f'bearish_div_{k}'] = False
        if len(df) < lookback * 2 + 1: return df

        symbol_name = df.name if hasattr(df, 'name') else 'N/A' # Get symbol name for logging

        try:
            n = lookback; low_min = df['low'].rolling(window=n, center=True, min_periods=1).min()
            high_max = df['high'].rolling(window=n, center=True, min_periods=1).max()
            is_trough = (df['low'] == low_min); is_peak = (df['high'] == high_max)

            for ind_key, ind_col in indicators.items():
                if ind_col not in df.columns or df[ind_col].isnull().all(): continue
                ind_min = df[ind_col].rolling(window=n, center=True, min_periods=1).min()
                ind_max = df[ind_col].rolling(window=n, center=True, min_periods=1).max()
                is_ind_trough = (df[ind_col] == ind_min); is_ind_peak = (df[ind_col] == ind_max)
                potential_bull_divs = df[is_trough & is_ind_trough].index
                potential_bear_divs = df[is_peak & is_ind_peak].index

                # Check Bullish Divergence
                for current_idx in potential_bull_divs:
                    prev_troughs = potential_bull_divs[potential_bull_divs < current_idx]
                    if len(prev_troughs) > 0: # Check if list is NOT empty
                        prev_idx = prev_troughs[-1] # Assign only if prev_troughs exists
                        # Check divergence conditions ONLY if prev_idx was assigned
                        if df.loc[current_idx, 'low'] < df.loc[prev_idx, 'low'] and df.loc[current_idx, ind_col] > df.loc[prev_idx, ind_col]:
                            df.loc[current_idx, f'bullish_div_{ind_key}'] = True
                    # else: logging.debug(f"({symbol_name}) No prev common trough for bull div at {current_idx}")

                # Check Bearish Divergence
                for current_idx in potential_bear_divs:
                    prev_peaks = potential_bear_divs[potential_bear_divs < current_idx]
                    if len(prev_peaks) > 0: # Check if list is NOT empty
                        prev_idx = prev_peaks[-1] # Assign only if prev_peaks exists
                        # Check divergence conditions ONLY if prev_idx was assigned
                        if df.loc[current_idx, 'high'] > df.loc[prev_idx, 'high'] and df.loc[current_idx, ind_col] < df.loc[prev_idx, ind_col]:
                            df.loc[current_idx, f'bearish_div_{ind_key}'] = True
                    # else: logging.debug(f"({symbol_name}) No prev common peak for bear div at {current_idx}")

        except Exception as e: logging.error(f"({symbol_name}) Divergence detection error: {type(e).__name__} - {e}", exc_info=True) # Log full traceback
        return df
    # <<< END find_divergences METHOD >>>

    def calculate_fibonacci_levels(self, df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        df = df.copy(); lookback_period = 20
        fib_cols = ['fib_0', 'fib_236', 'fib_382', 'fib_618', 'fib_1', 'fib_1618', 'fib_2', 'fib_neg618']
        for col in fib_cols: df[col] = np.nan
        symbol_name = df.name if hasattr(df,'name') else 'N/A'
        if len(df) < lookback_period: return df
        try:
            recent = df.iloc[-lookback_period:]; swing_high = recent['high'].max(); swing_low = recent['low'].min()
            if pd.isna(swing_high) or pd.isna(swing_low):
                logging.warning(f"({symbol_name}) NaN swing H/L for {timeframe}. Cannot calc Fibs."); return df
            diff = swing_high - swing_low
            # Handle potential NaN in ATR or close for min_diff calculation
            last_close = df['close'].iloc[-1]
            last_atr = df['atr'].iloc[-1] if 'atr' in df.columns and not pd.isna(df['atr'].iloc[-1]) else np.nan
            if pd.isna(last_atr) and not pd.isna(last_close) and last_close > 0: last_atr = last_close * 0.01
            min_diff = 0.01 # Default small value
            if not pd.isna(last_close) and last_close > 0 and not pd.isna(last_atr) and last_atr > 0:
                 min_diff = max(last_atr * 3, last_close * 0.005)
            min_diff = max(min_diff, 1e-8)

            if diff < min_diff and diff >= 0:
                 mid = (swing_high + swing_low) / 2; swing_high = mid + min_diff / 2; swing_low = mid - min_diff / 2; diff = min_diff
            elif diff < 0: logging.warning(f"({symbol_name}) Invalid swing range for {timeframe}. Diff={diff:.4f}."); return df
            last_idx = df.index[-1]
            df.loc[last_idx, 'fib_0'] = swing_low; df.loc[last_idx, 'fib_236'] = swing_low + 0.236 * diff
            df.loc[last_idx, 'fib_382'] = swing_low + 0.382 * diff; df.loc[last_idx, 'fib_618'] = swing_low + 0.618 * diff
            df.loc[last_idx, 'fib_1'] = swing_high; df.loc[last_idx, 'fib_1618'] = swing_high + 0.618 * diff
            df.loc[last_idx, 'fib_2'] = swing_high + 1.0 * diff; df.loc[last_idx, 'fib_neg618'] = swing_low - 0.618 * diff
        except Exception as e: logging.error(f"({symbol_name}) Fibonacci calc error ({timeframe}): {type(e).__name__} - {e}", exc_info=False)
        return df

    def calculate_indicators(self, df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        required_length = 50; symbol_name = df.name if hasattr(df,'name') else 'N/A'
        if df.empty or len(df) < required_length:
            logging.warning(f"({symbol_name}) Insufficient data ({len(df)}) for indicators on {timeframe}. Need {required_length}.")
            return self._create_empty_indicators(df)
        df = df.copy()
        try:
            df['ema_fast'] = ta.trend.ema_indicator(df['close'], window=9, fillna=True)
            df['ema_slow'] = ta.trend.ema_indicator(df['close'], window=50, fillna=True)
            df['rsi'] = ta.momentum.rsi(df['close'], window=self.rsi_length, fillna=True)
            macd = ta.trend.MACD(df['close'], window_slow=26, window_fast=12, window_sign=9, fillna=True)
            df['macd'] = macd.macd(); df['macd_signal'] = macd.macd_signal(); df['macd_hist'] = macd.macd_diff()
            df['volume_ma'] = df['volume'].rolling(window=20, min_periods=1).mean()
            df['volume_spike'] = df['volume'] > (df['volume_ma'] * 1.5)
            df['atr'] = ta.volatility.average_true_range(df['high'], df['low'], df['close'], window=14, fillna=True)
            df['adx'] = ta.trend.adx(df['high'], df['low'], df['close'], window=14, fillna=True)
            df['hlc3'] = (df['high'] + df['low'] + df['close']) / 3
            esa = ta.trend.ema_indicator(df['hlc3'], window=self.tci_length, fillna=True)
            de = ta.trend.ema_indicator(abs(df['hlc3'] - esa), window=self.tci_length, fillna=True)
            ci = (df['hlc3'] - esa) / (0.015 * de + 1e-9)
            df['tci'] = ta.trend.ema_indicator(ci, window=self.tci_smooth_length, fillna=True)
            df['wt2'] = ta.trend.sma_indicator(df['tci'], window=4, fillna=True)
            stoch_rsi = ta.momentum.StochRSIIndicator(df['rsi'], self.stoch_length, self.stoch_smooth_k, self.stoch_smooth_d, fillna=True)
            df['stoch_rsi_k'] = stoch_rsi.stochrsi_k(); df['stoch_rsi_d'] = stoch_rsi.stochrsi_d()
            df['mfi'] = ta.volume.money_flow_index(df['high'], df['low'], df['close'], df['volume'], window=self.mfi_length, fillna=True)
            df = self.calculate_supertrend(df, self.supertrend_atr_length, self.supertrend_multiplier)

            # Call find_divergences here - Ensure it's called correctly
            df = self.find_divergences(df, lookback=5) # This is the line that caused the error

            df = self.calculate_fibonacci_levels(df, timeframe)
            df = df.drop(columns=['hlc3'], errors='ignore')
            return df
        except Exception as e:
            # Log the error with full traceback
            logging.error(f"({symbol_name}) Indicator calc error on {timeframe}: {type(e).__name__} - {e}", exc_info=True)
            return self._create_empty_indicators(df)

    def _create_empty_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        df = df.copy()
        defaults = {
            'ema_fast': np.nan, 'ema_slow': np.nan, 'rsi': np.nan, 'macd': np.nan, 'macd_signal': np.nan, 'macd_hist': np.nan,
            'volume_ma': np.nan, 'volume_spike': False, 'atr': np.nan, 'adx': np.nan, 'tci': np.nan, 'wt2': np.nan,
            'stoch_rsi_k': np.nan, 'stoch_rsi_d': np.nan, 'mfi': np.nan, 'supertrend': np.nan, 'supertrend_signal': 0,
            'bullish_div_rsi': False, 'bearish_div_rsi': False, 'bullish_div_stoch': False, 'bearish_div_stoch': False,
            'bullish_div_wt1': False, 'bearish_div_wt1': False, 'bullish_div_wt2': False, 'bearish_div_wt2': False,
            'fib_0': np.nan, 'fib_236': np.nan, 'fib_382': np.nan, 'fib_618': np.nan, 'fib_1': np.nan,
            'fib_1618': np.nan, 'fib_2': np.nan, 'fib_neg618': np.nan,
        }
        for col, default in defaults.items():
            if col not in df.columns: df[col] = default
        return df

    # --- UPDATED Trade Plan Calculation with DEBUG logging ---
    def calculate_trade_plan(self, df: pd.DataFrame, signal: str, timeframe: str) -> Optional[Dict[str, Any]]:
        if signal == 'HOLD': return None
        last = df.iloc[-1]; symbol_name = df.name if hasattr(df,'name') else 'N/A'
        logging.debug(f"--- Calculating Trade Plan for {symbol_name} {timeframe} {signal} ---")

        atr = last['atr']; entry_price_for_atr = last['close']
        if pd.isna(entry_price_for_atr) or entry_price_for_atr <= 0:
             logging.warning(f"({symbol_name}) Invalid last close price: {entry_price_for_atr}. Cannot calculate trade plan.")
             logging.debug(f"--- End Trade Plan Calculation for {symbol_name} (FAILED - Invalid Close) ---")
             return None

        min_atr_val = entry_price_for_atr * 0.005
        valid_atr = max(atr, min_atr_val) if not pd.isna(atr) and atr > 0 else min_atr_val
        valid_atr = max(valid_atr, 1e-9)
        logging.debug(f"({symbol_name}) Last Close: {entry_price_for_atr:.4f}, Raw ATR: {atr if not pd.isna(atr) else 'NaN':.4f}, Valid ATR: {valid_atr:.4f}")

        fib_levels = {k: last.get(k, np.nan) for k in ['fib_0', 'fib_236', 'fib_382', 'fib_618', 'fib_1', 'fib_1618', 'fib_2', 'fib_neg618']}
        fibs_available = not any(pd.isna(v) for v in fib_levels.values())
        logging.debug(f"({symbol_name}) Fibs Available: {fibs_available}")
        if fibs_available:
             fib_neg618_val = fib_levels.get('fib_neg618')
             fib_neg618_str = f"{fib_neg618_val:.4f}" if not pd.isna(fib_neg618_val) else 'NaN'
             logging.debug(f"({symbol_name}) Fib Levels: 0%={fib_levels['fib_0']:.4f}, 38.2%={fib_levels['fib_382']:.4f}, "
                           f"61.8%={fib_levels['fib_618']:.4f}, 100%={fib_levels['fib_1']:.4f}, "
                           f"161.8%={fib_levels['fib_1618']:.4f}, -61.8%={fib_neg618_str}")

        entry_zone = (np.nan, np.nan); stop_loss, take_profit = np.nan, np.nan
        method = "N/A"; sl_buffer_atr_fraction = 0.25

        if fibs_available:
            try:
                if signal == 'BUY':
                    zone = (fib_levels['fib_382'], fib_levels['fib_618'])
                    sl = fib_levels['fib_0'] - valid_atr * sl_buffer_atr_fraction
                    tp = fib_levels['fib_1618']
                elif signal == 'SELL':
                    zone = (fib_levels['fib_618'], fib_levels['fib_382'])
                    sl = fib_levels['fib_1'] + valid_atr * sl_buffer_atr_fraction
                    tp_val = fib_levels.get('fib_neg618')
                    tp = tp_val if not pd.isna(tp_val) else fib_levels['fib_0'] - valid_atr * 3 # Fallback TP if neg618 is NaN
                else:
                     logging.debug(f"--- End Trade Plan Calculation for {symbol_name} (FAILED - Invalid Signal '{signal}') ---")
                     return None

                logging.debug(f"({symbol_name}) Attempting Fib Plan. Signal: {signal}, Zone Tuple: {zone}, SL: {sl:.4f}, TP: {tp:.4f}")
                entry_zone = (min(zone), max(zone)); stop_loss = sl; take_profit = tp

                if pd.isna(stop_loss) or pd.isna(take_profit) or pd.isna(entry_zone[0]) or pd.isna(entry_zone[1]) or \
                   (signal == 'BUY' and stop_loss >= take_profit) or \
                   (signal == 'SELL' and stop_loss <= take_profit) or \
                   entry_zone[0] < 0 or stop_loss < 0:
                    logging.warning(f"({symbol_name}) Invalid Fib trade plan values. Falling back. "
                                    f"Zone:{entry_zone}, SL:{stop_loss:.4f}, TP:{take_profit:.4f}")
                    fibs_available = False
                else:
                    method = 'Fibonacci Zone'
                    logging.debug(f"({symbol_name}) Fib plan validated. Method: {method}")
            except Exception as e:
                logging.error(f"({symbol_name}) Error in Fib plan calculation block: {e}. Falling back.")
                fibs_available = False

        if not fibs_available:
            method = 'ATR Fallback'; logging.debug(f"({symbol_name}) Using {method}.")
            atr_mult = self.atr_multipliers.get(timeframe, self.atr_multipliers['1h'])
            sl_multiplier = atr_mult['sl']; tp_multiplier = atr_mult['tp']
            entry = entry_price_for_atr; entry_zone = (entry - valid_atr * 0.5, entry + valid_atr * 0.5)

            if signal == 'BUY': stop_loss = entry - valid_atr * sl_multiplier; take_profit = entry + valid_atr * tp_multiplier
            elif signal == 'SELL': stop_loss = entry + valid_atr * sl_multiplier; take_profit = entry - valid_atr * tp_multiplier
            entry_zone = (min(entry_zone), max(entry_zone))

            if pd.isna(stop_loss) or pd.isna(take_profit) or pd.isna(entry_zone[0]) or pd.isna(entry_zone[1]) or \
               (signal == 'BUY' and stop_loss >= take_profit) or \
               (signal == 'SELL' and stop_loss <= take_profit) or \
                entry_zone[0] < 0 or stop_loss < 0:
                logging.error(f"({symbol_name}) Failed to calculate valid ATR fallback plan. Zone:{entry_zone}, SL:{stop_loss:.4f}, TP:{take_profit:.4f}")
                logging.debug(f"--- End Trade Plan Calculation for {symbol_name} (FAILED - ATR Fallback Invalid) ---")
                return None

        logging.debug(f"({symbol_name}) Final Plan Output: Method={method}, Zone={entry_zone}, SL={stop_loss:.4f}, TP={take_profit:.4f}")
        logging.debug(f"--- End Trade Plan Calculation for {symbol_name} (Success) ---")
        return {'entry_zone': entry_zone, 'stop_loss': stop_loss, 'take_profit': take_profit, 'method': method}

    # --- UPDATED Signal Generation with DEBUG logging & TEST ---
    def generate_signal(self, df: pd.DataFrame, timeframe: str) -> Tuple[str, float, Optional[Dict[str, Any]]]:
        required_length = 50; symbol_name = df.name if hasattr(df,'name') else 'N/A'
        if df.empty or len(df) < required_length: return 'HOLD', 0.0, None
        if 'stoch_rsi_k' not in df.columns or 'supertrend_signal' not in df.columns or 'fib_0' not in df.columns:
            df = self.calculate_indicators(df, timeframe) # Ensure indicators, including fibs, are calculated
        essential_cols = ['close', 'ema_fast', 'ema_slow', 'rsi', 'macd_hist', 'atr', 'tci', 'wt2', 'stoch_rsi_k', 'supertrend_signal', 'fib_0']
        last = df.iloc[-1]
        if last[essential_cols].isnull().any():
             nan_indicators = last[essential_cols][last[essential_cols].isnull()].index.tolist()
             logging.warning(f"({symbol_name}) Signal gen skipped due to NaN in essentials: {nan_indicators}")
             return 'HOLD', 0.0, None

        try:
            prev = df.iloc[-2] if len(df) > 1 else last
            ema_fast=last['ema_fast']; ema_slow=last['ema_slow']; trend_up=ema_fast>ema_slow
            macd_hist=last['macd_hist']; momentum_up=macd_hist>0
            tci=last['tci']; wt2=last['wt2']; wave_cross_bull=tci>wt2 and prev['tci']<=prev['wt2']; wave_cross_bear=tci<wt2 and prev['tci']>=prev['wt2']
            wt_oversold=tci<self.oversold_level; wt_overbought=tci>self.overbought_level
            stoch_k=last['stoch_rsi_k']; stoch_d=last['stoch_rsi_d']; stoch_cross_bull=not pd.isna(stoch_d) and stoch_k>stoch_d and prev['stoch_rsi_k']<=prev['stoch_rsi_d']; stoch_cross_bear=not pd.isna(stoch_d) and stoch_k<stoch_d and prev['stoch_rsi_k']>=prev['stoch_rsi_d']
            stoch_oversold=stoch_k<20; stoch_overbought=stoch_k>80
            rsi=last['rsi']; rsi_oversold=rsi<35; rsi_overbought=rsi>65
            volume_spike=last['volume_spike']; atr=last['atr']
            adx=last['adx'] if not pd.isna(last['adx']) else 0; is_trending=adx>20
            st_signal=last['supertrend_signal']; st_up=st_signal==1; st_down=st_signal==-1
            any_bull_div=last.get('bullish_div_rsi',False) or last.get('bullish_div_stoch',False) or last.get('bullish_div_wt1',False) or last.get('bullish_div_wt2',False)
            any_bear_div=last.get('bearish_div_rsi',False) or last.get('bearish_div_stoch',False) or last.get('bearish_div_wt1',False) or last.get('bearish_div_wt2',False)

            buy_score=0; sell_score=0; signal_threshold=3.5; max_possible_score=9.0
            if trend_up and momentum_up and st_up: buy_score+=3.5
            elif not trend_up and not momentum_up and st_down: sell_score+=3.5
            else:
                if st_up: buy_score+=1.5
                if st_down: sell_score+=1.5
                if trend_up and momentum_up: buy_score+=1.0
                if not trend_up and not momentum_up: sell_score+=1.0
            if is_trending:
                if trend_up or st_up: buy_score+=0.5
                if not trend_up or st_down: sell_score+=0.5
            if wave_cross_bull and wt_oversold: buy_score+=1.5
            elif wave_cross_bull: buy_score+=0.5
            if wave_cross_bear and wt_overbought: sell_score+=1.5
            elif wave_cross_bear: sell_score+=0.5
            if stoch_cross_bull and stoch_oversold: buy_score+=1.0
            elif stoch_cross_bull: buy_score+=0.3
            if stoch_cross_bear and stoch_overbought: sell_score+=1.0
            elif stoch_cross_bear: sell_score+=0.3
            if any_bull_div: buy_score+=1.0
            if any_bear_div: sell_score+=1.0

            initial_signal = 'HOLD'; raw_confidence = 0
            if buy_score >= signal_threshold and buy_score > sell_score: initial_signal = 'BUY'; raw_confidence = buy_score
            elif sell_score >= signal_threshold and sell_score > buy_score: initial_signal = 'SELL'; raw_confidence = sell_score
            else: raw_confidence = max(buy_score, sell_score)

            # <<< ADDED LOGGING HERE >>>
            logging.debug(f"({symbol_name}) Post-Score Check | "
                          f"Initial Signal: {initial_signal} | "
                          f"Score B/S: {buy_score:.2f}/{sell_score:.2f} vs Threshold: {signal_threshold}")
            # <<< END ADDED LOGGING >>>

            trade_plan = None; zone_note = ""; final_signal = initial_signal
            # <<< TEST: Force penalty to zero >>>
            outside_zone_penalty = 0

            if final_signal != 'HOLD':
                # <<< ADDED DEBUG >>>
                logging.debug(f"({symbol_name}) Initial signal is {final_signal}. Attempting trade plan calculation...")
                trade_plan = self.calculate_trade_plan(df, final_signal, timeframe)

                # <<< MODIFIED Check >>>
                if trade_plan:
                    logging.debug(f"({symbol_name}) Trade plan calculation SUCCEEDED. Method: {trade_plan.get('method', 'N/A')}")
                    last_close = last['close']
                    entry_lower, entry_upper = trade_plan.get('entry_zone', (np.nan, np.nan))

                    if not pd.isna(last_close) and not pd.isna(entry_lower) and not pd.isna(entry_upper):
                        is_outside = (final_signal == 'BUY' and last_close > entry_upper) or \
                                     (final_signal == 'SELL' and last_close < entry_lower)
                        if is_outside:
                             # Penalty currently 0 for testing
                             zone_note = "(Pullback Suggested)"
                             if 'note' not in trade_plan: trade_plan['note'] = zone_note
                             logging.info(f"{symbol_name} {timeframe}: {final_signal} signal, but price ({last_close:.4f}) " # INFO Log
                                          f"is outside entry zone ({entry_lower:.4f}-{entry_upper:.4f}). Penalty currently disabled for test.")
                        else:
                             logging.debug(f"({symbol_name}) Price ({last_close:.4f}) is INSIDE entry zone ({entry_lower:.4f}-{entry_upper:.4f}).") # Added
                    else:
                        logging.warning(f"({symbol_name}) Cannot validate price vs zone due to NaN values.")

                else: # Trade plan calculation failed
                    # <<< THIS IS LIKELY WHERE THE SIGNAL IS REVERTED >>>
                    logging.warning(f"{symbol_name} {timeframe}: Trade plan calculation FAILED for {final_signal} signal. Reverting to HOLD.") # Warning Log
                    final_signal = 'HOLD'

            confidence = min(100, max(0, (raw_confidence / max_possible_score) * 100))
            confidence = max(0, confidence - outside_zone_penalty) # Apply penalty (0 for test)
            if final_signal == 'HOLD': confidence = min(confidence, 40)

            # <<< Final Check Log >>>
            logging.debug(f"{symbol_name} {timeframe}: Final Check | "
                          f"Initial Signal: {initial_signal}, Final Signal: {final_signal} | "
                          f"Raw Score: {raw_confidence:.2f}/{max_possible_score:.1f} | "
                          f"Final Confidence: {confidence:.1f}% | "
                          f"Trade Plan Exists: {trade_plan is not None} | "
                          f"Zone Note: '{zone_note}'")

            return final_signal, confidence, trade_plan

        except Exception as e:
            logging.error(f"({symbol_name}) Signal generation error on {timeframe}: {type(e).__name__} - {e}", exc_info=True) # Log full traceback
            return 'HOLD', 0.0, None

    # --- Helper Methods ---
    def check_current_divergence(self, df: pd.DataFrame) -> Tuple[bool, bool]:
        # This is a simplified check and less reliable than the main divergence logic
        if len(df) < 2: return False, False
        last=df.iloc[-1]; prev=df.iloc[-2]
        req=['low','high','rsi','stoch_rsi_k','tci','wt2']
        if last[req].isnull().any() or prev[req].isnull().any(): return False, False
        try:
             bull=(last['low']<prev['low']) and ((last['rsi']>prev['rsi']) or (last['stoch_rsi_k']>prev['stoch_rsi_k']) or (last['tci']>prev['tci']) or (last['wt2']>prev['wt2']))
             bear=(last['high']>prev['high']) and ((last['rsi']<prev['rsi']) or (last['stoch_rsi_k']<prev['stoch_rsi_k']) or (last['tci']<prev['tci']) or (last['wt2']<prev['wt2']))
             return bull, bear
        except Exception as e: logging.error(f"Error in check_current_divergence: {e}", exc_info=False); return False, False

    def analyze_higher_timeframe(self, symbol: str, timeframe: str) -> str:
        higher_tf = self.higher_timeframes.get(timeframe)
        if not higher_tf: return 'N/A'
        df_htf = self.fetch_data(symbol, higher_tf, limit=60)
        if df_htf.empty or len(df_htf) < 50: return 'N/A'
        df_htf = self.calculate_indicators(df_htf, higher_tf)
        if df_htf.empty or not all(c in df_htf.columns for c in ['ema_fast','ema_slow','supertrend_signal']) or \
           df_htf[['ema_fast','ema_slow','supertrend_signal']].iloc[-1].isnull().any(): return 'N/A'
        last=df_htf.iloc[-1]; htf_ema_up=last['ema_fast']>last['ema_slow']; htf_st_up=last['supertrend_signal']==1
        if htf_ema_up and htf_st_up: return '🔼 Up'
        if not htf_ema_up and not htf_st_up: return '🔽 Down'
        if htf_ema_up and not htf_st_up: return ' Konfl (EMA Up / ST Down)'
        if not htf_ema_up and htf_st_up: return ' Konfl (EMA Down / ST Up)'
        return '❓ Unclear'

    def determine_market_type(self, df: pd.DataFrame) -> str:
        if len(df)<20 or not all(c in df.columns for c in ['adx','ema_fast','ema_slow']): return 'Uncertain'
        last=df.iloc[-1]; adx=last['adx'] if not pd.isna(last['adx']) else 0
        ema_fast=last['ema_fast']; ema_slow=last['ema_slow']
        if pd.isna(ema_fast) or pd.isna(ema_slow): return 'Uncertain'
        if adx>25: return '📈 Trend Up' if ema_fast>ema_slow else '📉 Trend Down'
        elif adx<20: return '횡보 Range'
        else: return '⏳ Developing'

# --- END OF AdvancedTradingBot CLASS ---
# --- GUI with Dear PyGui ---
class AdvancedTradingGUI:
    def __init__(self, bot: AdvancedTradingBot):
        self.bot = bot
        logging.info("AdvancedTradingGUI init started.")
        self.gui_queue = queue.Queue() # Queue for thread-safe GUI updates
        self.custom_symbols: List[str] = []
        self.live_running = False
        self.all_live_results: Dict[str, Dict[str, Any]] = {}
        self.live_scan = False
        self.live_scan_thread = None
        self.analysis_thread = None
        self.stop_event = threading.Event()
        # --- REPLACE CREDENTIALS ---
        self.TELEGRAM_TOKEN = "**********************************************"
        self.TELEGRAM_CHAT_ID = "650827211"
        # --- END CREDENTIALS ---
        self.theme = {
            'background': (30,30,30,255), 'foreground': (232,232,232,255), 'accent': (52,152,219,255),
            'success': (46,204,113,255), 'error': (231,76,60,255), 'warning': (241,196,15,255),
            'tree_header': (45,45,45,255), 'tree_row': (37,37,37,255)
        }
        self.timeframe = '1h'
        self.filter_vars = {
            'BUY': False, 'SELL': False, 'CONF>60': False, 'VOL_SPIKE': False, 'HTF_ALIGN': False,
            'SENT_CONF': False, 'WAVE_X': False, 'DIV_CONF': False, 'ST_ALIGN': False
        }
        self.currently_displayed_symbols: List[str] = []
        self.columns_config = [ # Updated Entry Zone label/width
            ('Symbol', 140), ('Signal', 80), ('Conf.', 70), ('Price', 110), ('Entry Zone', 180),
            ('Stop Loss', 110), ('Take Profit', 110), ('Chg.', 70), ('Trend', 80), ('STrend', 80),
            ('Momentum', 80), ('Volume', 100), ('Div.', 70), ('Market', 100), ('HTF', 90),
            ('Funding', 110), ('OI Trend', 90), ('Sent.', 70), ('Wave', 80), ('Action', 80)
        ]
        self.load_symbols()
        # Log file name updated
        # configure_logging("trading_bot_fibzone_debug.log") # Ensure logging is configured elsewhere
        self.setup_gui()

    # --- GUI Setup, Symbol List Mgmt, Analysis Control (No changes from previous full code) ---
    def setup_gui(self):
        logging.info("Setting up GUI...")
        dpg.create_context()
        dpg.create_viewport(title="⚡ Quantum Trading Suite Pro - FibZone Scan v1.4 DEBUG", width=1870, height=700, resizable=True)
        dpg.setup_dearpygui()
        
        try: # Font loading
            with dpg.font_registry(): default_font = dpg.add_font("C:/Windows/Fonts/Arial.ttf", 14)
            dpg.bind_font(default_font); logging.info("Arial font bound.")
        except Exception as e: logging.error(f"Failed to load font: {e}.")
        with dpg.theme() as global_theme: # Theme setup
            with dpg.theme_component(dpg.mvAll):
                dpg.add_theme_color(dpg.mvThemeCol_WindowBg, self.theme['background']); dpg.add_theme_color(dpg.mvThemeCol_ChildBg, self.theme['tree_row'])
                dpg.add_theme_color(dpg.mvThemeCol_FrameBg, (45,45,45,255)); dpg.add_theme_color(dpg.mvThemeCol_Button, self.theme['accent'])
                dpg.add_theme_color(dpg.mvThemeCol_Header, self.theme['tree_header']); dpg.add_theme_color(dpg.mvThemeCol_HeaderHovered, self.theme['accent'])
                dpg.add_theme_color(dpg.mvThemeCol_HeaderActive, self.theme['accent']); dpg.add_theme_color(dpg.mvThemeCol_Text, self.theme['foreground'])
                dpg.add_theme_style(dpg.mvStyleVar_FramePadding, 6, 4); dpg.add_theme_style(dpg.mvStyleVar_CellPadding, 4, 2); dpg.add_theme_style(dpg.mvStyleVar_ItemSpacing, 8, 6)
        dpg.bind_theme(global_theme)
        with dpg.window(tag="main_window", no_close=True, no_title_bar=True): # Main Window Layout
            with dpg.group(horizontal=True): # Top Bar
                default_sym = ",".join(self.custom_symbols) if self.custom_symbols else "BTCUSDT,ETHUSDT"
                dpg.add_text("GUI Loaded")
                dpg.add_input_text(tag="symbol_input", default_value=default_sym, hint="Symbols (comma-separated)", width=400)
                dpg.add_button(label="Load List", callback=self.add_symbols, width=80); dpg.add_button(label="Clear List", callback=self.clear_symbols, width=80)
                dpg.add_spacer(width=15); dpg.add_button(label="Refresh", tag="refresh_btn", callback=self.refresh_live_analysis, width=80)
                dpg.add_combo(['5m', '15m', '1h', '4h', '1d', '1w'], default_value=self.timeframe, callback=self.set_timeframe, width=70)
                dpg.add_spacer(width=15); dpg.add_button(label="Start Scan", tag="scan_btn", callback=self.toggle_live_analysis, width=100)
                dpg.add_spacer(width=15); dpg.add_checkbox(label="Auto", tag="auto_refresh_cb", default_value=self.live_scan, callback=self.toggle_auto_refresh)
                dpg.add_spacer(width=15); dpg.add_input_int(tag="refresh_interval", default_value=60, min_value=10, max_value=3600, width=100, step=10, label=" Sec")
                dpg.add_spacer(width=30); dpg.add_text("Status: Ready", tag="status_text")
            dpg.add_spacer(height=10)
            with dpg.group(horizontal=True): # Filters Bar
                dpg.add_text("Filters:"); dpg.add_spacer(width=10)
                keys_labels=[('BUY','BUY'),('SELL','SELL'),('CONF>60','Conf>60'),('VOL_SPIKE','VolSpk'),('HTF_ALIGN','HTF'),('SENT_CONF','Sent'),('WAVE_X','WaveX'),('DIV_CONF','Div'),('ST_ALIGN','ST Align')]
                for key, label in keys_labels:
                    if key in self.filter_vars: dpg.add_checkbox(label=label, callback=self.apply_live_filters_gui, user_data=key, tag=f'filter_{key}'); dpg.add_spacer(width=15)
            dpg.add_spacer(height=10)
            with dpg.table(tag="live_table", header_row=True, resizable=True, policy=dpg.mvTable_SizingStretchProp, # Results Table
                           row_background=True, borders_outerH=True, borders_innerV=True, borders_innerH=True, borders_outerV=True,
                           height=-1, width=-1, scrollY=True): pass # Columns added by _clear_table
        dpg.set_primary_window("main_window", True); self._clear_table(); dpg.show_viewport()

    def set_timeframe(self, sender, app_data):
        self.timeframe = app_data; logging.info(f"Timeframe changed to: {self.timeframe}")
        self.all_live_results.clear(); self.currently_displayed_symbols.clear()
        self._clear_table();
        if dpg.does_item_exist("status_text"): dpg.set_value("status_text", f"Status: Timeframe set to {self.timeframe}. Press Refresh.")
        
    def _schedule_apply_filters(self):
            """Helper function run via queue to schedule apply_live_filters on the next frame."""
            if dpg.is_dearpygui_running():
                current_frame = dpg.get_frame_count()
                if current_frame >= 0:
                    logging.debug(f"Scheduling apply_live_filters for frame {current_frame + 1}")
                    dpg.set_frame_callback(current_frame + 1, callback=self.apply_live_filters)
                else:
                     logging.error("Cannot schedule apply_live_filters: Invalid frame count.")
            else:
                 logging.warning("_schedule_apply_filters called but DPG not running.")
                 
    def _clear_table(self):
        if dpg.does_item_exist("live_table"):
            dpg.delete_item("live_table", children_only=True)
            for col_label, width_weight in self.columns_config:
                dpg.add_table_column(label=col_label, width_fixed=False, init_width_or_weight=float(width_weight), parent="live_table")
        else: logging.error("Cannot clear table: 'live_table' does not exist.")

    def apply_live_filters_gui(self, sender, app_data, user_data):
         if user_data in self.filter_vars: self.filter_vars[user_data] = app_data; logging.debug(f"Filter '{user_data}' set to {app_data}"); self.apply_live_filters()

    # --- UPDATED apply_live_filters ---
    def apply_live_filters(self):
        """Filters and redraws the table based on self.all_live_results and self.filter_vars."""
        logging.debug("apply_live_filters CALLED") # Add log to see when it runs
        if not dpg.is_dearpygui_running() or not dpg.does_item_exist("live_table"):
            logging.warning("apply_live_filters called but GUI/Table not ready.")
            return

        self._clear_table() # Clear existing rows and recreate headers/columns
        displayed_symbols_this_run = []

        # Filter out None values before sorting
        valid_results = [res for res in self.all_live_results.values() if isinstance(res, dict)]
        sorted_results = sorted(
            valid_results,
            key=lambda item: (-item.get('confidence', 0), item.get('symbol', ''))
        )
        logging.debug(f"apply_live_filters: Found {len(valid_results)} valid results, {len(sorted_results)} after sorting.") # Log count

        active_filters = {k for k, v in self.filter_vars.items() if v}

        if not sorted_results:
             logging.debug("apply_live_filters: No results to display.")

        for item in sorted_results:
            # This check is slightly redundant due to filtering above, but very safe
            if not isinstance(item, dict):
                 logging.warning(f"Skipping invalid item type in apply_live_filters loop: {type(item)}")
                 continue

            # --- Filter gathering and application logic (as before) ---
            signal=item.get('signal','HOLD'); conf=item.get('confidence',0.0); vol_spike=item.get('volume_spike',False)
            higher_signal=item.get('higher_signal','N/A'); st_align=item.get('st_align',False); sent_confirm=item.get('sentiment_confirm',False)
            wave_cross=item.get('wave_cross',False); div_confirm=item.get('div_confirm',False)
            htf_align=False
            if signal != 'HOLD' and ' ' in higher_signal:
                htf_dir = higher_signal.split()[1]
                if (signal=='BUY' and htf_dir=='Up') or (signal=='SELL' and htf_dir=='Down'): htf_align=True
            passes = all(((not self.filter_vars['BUY'] or signal=='BUY'), (not self.filter_vars['SELL'] or signal=='SELL'),
                          (not self.filter_vars['CONF>60'] or conf>=60), (not self.filter_vars['VOL_SPIKE'] or vol_spike),
                          (not self.filter_vars['HTF_ALIGN'] or htf_align), (not self.filter_vars['SENT_CONF'] or sent_confirm),
                          (not self.filter_vars['WAVE_X'] or wave_cross), (not self.filter_vars['DIV_CONF'] or div_confirm),
                          (not self.filter_vars['ST_ALIGN'] or st_align)))
            if not passes: continue
            # --- End Filter Logic ---

            symbol=item.get('symbol','N/A'); displayed_symbols_this_run.append(symbol)
            values_tuple=item.get('values',())
            logging.debug(f"Adding row for {symbol}") # Log row addition

            # Add row with appropriate cell coloring/widgets
            with dpg.table_row(parent="live_table"):
                # ... (cell population logic as before) ...
                 if len(values_tuple) != len(self.columns_config):
                     logging.error(f"Data mismatch for {symbol}: Cols {len(self.columns_config)}, Vals {len(values_tuple)}.")
                     with dpg.table_cell(): dpg.add_text(f"Data Error for {symbol}", color=self.theme['error'], wrap=0)
                     for _ in range(len(self.columns_config)-1):
                         with dpg.table_cell(): dpg.add_text("ERR",color=self.theme['error'])
                     continue
                 for i, value in enumerate(values_tuple):
                    col_label, _ = self.columns_config[i]; cell_value = str(value)
                    color = self.theme['foreground']; widget_type = 'text'
                    # Customize based on column label
                    if col_label=="Action": widget_type='button'
                    elif col_label=="Signal": color = self.theme['success'] if cell_value=="BUY" else (self.theme['error'] if cell_value=="SELL" else color)
                    elif col_label=="Conf.":
                        conf_val = item.get('confidence',0.0)
                        if conf_val >= 80: color = self.theme['success']
                        elif conf_val >= 60: color = self.theme['warning']
                        trade_plan_data = item.get('trade_plan')
                        if isinstance(trade_plan_data, dict) and trade_plan_data.get('note'): color = (255, 165, 0, 255)
                    elif col_label=="Chg.":
                         try: change_val = float(cell_value.replace('%','')); color = self.theme['success'] if change_val>0.1 else (self.theme['error'] if change_val<-0.1 else color)
                         except: pass
                    elif col_label=="STrend": color = self.theme['success'] if "Up" in cell_value else (self.theme['error'] if "Down" in cell_value else color)
                    elif col_label=="HTF": color = self.theme['warning'] if "Konfl" in cell_value else (self.theme['success'] if "Up" in cell_value else (self.theme['error'] if "Down" in cell_value else color))
                    # Add widget
                    with dpg.table_cell():
                        if widget_type == 'button': dpg.add_button(label=cell_value, callback=self.on_send_button_click, user_data=symbol, width=-1)
                        else: dpg.add_text(cell_value, color=color, wrap=0)

        self.currently_displayed_symbols = displayed_symbols_this_run
        filter_count = len(active_filters); status_msg = f"Status: Displaying {len(self.currently_displayed_symbols)} assets"
        if filter_count > 0: status_msg += f" ({filter_count} filter{'s' if filter_count > 1 else ''} active)"
        if dpg.does_item_exist("status_text"): dpg.set_value("status_text", status_msg)

        logging.debug("apply_live_filters FINISHED") # Add log to see when it finishes

    def add_symbols(self):
        sym_str = dpg.get_value("symbol_input").strip()
        if not sym_str: dpg.set_value("status_text", "Status: Input empty."); return
        new_syms = sorted(list(set(s.strip().upper() for s in sym_str.split(',') if s.strip() and re.match(r'^[A-Z0-9]+USDT$', s.strip().upper()))))
        if not new_syms: dpg.set_value("status_text", "Status: No valid symbols (e.g., BTCUSDT)."); return
        self.custom_symbols = new_syms; self.save_symbols()
        dpg.set_value("status_text", f"Status: Loaded {len(self.custom_symbols)} symbols.")
        self.all_live_results.clear(); self.currently_displayed_symbols.clear(); self._clear_table()

    def clear_symbols(self):
        self.custom_symbols = []; self.save_symbols(); dpg.set_value("symbol_input", "")
        dpg.set_value("status_text", "Status: Watchlist cleared.")
        self.all_live_results.clear(); self.currently_displayed_symbols.clear(); self._clear_table()

    def save_symbols(self):
        try:
            with Path('watchlist.json').open('w', encoding='utf-8') as f: json.dump(self.custom_symbols, f, indent=2)
        except Exception as e: logging.error(f"Error saving watchlist: {e}")

    def load_symbols(self):
        path = Path('watchlist.json'); defaults = ['BTCUSDT', 'ETHUSDT']
        if path.exists():
            try:
                with path.open('r', encoding='utf-8') as f: loaded = json.load(f)
                if isinstance(loaded, list):
                    self.custom_symbols = sorted(list(set(s.strip().upper() for s in loaded if isinstance(s, str) and s.strip())))
                    logging.info(f"Loaded {len(self.custom_symbols)} symbols from watchlist.json"); return
                else: logging.warning("watchlist.json not a valid list.")
            except Exception as e: logging.error(f"Error loading watchlist: {e}. Using defaults.")
        else: logging.info("watchlist.json not found. Using defaults.")
        self.custom_symbols = defaults; self.save_symbols()

    def toggle_live_analysis(self, sender=None, app_data=None):
        if not self.live_running:
            self.live_running = True
            self.stop_event.clear()
            dpg.configure_item("scan_btn", label="Stop Scan")
            dpg.configure_item("refresh_btn", enabled=False)
            self.bot.cache.clear()
            logging.info("Starting full manual analysis...")
            self.analysis_thread = threading.Thread(target=self.run_live_analysis, args=(self.custom_symbols,), daemon=True)
            self.analysis_thread.start()
        else:
            logging.info("Requesting manual analysis stop...")
            self.stop_event.set()  # Signal thread to stop
            dpg.configure_item("scan_btn", label="Stopping...", enabled=False)  # Immediate feedback
            
            # Forcefully stop the thread if it’s still alive
            if self.analysis_thread and self.analysis_thread.is_alive():
                try:
                    self.analysis_thread.join(timeout=1.0)  # Wait 1 second for clean exit
                    if self.analysis_thread.is_alive():
                        logging.warning("Thread did not stop gracefully. Forcing termination.")
                        # Python doesn’t natively kill threads, but daemon status ensures it dies when main thread exits
                        # We’ll rely on resetting state instead
                except Exception as e:
                    logging.error(f"Error joining thread: {e}")

            # Reset state immediately (no queue delay)
            self.live_running = False
            dpg.configure_item("scan_btn", label="Start Scan", enabled=True)
            dpg.configure_item("refresh_btn", enabled=True)
            dpg.set_value("status_text", "Status: Stopped (Forced)")
            logging.info("Analysis forcefully stopped and GUI reset.")

    def run_live_analysis(self, symbols_to_analyze: List[str]):
        if not symbols_to_analyze:
            if dpg.is_dearpygui_running() and dpg.does_item_exist("status_text"):
                dpg.set_value("status_text", "Status: No symbols.")
            self.live_running = False  # Reset immediately
            return
        total = len(symbols_to_analyze)
        if dpg.is_dearpygui_running() and dpg.does_item_exist("status_text"):
            dpg.set_value("status_text", f"Status: Starting analysis for {total}...")
        processed = 0
        max_workers = min(10, total)
        try:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = {executor.submit(self.analyze_symbol, symbol): symbol for symbol in symbols_to_analyze}
                for i, future in enumerate(as_completed(futures), 1):
                    if self.stop_event.is_set():
                        logging.info("Analysis cancelled mid-process.")
                        executor.shutdown(wait=False, cancel_futures=True)  # Kill all pending tasks
                        return  # Exit immediately
                    symbol = futures[future]
                    try:
                        result = future.result()
                        self.all_live_results[symbol] = result
                        if result:
                            processed += 1
                    except Exception as e:
                        logging.error(f"Error processing {symbol}: {e}")
                        self.all_live_results[symbol] = None

                    if self.stop_event.is_set():
                        logging.info("Analysis cancelled after result.")
                        executor.shutdown(wait=False, cancel_futures=True)
                        return  # Exit immediately

                    if i % 5 == 0 or i == total:
                        status = f"Status: Analyzing {symbol} ({i}/{total})... {processed} processed."
                        if dpg.is_dearpygui_running() and dpg.does_item_exist("status_text"):
                            dpg.set_value("status_text", status)
        except Exception as e:
            logging.error(f"ThreadPool error: {e}")
        finally:
            if self.stop_event.is_set():
                logging.info("Analysis stopped.")
            else:
                final_status = f"Status: Analysis complete - {processed}/{total} processed."
                logging.info(final_status)
                if dpg.is_dearpygui_running() and dpg.does_item_exist("status_text"):
                    dpg.set_value("status_text", final_status)
            # No queue—rely on toggle_live_analysis to reset GUI

    def toggle_auto_refresh(self, sender, app_data):
        self.live_scan = app_data
        if self.live_scan:
            if self.live_scan_thread is None or not self.live_scan_thread.is_alive():
                self.stop_event.clear()
                self.live_scan_thread = threading.Thread(target=self.auto_refresh_loop, daemon=True)
                self.live_scan_thread.start()
                if dpg.does_item_exist("status_text"):
                    dpg.set_value("status_text", "Status: Auto Refresh ON")
                logging.info("Auto Refresh enabled.")
                if dpg.does_item_exist("refresh_btn"):
                    dpg.configure_item("refresh_btn", enabled=False)
                if dpg.does_item_exist("scan_btn"):
                    dpg.configure_item("scan_btn", enabled=False)
        else:
            logging.info("Requesting Auto Refresh stop...")
            self.stop_event.set()
            if dpg.does_item_exist("status_text"):
                dpg.set_value("status_text", "Status: Stopping Auto Refresh...")

            # Forcefully stop threads
            if self.live_scan_thread and self.live_scan_thread.is_alive():
                try:
                    self.live_scan_thread.join(timeout=1.0)
                    if self.live_scan_thread.is_alive():
                        logging.warning("Auto refresh thread did not stop. Daemon will terminate on exit.")
                except Exception as e:
                    logging.error(f"Error joining live_scan_thread: {e}")
            if self.analysis_thread and self.analysis_thread.is_alive():
                try:
                    self.analysis_thread.join(timeout=1.0)
                    if self.analysis_thread.is_alive():
                        logging.warning("Analysis thread did not stop. Daemon will terminate on exit.")
                except Exception as e:
                    logging.error(f"Error joining analysis_thread: {e}")

            # Reset GUI state immediately
            self.live_running = False
            self.live_scan = False
            if dpg.is_dearpygui_running():
                if dpg.does_item_exist("refresh_btn"):
                    dpg.configure_item("refresh_btn", enabled=True)
                if dpg.does_item_exist("scan_btn"):
                    dpg.configure_item("scan_btn", label="Start Scan", enabled=True)
                if dpg.does_item_exist("auto_refresh_cb"):
                    dpg.set_value("auto_refresh_cb", False)
                if dpg.does_item_exist("status_text"):
                    dpg.set_value("status_text", "Status: Auto Refresh OFF")
            logging.info("Auto Refresh forcefully stopped and GUI reset.")

    def auto_refresh_loop(self):
        logging.info("Auto Refresh loop started.")
        while self.live_scan and not self.stop_event.is_set():
            if not self.live_running:
                self.refresh_live_analysis()
                # Wait for analysis thread to finish
                if self.analysis_thread and self.analysis_thread.is_alive():
                    self.analysis_thread.join()  # Block until scan completes
            interval = dpg.get_value("refresh_interval") if dpg.does_item_exist("refresh_interval") else 60
            logging.info(f"Auto Refresh waiting {interval} seconds for next cycle...")
            stopped = self.stop_event.wait(timeout=max(10, interval))
            if stopped:
                logging.info("Auto Refresh stopped by event.")
                break
        logging.info("Auto Refresh loop finished.")
        if self.stop_event.is_set():
            # Ensure cleanup if stopped
            self.live_running = False
            self.live_scan = False
            if dpg.is_dearpygui_running():
                if dpg.does_item_exist("status_text"):
                    dpg.set_value("status_text", "Status: Auto Refresh OFF")

    def refresh_live_analysis(self):
        if self.live_running:
            logging.warning("Analysis running. Refresh ignored.")
            return
        self.live_running = True
        self.stop_event.clear()
        dpg.configure_item("scan_btn", label="Stop Scan")
        dpg.configure_item("refresh_btn", enabled=False)
        filters_active = any(self.filter_vars.values())
        syms = self.currently_displayed_symbols if filters_active and self.currently_displayed_symbols else self.custom_symbols
        num = len(syms)
        if num == len(self.custom_symbols) or not filters_active:
            logging.info(f"Refreshing all {num} symbols...")
            self.bot.cache.clear()
        else:
            logging.info(f"Refreshing {num} filtered symbols...")
        self.analysis_thread = threading.Thread(target=self.run_live_analysis, args=(syms,), daemon=True)
        self.analysis_thread.start()
        
    def _finalize_auto_refresh_stop(self):
        if self.live_running: self.stop_event.set()
        if dpg.is_dearpygui_running():
            if dpg.does_item_exist("refresh_btn"): dpg.configure_item("refresh_btn", enabled=True)
            if dpg.does_item_exist("scan_btn"): dpg.configure_item("scan_btn", enabled=True, label="Start Scan")
            if dpg.does_item_exist("auto_refresh_cb"): dpg.set_value("auto_refresh_cb", False)
            if dpg.does_item_exist("status_text") and self.live_scan == False: dpg.set_value("status_text", "Status: Auto Refresh OFF")
        self.live_scan = False

    def _finalize_analysis_run(self):
        logging.debug("_finalize_analysis_run CALLED.")
        self.live_running = False
        if dpg.is_dearpygui_running():
            is_auto = dpg.get_value("auto_refresh_cb") if dpg.does_item_exist("auto_refresh_cb") else False
            logging.debug(f"_finalize_analysis_run: Setting button states (is_auto={is_auto}).")
            if dpg.does_item_exist("scan_btn"):
                dpg.configure_item("scan_btn", label="Start Scan", enabled=not is_auto)
            if dpg.does_item_exist("refresh_btn"):
                dpg.configure_item("refresh_btn", enabled=not is_auto)
        else:
            logging.warning("_finalize_analysis_run called but DPG not running.")
        logging.debug("_finalize_analysis_run FINISHED.")

    def _finalize_analysis_run(self):
         self.live_running = False
         if dpg.is_dearpygui_running():
             is_auto = dpg.get_value("auto_refresh_cb") if dpg.does_item_exist("auto_refresh_cb") else False
             if dpg.does_item_exist("scan_btn"): dpg.configure_item("scan_btn", label="Start Scan", enabled=not is_auto)
             if dpg.does_item_exist("refresh_btn"): dpg.configure_item("refresh_btn", enabled=not is_auto)

    # --- UPDATED Symbol Analysis with more robust return handling ---
    def analyze_symbol(self, symbol: str) -> Optional[Dict[str, Any]]:
        result_data = None
        symbol_name_log = symbol if symbol else "N/A"

        try:
            # <<< Check 1: Before anything >>>
            if self.stop_event.is_set(): logging.debug(f"({symbol_name_log}) Stop requested before analysis start."); return None
            timeframe = self.timeframe
            logging.debug(f"({symbol_name_log}) Starting analysis for {timeframe}...")

            # <<< Check 2: Before data fetch >>>
            if self.stop_event.is_set(): logging.debug(f"({symbol_name_log}) Stop requested before fetch_data."); return None
            df = self.bot.fetch_data(symbol, timeframe, limit=70)
            if df.empty:
                logging.debug(f"({symbol_name_log}) Fetch data returned empty. Returning None.")
                return None
            df.name = symbol

            # <<< Check 3: Before indicator calculation >>>
            if self.stop_event.is_set(): logging.debug(f"({symbol_name_log}) Stop requested before calculate_indicators."); return None
            df = self.bot.calculate_indicators(df, timeframe)
            if df.empty:
                 logging.debug(f"({symbol_name_log}) Calculate indicators returned empty. Returning None.")
                 return None

            # <<< Check 4: Before signal generation >>>
            if self.stop_event.is_set(): logging.debug(f"({symbol_name_log}) Stop requested before generate_signal."); return None
            signal, confidence, trade_plan = self.bot.generate_signal(df, timeframe)

            last = df.iloc[-1]
            if pd.isna(last['close']):
                 logging.warning(f"({symbol_name_log}) Last row NaN close. Skip result.")
                 return None
            prev = df.iloc[-2] if len(df) > 1 else last

            # --- Formatting ---
            price=last['close']; decimals=2
            if not pd.isna(price) and price>0: log10_p=np.floor(np.log10(abs(price))); decimals=max(2, min(8, 4-int(log10_p)))
            price_str = f"{price:.{decimals}f}" if not pd.isna(price) else "N/A"
            entry_str, sl_str, tp_str = "N/A", "N/A", "N/A"
            if trade_plan: # Only format if trade_plan exists
                e_low, e_high = trade_plan.get('entry_zone', (np.nan, np.nan))
                if not pd.isna(e_low) and not pd.isna(e_high):
                    entry_str = f"{e_low:.{decimals}f} - {e_high:.{decimals}f}";
                    note = trade_plan.get('note','')
                    entry_str += f" {note}" if note else ""
                else: entry_str = "N/A" # Indicate zone calculation issue if needed
                sl=trade_plan.get('stop_loss',np.nan); tp=trade_plan.get('take_profit',np.nan)
                sl_str = f"{sl:.{decimals}f}" if not pd.isna(sl) else "N/A"; tp_str = f"{tp:.{decimals}f}" if not pd.isna(tp) else "N/A"
            # If signal is HOLD, trade_plan will be None, so entry/sl/tp remain "N/A"

            change_str = "N/A"
            if not pd.isna(last['close']) and not pd.isna(prev['close']) and prev['close'] != 0: change = ((last['close'] - prev['close']) / prev['close']) * 100; change_str = f"{change:+.2f}%"
            trend_icon = '🔼 Up' if not pd.isna(last['ema_fast']) and not pd.isna(last['ema_slow']) and last['ema_fast']>last['ema_slow'] else ('🔽 Down' if not pd.isna(last['ema_fast']) and not pd.isna(last['ema_slow']) else '❓')
            mom_icon = '➕ Pos' if not pd.isna(last['macd_hist']) and last['macd_hist']>0 else ('➖ Neg' if not pd.isna(last['macd_hist']) else '❓')
            vol_icon="N/A"; vol_spike=False
            if not pd.isna(last['volume']):
                vol=last['volume']; vol_icon=f"{vol/1e9:.1f}B" if vol>1e9 else (f"{vol/1e6:.1f}M" if vol>1e6 else (f"{vol/1e3:.1f}K" if vol>1e3 else f"{vol:.0f}")); vol_spike=last.get('volume_spike',False); vol_icon+="💥" if vol_spike else ""
            st_sig=last.get('supertrend_signal',0); st_status='➖ N/A'; st_align=False
            if st_sig==1: st_status='🔼 ST Up'; st_align = signal=='BUY'
            elif st_sig==-1: st_status='🔽 ST Down'; st_align = signal=='SELL'
            div_icon=''; div_confirm=False
            bull=last.get('bullish_div_rsi',False) or last.get('bullish_div_stoch',False) or last.get('bullish_div_wt1',False) or last.get('bullish_div_wt2',False)
            bear=last.get('bearish_div_rsi',False) or last.get('bearish_div_stoch',False) or last.get('bearish_div_wt1',False) or last.get('bearish_div_wt2',False)
            if bull and bear: div_icon='❓ Both'
            elif bull: div_icon='📈 Bull'
            elif bear: div_icon='📉 Bear'
            if (signal=='BUY' and bull) or (signal=='SELL' and bear): div_confirm=True
            market_type = self.bot.determine_market_type(df)
            higher_signal = self.bot.analyze_higher_timeframe(symbol, timeframe)
            if self.stop_event.is_set(): return None
            funding_rate, _ = self.bot.fetch_funding_rate(symbol)
            oi_data = self.bot.fetch_open_interest(symbol, timeframe)
            oi_trend="N/A"; sent_confirm=False
            if len(oi_data)>=2: oi_now, oi_prev = oi_data[1], oi_data[0]; oi_trend = '📈 Inc' if oi_now>oi_prev*1.001 else ('📉 Dec' if oi_now<oi_prev*0.999 else '➖ Stable')
            funding_str = f"{funding_rate*100:.4f}%" if funding_rate is not None else "N/A"
            if funding_rate is not None and not pd.isna(last['close']) and not pd.isna(prev['close']):
                price_up=last['close']>prev['close']; price_down=last['close']<prev['close']
                if signal=="BUY" and price_up and funding_rate<=0.0002 and oi_trend=='📈 Inc': sent_confirm=True
                elif signal=="SELL" and price_down and funding_rate>=-0.0002 and oi_trend=='📉 Dec': sent_confirm=True
            sentiment_icon='✅ Yes' if sent_confirm else '❌ No'
            wave_icon="N/A"; wave_cross=False
            if not pd.isna(last['tci']) and not pd.isna(last['wt2']):
                wt1=last['tci']; wt2=last['wt2']; wave_icon='🔼 Above' if wt1>wt2 else '🔽 Below'
                if not pd.isna(prev['tci']) and not pd.isna(prev['wt2']):
                    if wt1>wt2 and prev['tci']<=prev['wt2']: wave_icon+=' X Up'; wave_cross=(signal=='BUY')
                    elif wt1<wt2 and prev['tci']>=prev['wt2']: wave_icon+=' X Down'; wave_cross=(signal=='SELL')

                if self.stop_event.is_set(): logging.debug(f"({symbol_name_log}) Stop requested before final assembly."); return None

                values_tuple = ( # Assemble tuple
                    symbol, signal, f"{confidence:.1f}%", price_str, entry_str, sl_str, tp_str, change_str, trend_icon, st_status,
                    mom_icon, vol_icon, div_icon, market_type, higher_signal, funding_str, oi_trend, sentiment_icon, wave_icon, "Send"
            )
            if len(values_tuple) != len(self.columns_config): # Validate length
                 logging.critical(f"({symbol_name_log}) FATAL: Value tuple length mismatch. Check analyze_symbol!")
                 expected=len(self.columns_config); current=len(values_tuple)
                 values_tuple = values_tuple + ("ERROR",)*(expected-current) if current<expected else values_tuple[:expected]

            result_data = { # Assign final result
                'symbol': symbol, 'timeframe': timeframe, 'values': values_tuple, 'signal': signal, 'confidence': confidence,
                 'trade_plan': trade_plan, 'higher_signal': higher_signal, 'sentiment_confirm': sent_confirm, 'wave_cross': wave_cross,
                 'volume_spike': vol_spike, 'div_confirm': div_confirm, 'st_align': st_align,
                 'timestamp': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

        except Exception as e:
            logging.error(f"({symbol_name_log}) Error during analyze_symbol for {timeframe}: {type(e).__name__} - {e}", exc_info=True)
            result_data = None

        finally:
             log_signal = result_data['signal'] if isinstance(result_data, dict) else "None"
             # Only log return if stop wasn't the reason for returning None
             if not (result_data is None and self.stop_event.is_set()):
                 logging.debug(f"({symbol_name_log}) analyze_symbol returning: Signal={log_signal}")
             return result_data

    # --- Telegram Integration (No changes from previous full code) ---
    def on_send_button_click(self, sender, app_data, user_data):
        if user_data: logging.info(f"Send btn for: {user_data}"); self.send_to_telegram(user_data)

    def send_to_telegram(self, symbol: str):
        result = self.all_live_results.get(symbol)
        # Added check for None result, which might be stored now
        if not isinstance(result, dict): logging.warning(f"No valid result data for {symbol} for Telegram."); return
        if not self.TELEGRAM_TOKEN or not self.TELEGRAM_CHAT_ID: logging.error("Telegram creds missing."); return
        try:
            vals = result.get('values', ())
            if len(vals) != len(self.columns_config): logging.error(f"TG Vals/Cols mismatch for {symbol}"); return
            labels=[cfg[0] for cfg in self.columns_config]
            try:
                sig_i=labels.index('Signal'); conf_i=labels.index('Conf.'); price_i=labels.index('Price'); zone_i=labels.index('Entry Zone')
                sl_i=labels.index('Stop Loss'); tp_i=labels.index('Take Profit'); chg_i=labels.index('Chg.'); trend_i=labels.index('Trend')
                st_i=labels.index('STrend'); mom_i=labels.index('Momentum'); vol_i=labels.index('Volume'); div_i=labels.index('Div.')
                mkt_i=labels.index('Market'); htf_i=labels.index('HTF'); fund_i=labels.index('Funding'); oi_i=labels.index('OI Trend')
                sent_i=labels.index('Sent.'); wave_i=labels.index('Wave')
            except ValueError as e: logging.error(f"TG: Error finding column index: {e}"); return

            sig=html.escape(vals[sig_i]); conf=html.escape(vals[conf_i]); price=html.escape(vals[price_i]); zone=html.escape(vals[zone_i])
            sl=html.escape(vals[sl_i]); tp=html.escape(vals[tp_i]); chg=html.escape(vals[chg_i]); trend=html.escape(vals[trend_i])
            st=html.escape(vals[st_i]); mom=html.escape(vals[mom_i]); vol=html.escape(vals[vol_i]); div=html.escape(vals[div_i])
            market=html.escape(vals[mkt_i]); htf=html.escape(vals[htf_i]); fund=html.escape(vals[fund_i]); oi=html.escape(vals[oi_i])
            sent=html.escape(vals[sent_i]); wave=html.escape(vals[wave_i]); sym_esc=html.escape(symbol); tf_esc=html.escape(self.timeframe)
            emoji = "🟢" if sig=="BUY" else ("🔴" if sig=="SELL" else "⚪")
            plan_method = result.get('trade_plan',{}).get('method','N/A') # Safely get method

            message = ( f"<b>⚡ Signal | {sym_esc} | {tf_esc} ⚡</b>\n\n"
                        f"<b>{emoji} {sig} </b> @ <code>{price}</code>\n"
                        f"<i>Confidence:</i> {conf}\n" f"<i>Change:</i> <code>{chg}</code>\n\n"
                        f"<b><u>Trade Plan ({plan_method}):</u></b>\n"
                        f"  Entry Zone: <code>{zone}</code>\n" f"  SL: <code>{sl}</code>\n" f"  TP: <code>{tp}</code>\n\n"
                        f"<b><u>Context:</u></b>\n" f"  Trend: {trend} | <b>STrend: {st}</b>\n" f"  Mom: {mom} | Vol: {vol}\n"
                        f"  Div: {div} | Market: {market}\n" f"  HTF: {htf}\n"
                        f"<b><u>Sentiment:</u></b>\n" f"  Funding: <code>{fund}</code> | OI: {oi}\n" f"  Confirm: {sent} | Wave: {wave}" )

            url=f"https://api.telegram.org/bot{self.TELEGRAM_TOKEN}/sendMessage"; payload={'chat_id': self.TELEGRAM_CHAT_ID, 'text': message, 'parse_mode': 'HTML'}
            response = requests.post(url, json=payload, timeout=10)
            if response.status_code==200: logging.info(f"Sent TG msg for {symbol}.")
            else: logging.error(f"Failed TG send ({symbol}): {response.status_code} - {response.text}")
        except Exception as e: logging.error(f"Error sending TG msg ({symbol}): {e}", exc_info=True)

    # --- GUI Queue Processing & Main Loop (No changes from previous full code) ---
    def process_gui_queue(self):
        try:
            while not self.gui_queue.empty():
                callback_func = self.gui_queue.get_nowait()
                if callable(callback_func):
                    try: callback_func() # Execute in main thread
                    except Exception as e: logging.error(f"Error in GUI callback '{getattr(callback_func,'__name__','N/A')}': {e}", exc_info=True)
                else: logging.warning(f"Non-callable item from GUI queue: {callback_func}")
                self.gui_queue.task_done()
        except queue.Empty: pass
        except Exception as e: logging.error(f"Error processing GUI queue: {e}", exc_info=True)
        finally: # Re-schedule for next frame
            if dpg.is_dearpygui_running():
                 frame = dpg.get_frame_count()
                 if frame >= 0: dpg.set_frame_callback(frame + 1, callback=self.process_gui_queue)
                 elif frame == -1 and not hasattr(self, '_queue_warned'):
                      logging.warning("DPG frame count invalid, cannot reschedule queue processing yet.")
                      self._queue_warned = True

    def run(self):
        logging.info("Running GUI...")
        dpg.start_dearpygui()

# --- Main Function ---
def main():
    # Configure logging first
    configure_logging()
    logging.info("Starting application...")

    try:
        logging.info("Initializing AdvancedTradingBot...")
        bot = AdvancedTradingBot()
        logging.info("AdvancedTradingBot initialized.")

        if not bot.session:
            logging.critical("Failed Bybit connection. Check API keys/network. Exiting.")
            print("CRITICAL: Failed Bybit connection. Check API keys/network. Exiting.")
            return

        logging.info("Creating AdvancedTradingGUI...")
        gui = AdvancedTradingGUI(bot)
        logging.info("AdvancedTradingGUI created successfully.")

        logging.info("Running GUI...")
        gui.run()
        logging.info("GUI run completed.")

    except Exception as e:
        logging.error(f"Error in main: {type(e).__name__} - {e}", exc_info=True)
        print(f"Error: {type(e).__name__} - {e}")  # Force print to console
        raise  # Re-raise to see full traceback

    finally:
        logging.info("Shutting down application...")

    gui = AdvancedTradingGUI(bot)
    gui.run()

if __name__ == "__main__":
    main()
