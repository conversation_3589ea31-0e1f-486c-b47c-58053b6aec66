2025-04-05 01:38:33,235 INFO: DPG main loop ended
2025-04-05 01:38:33,236 INFO: Shutting down GUI...
2025-04-05 01:38:33,238 INFO: GUI run completed.
2025-04-05 01:38:33,241 INFO: Shutting down application...
2025-04-05 01:38:33,244 INFO: Shutting down GUI...
2025-04-05 01:38:33,246 INFO: GUI run completed.
2025-04-05 01:38:33,247 INFO: Shutting down application...
2025-04-05 01:39:07,492 INFO: File logging configured successfully.
2025-04-05 01:39:07,492 INFO: Starting application...
2025-04-05 01:39:07,492 INFO: Initializing AdvancedTradingBot...
2025-04-05 01:39:07,492 INFO: Loaded 2 open trades from file
2025-04-05 01:39:07,499 INFO: Initializing direct Bybit client...
2025-04-05 01:39:07,503 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:07,837 DEBUG: https://api.bybit.com:443 "GET /v5/market/time HTTP/1.1" 200 134
2025-04-05 01:39:07,838 INFO: Time synchronized. Server: 1743806343000, Local: 1743806347838, Offset: -4838 ms
2025-04-05 01:39:07,838 INFO: Direct Bybit client initialized successfully with time offset: -4838 ms
2025-04-05 01:39:07,838 INFO: Initializing pybit client as fallback...
2025-04-05 01:39:07,839 DEBUG: Initializing HTTP session.
2025-04-05 01:39:07,840 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:08,060 DEBUG: https://api.bybit.com:443 "GET /v5/market/time HTTP/1.1" 200 134
2025-04-05 01:39:08,061 INFO: Pybit client initialized successfully. Server time: {'retCode': 0, 'retMsg': 'OK', 'result': {'timeSecond': '1743806344', 'timeNano': '1743806344133779206'}, 'retExtInfo': {}, 'time': 1743806344133}
2025-04-05 01:39:08,062 INFO: AdvancedTradingBot initialized.
2025-04-05 01:39:08,062 INFO: Creating AdvancedTradingGUI...
2025-04-05 01:39:08,062 INFO: AdvancedTradingGUI init started.
2025-04-05 01:39:08,063 INFO: Loaded 7 trades from history file
2025-04-05 01:39:08,063 INFO: Calculated equity curve with 8 points
2025-04-05 01:39:08,064 INFO: Loaded 38 symbols from watchlist.json
2025-04-05 01:39:08,064 INFO: AdvancedTradingGUI created successfully.
2025-04-05 01:39:08,065 INFO: Running GUI...
2025-04-05 01:39:08,065 INFO: Running GUI...
2025-04-05 01:39:08,066 INFO: DPG context created
2025-04-05 01:39:08,066 INFO: DPG viewport created
2025-04-05 01:39:08,069 INFO: Main window created
2025-04-05 01:39:08,204 INFO: Viewport shown
2025-04-05 01:39:08,205 DEBUG: Fetching fresh positions data from API
2025-04-05 01:39:08,206 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:08,421 DEBUG: https://api.bybit.com:443 "GET /v5/market/time HTTP/1.1" 200 134
2025-04-05 01:39:08,422 DEBUG: Request URL: https://api.bybit.com/v5/position/list
2025-04-05 01:39:08,422 DEBUG: Request Params: {'category': 'linear', 'settleCoin': 'USDT', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': 1743806344000, 'recv_window': 120000, 'sign': '257548b426a5332faf330f3ee9412aac40ce4da141a1905e0330538829c9b3cf'}
2025-04-05 01:39:08,424 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:08,640 DEBUG: https://api.bybit.com:443 "GET /v5/position/list?category=linear&settleCoin=USDT&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806344000&recv_window=120000&sign=257548b426a5332faf330f3ee9412aac40ce4da141a1905e0330538829c9b3cf HTTP/1.1" 200 125
2025-04-05 01:39:08,642 ERROR: Error using pybit client: AdvancedTradingBot.initialize_api_clients.<locals>.patched_auth() got an unexpected keyword argument 'recv_window'
2025-04-05 01:39:08,643 INFO: Saved 0 positions as last known positions
2025-04-05 01:39:08,643 DEBUG: Using cached positions data (age: 0.4s)
2025-04-05 01:39:08,644 INFO: Saved 0 positions as last known positions
2025-04-05 01:39:08,644 DEBUG: Using cached positions data (age: 0.4s)
2025-04-05 01:39:08,645 DEBUG: Saved 0 positions to last_positions.json
2025-04-05 01:39:08,645 INFO: Starting DPG main loop
2025-04-05 01:39:10,429 INFO: Starting full manual analysis...
2025-04-05 01:39:10,430 DEBUG: (10000000AIDOGEUSDT) Starting analysis for 1h...
2025-04-05 01:39:10,431 DEBUG: (1000000BABYDOGEUSDT) Starting analysis for 1h...
2025-04-05 01:39:10,431 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:10,431 DEBUG: (1000000CHEEMSUSDT) Starting analysis for 1h...
2025-04-05 01:39:10,431 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:10,431 DEBUG: (1000000MOGUSDT) Starting analysis for 1h...
2025-04-05 01:39:10,432 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000000AIDOGEUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806350431', 'recv_window': '120000', 'sign': '4e1f9d75af8f43d17376ce1effb00d644a05dae170ba0bc6e497f3d14f282b0a'}
2025-04-05 01:39:10,432 DEBUG: (1000000PEIPEIUSDT) Starting analysis for 1h...
2025-04-05 01:39:10,432 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:10,432 DEBUG: (10000COQUSDT) Starting analysis for 1h...
2025-04-05 01:39:10,432 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000000BABYDOGEUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806350431', 'recv_window': '120000', 'sign': 'c96d30ca8e26f04aa4fa7da650f16480384c7a5ec858bb3bfac7c635cc218e56'}
2025-04-05 01:39:10,433 DEBUG: (10000ELONUSDT) Starting analysis for 1h...
2025-04-05 01:39:10,433 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:10,433 DEBUG: (10000LADYSUSDT) Starting analysis for 1h...
2025-04-05 01:39:10,433 DEBUG: (10000QUBICUSDT) Starting analysis for 1h...
2025-04-05 01:39:10,434 DEBUG: (10000SATSUSDT) Starting analysis for 1h...
2025-04-05 01:39:10,434 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:10,435 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:10,435 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000000CHEEMSUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806350432', 'recv_window': '120000', 'sign': '1a286767eadd65deb1a74b43bab564b294fc8ec4de0a0439c37852c5604110f5'}
2025-04-05 01:39:10,435 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:10,436 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:10,436 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:10,437 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000000MOGUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806350433', 'recv_window': '120000', 'sign': '2b3bed9def5b5045b73f324a07e81c34593a237b61a8bb3471392fbf43d3f396'}
2025-04-05 01:39:10,437 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:10,437 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:10,437 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:10,438 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000000PEIPEIUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806350434', 'recv_window': '120000', 'sign': 'bd4d3f5691f847a31eade2c9ae250401c579fd9518d2e26be05151e43ff4125c'}
2025-04-05 01:39:10,439 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:10,439 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000COQUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806350435', 'recv_window': '120000', 'sign': '9fe9e67d8e6dc3994964e373b7953f2b70ed0877a75e01025406f70b32f010a7'}
2025-04-05 01:39:10,439 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000ELONUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806350436', 'recv_window': '120000', 'sign': '9e0e216484c43c71706a10f3e793aa8170ba183a2a090dd30940b6029682c564'}
2025-04-05 01:39:10,441 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:10,441 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000LADYSUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806350437', 'recv_window': '120000', 'sign': '5259b0dfd4178b8212aac619260163f10fb780e62a0899c82d94f125d3198a9b'}
2025-04-05 01:39:10,442 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000QUBICUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806350437', 'recv_window': '120000', 'sign': '69847c27b9987bad73674800c263664268508b4a3a7ad5714aa513583aa1bb31'}
2025-04-05 01:39:10,442 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000SATSUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806350437', 'recv_window': '120000', 'sign': 'e10e7cc837b3013303611abd0be1c42844de0891ff3abc307fa3f085ebd76c39'}
2025-04-05 01:39:10,444 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:10,445 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:10,446 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:10,447 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:10,449 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:10,450 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:10,655 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000000AIDOGEUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806350431&recv_window=120000&sign=4e1f9d75af8f43d17376ce1effb00d644a05dae170ba0bc6e497f3d14f282b0a HTTP/1.1" 200 None
2025-04-05 01:39:10,660 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000000BABYDOGEUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806350431&recv_window=120000&sign=c96d30ca8e26f04aa4fa7da650f16480384c7a5ec858bb3bfac7c635cc218e56 HTTP/1.1" 200 None
2025-04-05 01:39:10,662 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000000MOGUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806350433&recv_window=120000&sign=2b3bed9def5b5045b73f324a07e81c34593a237b61a8bb3471392fbf43d3f396 HTTP/1.1" 200 None
2025-04-05 01:39:10,663 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000000CHEEMSUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806350432&recv_window=120000&sign=1a286767eadd65deb1a74b43bab564b294fc8ec4de0a0439c37852c5604110f5 HTTP/1.1" 200 None
2025-04-05 01:39:10,673 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000LADYSUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806350437&recv_window=120000&sign=5259b0dfd4178b8212aac619260163f10fb780e62a0899c82d94f125d3198a9b HTTP/1.1" 200 None
2025-04-05 01:39:10,673 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000QUBICUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806350437&recv_window=120000&sign=69847c27b9987bad73674800c263664268508b4a3a7ad5714aa513583aa1bb31 HTTP/1.1" 200 None
2025-04-05 01:39:10,674 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000SATSUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806350437&recv_window=120000&sign=e10e7cc837b3013303611abd0be1c42844de0891ff3abc307fa3f085ebd76c39 HTTP/1.1" 200 None
2025-04-05 01:39:10,674 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000ELONUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806350436&recv_window=120000&sign=9e0e216484c43c71706a10f3e793aa8170ba183a2a090dd30940b6029682c564 HTTP/1.1" 200 6051
2025-04-05 01:39:10,674 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000COQUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806350435&recv_window=120000&sign=9fe9e67d8e6dc3994964e373b7953f2b70ed0877a75e01025406f70b32f010a7 HTTP/1.1" 200 None
2025-04-05 01:39:10,678 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000000PEIPEIUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806350434&recv_window=120000&sign=bd4d3f5691f847a31eade2c9ae250401c579fd9518d2e26be05151e43ff4125c HTTP/1.1" 200 5778
2025-04-05 01:39:10,903 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.00/0.00 vs Threshold: 3.5
2025-04-05 01:39:10,937 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:10,994 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:11,028 DEBUG: (N/A) Last Close: 0.0008, Raw ATR: 0.0000, Valid ATR: 0.0000
2025-04-05 01:39:11,035 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.50/0.00 vs Threshold: 3.5
2025-04-05 01:39:11,064 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:11,075 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:11,104 DEBUG: (N/A) Fib Levels: 0%=0.0008, 38.2%=0.0008, 61.8%=0.0008, 100%=0.0008, 161.8%=0.0009, -61.8%=0.0007
2025-04-05 01:39:11,124 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:11,136 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/1.00 vs Threshold: 3.5
2025-04-05 01:39:11,158 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.00/0.00 vs Threshold: 3.5
2025-04-05 01:39:11,158 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.0007823774, 0.0007955226), SL: 0.0008, TP: 0.0009
2025-04-05 01:39:11,169 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.50/1.00 vs Threshold: 3.5
2025-04-05 01:39:11,173 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.00/0.00 vs Threshold: 3.5
2025-04-05 01:39:11,196 DEBUG: (N/A) Last Close: 0.3844, Raw ATR: 0.0093, Valid ATR: 0.0093
2025-04-05 01:39:11,213 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:11,226 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/0.00 vs Threshold: 3.5
2025-04-05 01:39:11,229 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:11,232 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.50/1.00 vs Threshold: 3.5
2025-04-05 01:39:11,235 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/0.00 vs Threshold: 3.5
2025-04-05 01:39:11,238 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-04-05 01:39:11,238 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:11,238 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:11,239 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:11,239 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:11,239 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:11,240 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:11,240 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:11,240 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.50/9.0 | Final Confidence: 27.8% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:39:11,240 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:39:11,241 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:39:11,241 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.0007823774, 0.0007955226), SL=0.0008, TP=0.0009
2025-04-05 01:39:11,241 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:11,242 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:11,242 DEBUG: (N/A) Fib Levels: 0%=0.3567, 38.2%=0.3709, 61.8%=0.3797, 100%=0.3939, 161.8%=0.4169, -61.8%=0.3337
2025-04-05 01:39:11,243 DEBUG: (N/A) Last Close: 0.0003, Raw ATR: 0.0000, Valid ATR: 0.0000
2025-04-05 01:39:11,243 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:11,244 DEBUG: (N/A) Last Close: 1.6407, Raw ATR: 0.0471, Valid ATR: 0.0471
2025-04-05 01:39:11,244 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:11,246 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:11,246 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:11,247 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:11,247 DEBUG: (N/A) Last Close: 0.0100, Raw ATR: 0.0002, Valid ATR: 0.0002
2025-04-05 01:39:11,247 DEBUG: (N/A) Last Close: 0.0012, Raw ATR: 0.0000, Valid ATR: 0.0000
2025-04-05 01:39:11,247 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.3709104, 0.37968959999999996), SL: 0.3544, TP: 0.4169
2025-04-05 01:39:11,247 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:11,248 DEBUG: (N/A) Last Close: 0.0013, Raw ATR: 0.0000, Valid ATR: 0.0000
2025-04-05 01:39:11,248 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:11,248 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000COQUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806351244', 'recv_window': '120000', 'sign': '6cd1d53144f1eb768d4e3e01d46db3efc8ceda280bde93d1bd6b027b45858231'}
2025-04-05 01:39:11,248 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000000PEIPEIUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806351246', 'recv_window': '120000', 'sign': '25adbad9a0d94243c6a045b8db104731904d0a0d6b22b3497203a5a0bf06057e'}
2025-04-05 01:39:11,249 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000SATSUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806351246', 'recv_window': '120000', 'sign': 'c7253edfbb8629b6c7e23026513f37aa5d9de6ee67a3de5942777dd346530578'}
2025-04-05 01:39:11,249 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:11,249 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:11,249 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:11,250 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:11,250 DEBUG: (N/A) Fib Levels: 0%=0.0003, 38.2%=0.0003, 61.8%=0.0003, 100%=0.0003, 161.8%=0.0003, -61.8%=0.0003
2025-04-05 01:39:11,250 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:11,250 DEBUG: (N/A) Fib Levels: 0%=1.4411, 38.2%=1.5223, 61.8%=1.5724, 100%=1.6536, 161.8%=1.7849, -61.8%=1.3098
2025-04-05 01:39:11,251 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:11,252 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:11,253 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:11,253 INFO: N/A 1h: BUY signal, but price (0.0008) is outside entry zone (0.0008-0.0008). Penalty currently disabled for test.
2025-04-05 01:39:11,253 DEBUG: (N/A) Fib Levels: 0%=0.0094, 38.2%=0.0097, 61.8%=0.0099, 100%=0.0102, 161.8%=0.0108, -61.8%=0.0088
2025-04-05 01:39:11,254 DEBUG: (N/A) Fib Levels: 0%=0.0011, 38.2%=0.0011, 61.8%=0.0012, 100%=0.0012, 161.8%=0.0012, -61.8%=0.0011
2025-04-05 01:39:11,254 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.3709104, 0.37968959999999996), SL=0.3544, TP=0.4169
2025-04-05 01:39:11,254 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.0003187232, 0.0003228768), SL: 0.0003, TP: 0.0003
2025-04-05 01:39:11,254 DEBUG: (N/A) Fib Levels: 0%=0.0012, 38.2%=0.0013, 61.8%=0.0013, 100%=0.0013, 161.8%=0.0013, -61.8%=0.0012
2025-04-05 01:39:11,255 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (1.522275, 1.572425), SL: 1.4293, TP: 1.7849
2025-04-05 01:39:11,256 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.00/9.0 | Final Confidence: 44.4% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:11,256 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.00970216, 0.00990984), SL: 0.0093, TP: 0.0108
2025-04-05 01:39:11,256 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.0011400874, 0.0011544126), SL: 0.0011, TP: 0.0012
2025-04-05 01:39:11,257 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:11,257 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:11,257 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.0012516839999999999, 0.001266316), SL: 0.0012, TP: 0.0013
2025-04-05 01:39:11,257 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:11,258 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:11,259 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:11,259 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:11,259 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:11,259 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.0003187232, 0.0003228768), SL=0.0003, TP=0.0003
2025-04-05 01:39:11,260 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:11,260 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(1.522275, 1.572425), SL=1.4293, TP=1.7849
2025-04-05 01:39:11,260 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000000AIDOGEUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806351258', 'recv_window': '120000', 'sign': 'f3d2cb448afafac5cfb259031ff0f7c44f7134179b558aa17fc10cc79fa0be89'}
2025-04-05 01:39:11,260 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.00970216, 0.00990984), SL=0.0093, TP=0.0108
2025-04-05 01:39:11,260 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.0011400874, 0.0011544126), SL=0.0011, TP=0.0012
2025-04-05 01:39:11,261 INFO: N/A 1h: BUY signal, but price (0.3844) is outside entry zone (0.3709-0.3797). Penalty currently disabled for test.
2025-04-05 01:39:11,261 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:11,261 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.0012516839999999999, 0.001266316), SL=0.0012, TP=0.0013
2025-04-05 01:39:11,261 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:11,262 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:11,263 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:11,263 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:11,263 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.50/9.0 | Final Confidence: 50.0% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:11,263 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:11,263 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:11,264 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:11,264 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:11,265 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:11,265 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:11,266 INFO: N/A 1h: BUY signal, but price (0.0003) is outside entry zone (0.0003-0.0003). Penalty currently disabled for test.
2025-04-05 01:39:11,266 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:11,266 INFO: N/A 1h: BUY signal, but price (1.6407) is outside entry zone (1.5223-1.5724). Penalty currently disabled for test.
2025-04-05 01:39:11,266 INFO: N/A 1h: BUY signal, but price (0.0100) is outside entry zone (0.0097-0.0099). Penalty currently disabled for test.
2025-04-05 01:39:11,267 INFO: N/A 1h: BUY signal, but price (0.0012) is outside entry zone (0.0011-0.0012). Penalty currently disabled for test.
2025-04-05 01:39:11,267 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000000MOGUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806351265', 'recv_window': '120000', 'sign': 'c0f29e44ac067b88d8b2cc71704bc77c836ec48e6d894bd71328ddfa944fcd8e'}
2025-04-05 01:39:11,267 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:11,267 INFO: N/A 1h: BUY signal, but price (0.0013) is outside entry zone (0.0013-0.0013). Penalty currently disabled for test.
2025-04-05 01:39:11,268 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.00/9.0 | Final Confidence: 44.4% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:11,268 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.50/9.0 | Final Confidence: 50.0% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:11,268 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.00/9.0 | Final Confidence: 44.4% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:11,270 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:11,271 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:11,271 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:11,272 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:11,272 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:11,273 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:11,274 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000LADYSUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806351271', 'recv_window': '120000', 'sign': '95fa7e501cfa51e9eb1636c141721644ef6050f18a7be99d7b194f3d680fbf52'}
2025-04-05 01:39:11,275 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:11,275 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000000CHEEMSUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806351272', 'recv_window': '120000', 'sign': 'c469e8abde2a7aa1a336321254b1330819e823590e9dd2d3a1afdf8659f4ff54'}
2025-04-05 01:39:11,275 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000QUBICUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806351272', 'recv_window': '120000', 'sign': '1a79eadaaa79625fd3d7bad68ce4b3dc93654085e0566e8b80b6038d29b3898f'}
2025-04-05 01:39:11,275 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000000BABYDOGEUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806351273', 'recv_window': '120000', 'sign': 'bc3840ba6aa35b2a747818875192e7464b78a1e10e25d8230c2f2b9b5110c44b'}
2025-04-05 01:39:11,276 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:11,276 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000ELONUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806351274', 'recv_window': '120000', 'sign': 'f976494d36deec302dc40fd0d39c3590f074e6145783e616ee98ec37c89b83b1'}
2025-04-05 01:39:11,278 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:11,279 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:11,279 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:11,281 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:11,471 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000COQUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806351244&recv_window=120000&sign=6cd1d53144f1eb768d4e3e01d46db3efc8ceda280bde93d1bd6b027b45858231 HTTP/1.1" 200 None
2025-04-05 01:39:11,472 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000SATSUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806351246&recv_window=120000&sign=c7253edfbb8629b6c7e23026513f37aa5d9de6ee67a3de5942777dd346530578 HTTP/1.1" 200 None
2025-04-05 01:39:11,477 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000000PEIPEIUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806351246&recv_window=120000&sign=25adbad9a0d94243c6a045b8db104731904d0a0d6b22b3497203a5a0bf06057e HTTP/1.1" 200 None
2025-04-05 01:39:11,486 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000000AIDOGEUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806351258&recv_window=120000&sign=f3d2cb448afafac5cfb259031ff0f7c44f7134179b558aa17fc10cc79fa0be89 HTTP/1.1" 200 None
2025-04-05 01:39:11,494 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000000MOGUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806351265&recv_window=120000&sign=c0f29e44ac067b88d8b2cc71704bc77c836ec48e6d894bd71328ddfa944fcd8e HTTP/1.1" 200 None
2025-04-05 01:39:11,501 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000QUBICUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806351272&recv_window=120000&sign=1a79eadaaa79625fd3d7bad68ce4b3dc93654085e0566e8b80b6038d29b3898f HTTP/1.1" 200 5066
2025-04-05 01:39:11,505 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000000CHEEMSUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806351272&recv_window=120000&sign=c469e8abde2a7aa1a336321254b1330819e823590e9dd2d3a1afdf8659f4ff54 HTTP/1.1" 200 None
2025-04-05 01:39:11,505 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000000BABYDOGEUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806351273&recv_window=120000&sign=bc3840ba6aa35b2a747818875192e7464b78a1e10e25d8230c2f2b9b5110c44b HTTP/1.1" 200 None
2025-04-05 01:39:11,795 DEBUG: Starting new HTTPS connection (2): api.bybit.com:443
2025-04-05 01:39:11,859 DEBUG: Starting new HTTPS connection (3): api.bybit.com:443
2025-04-05 01:39:11,870 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000LADYSUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806351271&recv_window=120000&sign=95fa7e501cfa51e9eb1636c141721644ef6050f18a7be99d7b194f3d680fbf52 HTTP/1.1" 200 5630
2025-04-05 01:39:11,876 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000ELONUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806351274&recv_window=120000&sign=f976494d36deec302dc40fd0d39c3590f074e6145783e616ee98ec37c89b83b1 HTTP/1.1" 200 5298
2025-04-05 01:39:11,883 DEBUG: Starting new HTTPS connection (4): api.bybit.com:443
2025-04-05 01:39:11,902 DEBUG: Starting new HTTPS connection (5): api.bybit.com:443
2025-04-05 01:39:11,911 DEBUG: Starting new HTTPS connection (6): api.bybit.com:443
2025-04-05 01:39:11,914 DEBUG: Starting new HTTPS connection (7): api.bybit.com:443
2025-04-05 01:39:11,917 DEBUG: Starting new HTTPS connection (8): api.bybit.com:443
2025-04-05 01:39:11,947 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=10000COQUSDT HTTP/1.1" 200 197
2025-04-05 01:39:12,018 DEBUG: Starting new HTTPS connection (9): api.bybit.com:443
2025-04-05 01:39:12,018 DEBUG: Starting new HTTPS connection (10): api.bybit.com:443
2025-04-05 01:39:12,121 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000000PEIPEIUSDT HTTP/1.1" 200 197
2025-04-05 01:39:12,138 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=10000000AIDOGEUSDT HTTP/1.1" 200 198
2025-04-05 01:39:12,146 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000000MOGUSDT HTTP/1.1" 200 195
2025-04-05 01:39:12,148 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000000CHEEMSUSDT HTTP/1.1" 200 197
2025-04-05 01:39:12,169 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=10000COQUSDT HTTP/1.1" 200 318
2025-04-05 01:39:12,170 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000000BABYDOGEUSDT HTTP/1.1" 200 199
2025-04-05 01:39:12,172 DEBUG: (10000COQUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:39:12,174 DEBUG: (10000WENUSDT) Starting analysis for 1h...
2025-04-05 01:39:12,175 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=10000QUBICUSDT HTTP/1.1" 200 194
2025-04-05 01:39:12,176 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:12,180 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000WENUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806352176', 'recv_window': '120000', 'sign': '38a55011f1ac48a92005e247706ad1f7b1bc59bc9c1232b64115ac4ac3befd47'}
2025-04-05 01:39:12,182 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:12,241 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=10000LADYSUSDT HTTP/1.1" 200 194
2025-04-05 01:39:12,243 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=10000ELONUSDT HTTP/1.1" 200 194
2025-04-05 01:39:12,320 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000000PEIPEIUSDT HTTP/1.1" 200 320
2025-04-05 01:39:12,326 DEBUG: (1000000PEIPEIUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:39:12,327 DEBUG: (10000WHYUSDT) Starting analysis for 1h...
2025-04-05 01:39:12,328 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:12,329 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000WHYUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806352328', 'recv_window': '120000', 'sign': '908c7f55995660ff7646e97a259892ab8472f0f364a40388475d35aa271bcaf0'}
2025-04-05 01:39:12,331 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:12,336 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=10000000AIDOGEUSDT HTTP/1.1" 200 325
2025-04-05 01:39:12,337 DEBUG: (10000000AIDOGEUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:12,338 DEBUG: (1000APUUSDT) Starting analysis for 1h...
2025-04-05 01:39:12,339 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:12,340 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000APUUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806352339', 'recv_window': '120000', 'sign': 'c4e0255e2aa684f42c440d508dcd06470f2ea0a0ea85a134fb94c22de5e7132b'}
2025-04-05 01:39:12,342 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:12,343 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000000MOGUSDT HTTP/1.1" 200 317
2025-04-05 01:39:12,344 DEBUG: (1000000MOGUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:12,345 DEBUG: (1000BONKUSDT) Starting analysis for 1h...
2025-04-05 01:39:12,345 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000000CHEEMSUSDT HTTP/1.1" 200 320
2025-04-05 01:39:12,345 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:12,346 DEBUG: (1000000CHEEMSUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:12,346 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000BONKUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806352345', 'recv_window': '120000', 'sign': 'a2d73b4d62a948998d2ac3caf358f381a4bf4d391097f00f0b10700d1fb60f84'}
2025-04-05 01:39:12,346 DEBUG: (1000BTTUSDT) Starting analysis for 1h...
2025-04-05 01:39:12,347 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:12,348 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:12,348 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000BTTUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806352348', 'recv_window': '120000', 'sign': '6e2917442bae65ce624d0e2d9bf1c49907fcf98288fb58d24ad20d806671300e'}
2025-04-05 01:39:12,350 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:12,368 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000000BABYDOGEUSDT HTTP/1.1" 200 328
2025-04-05 01:39:12,369 DEBUG: (1000000BABYDOGEUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:12,370 DEBUG: (1000CATSUSDT) Starting analysis for 1h...
2025-04-05 01:39:12,370 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:12,371 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000CATSUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806352370', 'recv_window': '120000', 'sign': '7314a5113e6a5c2fbea4b4a97c1dae9be301aeb88243e7bbf21728a0114eed55'}
2025-04-05 01:39:12,372 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:12,377 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=10000QUBICUSDT HTTP/1.1" 200 319
2025-04-05 01:39:12,378 DEBUG: (10000QUBICUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:12,379 DEBUG: (1000CATUSDT) Starting analysis for 1h...
2025-04-05 01:39:12,380 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:12,380 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000CATUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806352379', 'recv_window': '120000', 'sign': '6a5fc97eaca66cf38cd6cb58ece6196955150f79307d318217d25e24c0854b0e'}
2025-04-05 01:39:12,381 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:12,406 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000WENUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806352176&recv_window=120000&sign=38a55011f1ac48a92005e247706ad1f7b1bc59bc9c1232b64115ac4ac3befd47 HTTP/1.1" 200 None
2025-04-05 01:39:12,445 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=10000LADYSUSDT HTTP/1.1" 200 323
2025-04-05 01:39:12,460 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=10000ELONUSDT HTTP/1.1" 200 320
2025-04-05 01:39:12,461 DEBUG: (10000LADYSUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:12,463 DEBUG: (10000ELONUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:12,471 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=10000SATSUSDT HTTP/1.1" 200 197
2025-04-05 01:39:12,472 DEBUG: (1000FLOKIUSDT) Starting analysis for 1h...
2025-04-05 01:39:12,477 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/0.30 vs Threshold: 3.5
2025-04-05 01:39:12,477 DEBUG: (1000LUNCUSDT) Starting analysis for 1h...
2025-04-05 01:39:12,478 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:12,478 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:12,479 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:12,479 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000FLOKIUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806352478', 'recv_window': '120000', 'sign': '57594562e0e7638711bc304c5436a368c6d48afbdcf22d549c680cbfbf30161f'}
2025-04-05 01:39:12,479 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:12,480 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000LUNCUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806352479', 'recv_window': '120000', 'sign': 'a62afe758ebe2239278cf35c9848c83d308cc5612fadd8ec2fb79b9349fcc5da'}
2025-04-05 01:39:12,480 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:12,481 DEBUG: (N/A) Last Close: 0.2120, Raw ATR: 0.0030, Valid ATR: 0.0030
2025-04-05 01:39:12,482 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:12,482 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:12,484 DEBUG: (N/A) Fib Levels: 0%=0.2012, 38.2%=0.2058, 61.8%=0.2086, 100%=0.2132, 161.8%=0.2206, -61.8%=0.1938
2025-04-05 01:39:12,484 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.205784, 0.208616), SL: 0.2004, TP: 0.2206
2025-04-05 01:39:12,485 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:12,485 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.205784, 0.208616), SL=0.2004, TP=0.2206
2025-04-05 01:39:12,485 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:12,485 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:12,486 INFO: N/A 1h: BUY signal, but price (0.2120) is outside entry zone (0.2058-0.2086). Penalty currently disabled for test.
2025-04-05 01:39:12,486 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:12,487 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:12,487 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000WENUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806352487', 'recv_window': '120000', 'sign': 'c96fda7382eaecbba6085b44812fcfa289594e343baa0a67d706862f4bbea574'}
2025-04-05 01:39:12,489 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:12,560 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000APUUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806352339&recv_window=120000&sign=c4e0255e2aa684f42c440d508dcd06470f2ea0a0ea85a134fb94c22de5e7132b HTTP/1.1" 200 None
2025-04-05 01:39:12,581 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000BTTUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806352348&recv_window=120000&sign=6e2917442bae65ce624d0e2d9bf1c49907fcf98288fb58d24ad20d806671300e HTTP/1.1" 200 None
2025-04-05 01:39:12,589 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000CATSUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806352370&recv_window=120000&sign=7314a5113e6a5c2fbea4b4a97c1dae9be301aeb88243e7bbf21728a0114eed55 HTTP/1.1" 200 6072
2025-04-05 01:39:12,612 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000CATUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806352379&recv_window=120000&sign=6a5fc97eaca66cf38cd6cb58ece6196955150f79307d318217d25e24c0854b0e HTTP/1.1" 200 None
2025-04-05 01:39:12,629 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.00/0.00 vs Threshold: 3.5
2025-04-05 01:39:12,636 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:12,648 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:12,659 DEBUG: (N/A) Last Close: 0.1658, Raw ATR: 0.0046, Valid ATR: 0.0046
2025-04-05 01:39:12,670 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:12,676 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=10000SATSUSDT HTTP/1.1" 200 322
2025-04-05 01:39:12,684 DEBUG: (N/A) Fib Levels: 0%=0.1495, 38.2%=0.1565, 61.8%=0.1609, 100%=0.1679, 161.8%=0.1793, -61.8%=0.1381
2025-04-05 01:39:12,698 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000LUNCUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806352479&recv_window=120000&sign=a62afe758ebe2239278cf35c9848c83d308cc5612fadd8ec2fb79b9349fcc5da HTTP/1.1" 200 5806
2025-04-05 01:39:12,698 DEBUG: (10000SATSUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:39:12,699 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000FLOKIUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806352478&recv_window=120000&sign=57594562e0e7638711bc304c5436a368c6d48afbdcf22d549c680cbfbf30161f HTTP/1.1" 200 5911
2025-04-05 01:39:12,703 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.1565288, 0.1608712), SL: 0.1484, TP: 0.1793
2025-04-05 01:39:12,710 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000WENUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806352487&recv_window=120000&sign=c96fda7382eaecbba6085b44812fcfa289594e343baa0a67d706862f4bbea574 HTTP/1.1" 200 None
2025-04-05 01:39:12,727 DEBUG: (1000MUMUUSDT) Starting analysis for 1h...
2025-04-05 01:39:12,751 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:12,798 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:12,811 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.1565288, 0.1608712), SL=0.1484, TP=0.1793
2025-04-05 01:39:12,832 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000MUMUUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806352798', 'recv_window': '120000', 'sign': 'ad43215499a096444e283448260f10c4101a848a2ba4cf798391b0ff8e06226d'}
2025-04-05 01:39:12,849 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.00/0.00 vs Threshold: 3.5
2025-04-05 01:39:12,851 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:12,853 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/0.50 vs Threshold: 3.5
2025-04-05 01:39:12,869 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:12,869 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:12,874 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/0.00 vs Threshold: 3.5
2025-04-05 01:39:12,880 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:12,899 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:39:12,911 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000WHYUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806352328&recv_window=120000&sign=908c7f55995660ff7646e97a259892ab8472f0f364a40388475d35aa271bcaf0 HTTP/1.1" 200 6485
2025-04-05 01:39:12,913 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:12,929 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000BONKUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806352345&recv_window=120000&sign=a2d73b4d62a948998d2ac3caf358f381a4bf4d391097f00f0b10700d1fb60f84 HTTP/1.1" 200 6304
2025-04-05 01:39:12,937 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:39:12,963 INFO: N/A 1h: BUY signal, but price (0.1658) is outside entry zone (0.1565-0.1609). Penalty currently disabled for test.
2025-04-05 01:39:12,970 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/0.00 vs Threshold: 3.5
2025-04-05 01:39:12,974 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/1.00 vs Threshold: 3.5
2025-04-05 01:39:12,976 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:12,990 DEBUG: (N/A) Last Close: 0.0072, Raw ATR: 0.0001, Valid ATR: 0.0001
2025-04-05 01:39:13,002 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:13,010 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.00/9.0 | Final Confidence: 44.4% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:13,019 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:13,025 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:13,059 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000BTTUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806352976', 'recv_window': '120000', 'sign': 'a6191bcb1fae86764f8a5fadcb5c099f467f60f75981082d1dd479ed2bc1b138'}
2025-04-05 01:39:13,064 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:13,067 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000CATUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806353002', 'recv_window': '120000', 'sign': '9d3d4afd6506c1a0d3df29b12f9602f9810fe94efa0f5d1c9d02975584c0fed7'}
2025-04-05 01:39:13,070 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:13,103 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:13,105 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:13,118 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:13,125 DEBUG: (N/A) Fib Levels: 0%=0.0067, 38.2%=0.0069, 61.8%=0.0070, 100%=0.0073, 161.8%=0.0076, -61.8%=0.0063
2025-04-05 01:39:13,129 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/1.00 vs Threshold: 3.5
2025-04-05 01:39:13,133 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.00/1.00 vs Threshold: 3.5
2025-04-05 01:39:13,134 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:13,134 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000APUUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806353070', 'recv_window': '120000', 'sign': 'cf604ebdaf7a4fd56380b437b28bd6c6b6950f8587ca2c1f8227f598cd98f4bf'}
2025-04-05 01:39:13,134 DEBUG: (N/A) Last Close: 0.0590, Raw ATR: 0.0008, Valid ATR: 0.0008
2025-04-05 01:39:13,135 DEBUG: (N/A) Last Close: 0.0570, Raw ATR: 0.0009, Valid ATR: 0.0009
2025-04-05 01:39:13,135 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.0069127400000000006, 0.00704726), SL: 0.0067, TP: 0.0076
2025-04-05 01:39:13,136 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000MUMUUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806352798&recv_window=120000&sign=ad43215499a096444e283448260f10c4101a848a2ba4cf798391b0ff8e06226d HTTP/1.1" 200 None
2025-04-05 01:39:13,136 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:13,136 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:13,138 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:13,138 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:13,138 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:13,139 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:13,142 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:13,157 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:13,160 DEBUG: (N/A) Fib Levels: 0%=0.0572, 38.2%=0.0582, 61.8%=0.0588, 100%=0.0598, 161.8%=0.0615, -61.8%=0.0555
2025-04-05 01:39:13,166 DEBUG: (N/A) Fib Levels: 0%=0.0534, 38.2%=0.0548, 61.8%=0.0557, 100%=0.0572, 161.8%=0.0595, -61.8%=0.0510
2025-04-05 01:39:13,183 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.0069127400000000006, 0.00704726), SL=0.0067, TP=0.0076
2025-04-05 01:39:13,195 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 5.00/1.00 vs Threshold: 3.5
2025-04-05 01:39:13,195 DEBUG: (N/A) Last Close: 0.0114, Raw ATR: 0.0002, Valid ATR: 0.0002
2025-04-05 01:39:13,196 DEBUG: (N/A) Last Close: 0.0006, Raw ATR: 0.0000, Valid ATR: 0.0000
2025-04-05 01:39:13,196 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.05818612, 0.05881388), SL: 0.0570, TP: 0.0615
2025-04-05 01:39:13,196 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.054835419999999996, 0.05573458), SL: 0.0531, TP: 0.0595
2025-04-05 01:39:13,196 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:13,197 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:13,197 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:13,197 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:13,197 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:13,197 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:13,198 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:13,198 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:13,198 DEBUG: (N/A) Fib Levels: 0%=0.0107, 38.2%=0.0110, 61.8%=0.0112, 100%=0.0115, 161.8%=0.0120, -61.8%=0.0102
2025-04-05 01:39:13,198 DEBUG: (N/A) Fib Levels: 0%=0.0006, 38.2%=0.0006, 61.8%=0.0006, 100%=0.0006, 161.8%=0.0007, -61.8%=0.0006
2025-04-05 01:39:13,199 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.05818612, 0.05881388), SL=0.0570, TP=0.0615
2025-04-05 01:39:13,199 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.054835419999999996, 0.05573458), SL=0.0531, TP=0.0595
2025-04-05 01:39:13,199 INFO: N/A 1h: BUY signal, but price (0.0072) is outside entry zone (0.0069-0.0070). Penalty currently disabled for test.
2025-04-05 01:39:13,199 DEBUG: (N/A) Last Close: 0.0028, Raw ATR: 0.0001, Valid ATR: 0.0001
2025-04-05 01:39:13,199 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.010992914000000001, 0.011188086), SL: 0.0106, TP: 0.0120
2025-04-05 01:39:13,200 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.0006108384, 0.0006205616), SL: 0.0006, TP: 0.0007
2025-04-05 01:39:13,200 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:13,200 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:13,200 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.00/9.0 | Final Confidence: 44.4% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:13,201 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:13,201 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:13,201 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:13,201 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:13,201 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:13,203 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:13,203 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=10000WENUSDT HTTP/1.1" 200 192
2025-04-05 01:39:13,204 DEBUG: (N/A) Fib Levels: 0%=0.0024, 38.2%=0.0026, 61.8%=0.0027, 100%=0.0029, 161.8%=0.0032, -61.8%=0.0021
2025-04-05 01:39:13,204 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.010992914000000001, 0.011188086), SL=0.0106, TP=0.0120
2025-04-05 01:39:13,204 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.0006108384, 0.0006205616), SL=0.0006, TP=0.0007
2025-04-05 01:39:13,205 INFO: N/A 1h: BUY signal, but price (0.0590) is outside entry zone (0.0582-0.0588). Penalty currently disabled for test.
2025-04-05 01:39:13,205 INFO: N/A 1h: BUY signal, but price (0.0570) is outside entry zone (0.0548-0.0557). Penalty currently disabled for test.
2025-04-05 01:39:13,205 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000CATSUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806353203', 'recv_window': '120000', 'sign': '9f479f514d368703f4ca7e7f8fb7ca269782fa8bc3588d9a2c3c425bc35ea910'}
2025-04-05 01:39:13,206 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.002623382, 0.002741618), SL: 0.0024, TP: 0.0032
2025-04-05 01:39:13,207 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:13,207 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:13,207 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:13,207 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:13,208 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:13,208 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:13,209 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:13,209 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:13,209 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:13,210 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:13,211 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.002623382, 0.002741618), SL=0.0024, TP=0.0032
2025-04-05 01:39:13,211 INFO: N/A 1h: BUY signal, but price (0.0114) is outside entry zone (0.0110-0.0112). Penalty currently disabled for test.
2025-04-05 01:39:13,211 INFO: N/A 1h: BUY signal, but price (0.0006) is outside entry zone (0.0006-0.0006). Penalty currently disabled for test.
2025-04-05 01:39:13,211 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000LUNCUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806353209', 'recv_window': '120000', 'sign': 'e54d3ba76fb035fbf3fdbd26379c00b07ba0709454aaa8fdf41c2cf67ec90933'}
2025-04-05 01:39:13,211 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000FLOKIUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806353210', 'recv_window': '120000', 'sign': '223dfe19419f8b6722a945fefa664b83d3da877cf95c4fbb14e42190969da812'}
2025-04-05 01:39:13,212 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:13,212 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:13,212 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.00/9.0 | Final Confidence: 44.4% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:13,213 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:13,213 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:13,215 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:13,215 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:13,216 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:13,217 INFO: N/A 1h: BUY signal, but price (0.0028) is outside entry zone (0.0026-0.0027). Penalty currently disabled for test.
2025-04-05 01:39:13,218 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000BONKUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806353215', 'recv_window': '120000', 'sign': '355f6147df399a512a62c976d414a057ee6a0602b88265a7e2f544834673410b'}
2025-04-05 01:39:13,218 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000WHYUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806353216', 'recv_window': '120000', 'sign': '2a3f1c7c86e5a4d11d335762a2c9d9475e185eb7b8a315e498b7ee1413956f9f'}
2025-04-05 01:39:13,218 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 5.00/9.0 | Final Confidence: 55.6% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:13,219 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:13,220 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:13,221 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:13,221 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000MUMUUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806353221', 'recv_window': '120000', 'sign': 'daac4d12e634a86d70d4c103f3c2d9bc9461d2078c95e52a0f2f32e08a304c2e'}
2025-04-05 01:39:13,222 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:13,353 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000CATUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806353002&recv_window=120000&sign=9d3d4afd6506c1a0d3df29b12f9602f9810fe94efa0f5d1c9d02975584c0fed7 HTTP/1.1" 200 5464
2025-04-05 01:39:13,363 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000BTTUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806352976&recv_window=120000&sign=a6191bcb1fae86764f8a5fadcb5c099f467f60f75981082d1dd479ed2bc1b138 HTTP/1.1" 200 None
2025-04-05 01:39:13,388 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000APUUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806353070&recv_window=120000&sign=cf604ebdaf7a4fd56380b437b28bd6c6b6950f8587ca2c1f8227f598cd98f4bf HTTP/1.1" 200 None
2025-04-05 01:39:13,410 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=10000WENUSDT HTTP/1.1" 200 315
2025-04-05 01:39:13,427 DEBUG: (10000WENUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:13,433 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000CATSUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806353203&recv_window=120000&sign=9f479f514d368703f4ca7e7f8fb7ca269782fa8bc3588d9a2c3c425bc35ea910 HTTP/1.1" 200 None
2025-04-05 01:39:13,433 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000LUNCUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806353209&recv_window=120000&sign=e54d3ba76fb035fbf3fdbd26379c00b07ba0709454aaa8fdf41c2cf67ec90933 HTTP/1.1" 200 None
2025-04-05 01:39:13,435 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000FLOKIUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806353210&recv_window=120000&sign=223dfe19419f8b6722a945fefa664b83d3da877cf95c4fbb14e42190969da812 HTTP/1.1" 200 None
2025-04-05 01:39:13,435 DEBUG: (1000NEIROCTOUSDT) Starting analysis for 1h...
2025-04-05 01:39:13,437 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000BONKUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806353215&recv_window=120000&sign=355f6147df399a512a62c976d414a057ee6a0602b88265a7e2f544834673410b HTTP/1.1" 200 None
2025-04-05 01:39:13,441 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000WHYUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806353216&recv_window=120000&sign=2a3f1c7c86e5a4d11d335762a2c9d9475e185eb7b8a315e498b7ee1413956f9f HTTP/1.1" 200 None
2025-04-05 01:39:13,449 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000MUMUUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806353221&recv_window=120000&sign=daac4d12e634a86d70d4c103f3c2d9bc9461d2078c95e52a0f2f32e08a304c2e HTTP/1.1" 200 None
2025-04-05 01:39:13,497 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:13,557 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000NEIROCTOUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806353497', 'recv_window': '120000', 'sign': '798627964ddb71474aea3d47db5378749488811c1d1dbe866cf300d4b7d8fa3e'}
2025-04-05 01:39:13,636 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:13,701 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000CATUSDT HTTP/1.1" 200 196
2025-04-05 01:39:13,783 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000BTTUSDT HTTP/1.1" 200 191
2025-04-05 01:39:13,861 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000APUUSDT HTTP/1.1" 200 191
2025-04-05 01:39:13,880 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000NEIROCTOUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806353497&recv_window=120000&sign=798627964ddb71474aea3d47db5378749488811c1d1dbe866cf300d4b7d8fa3e HTTP/1.1" 200 None
2025-04-05 01:39:13,934 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/0.00 vs Threshold: 3.5
2025-04-05 01:39:13,934 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:13,935 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:13,936 DEBUG: (N/A) Last Close: 0.1786, Raw ATR: 0.0033, Valid ATR: 0.0033
2025-04-05 01:39:13,937 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:13,937 DEBUG: (N/A) Fib Levels: 0%=0.1676, 38.2%=0.1728, 61.8%=0.1761, 100%=0.1813, 161.8%=0.1898, -61.8%=0.1591
2025-04-05 01:39:13,938 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.1728334, 0.1760666), SL: 0.1668, TP: 0.1898
2025-04-05 01:39:13,938 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:13,938 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.1728334, 0.1760666), SL=0.1668, TP=0.1898
2025-04-05 01:39:13,939 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:13,939 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:13,939 INFO: N/A 1h: BUY signal, but price (0.1786) is outside entry zone (0.1728-0.1761). Penalty currently disabled for test.
2025-04-05 01:39:13,939 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:13,940 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:13,941 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000CATUSDT HTTP/1.1" 200 319
2025-04-05 01:39:13,941 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000NEIROCTOUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806353940', 'recv_window': '120000', 'sign': '2d831331991ba0ae6ba4b6b5f6bb77fb249ae1c2a8ea404dbaebcd77b87a09dc'}
2025-04-05 01:39:13,941 DEBUG: (1000CATUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:39:13,942 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:13,942 DEBUG: (1000PEPEUSDT) Starting analysis for 1h...
2025-04-05 01:39:13,943 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:13,943 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000PEPEUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806353943', 'recv_window': '120000', 'sign': 'd808bfaf2f782304e414a67fe43666410e73e7a5fa8bf8d21c1a2835bb6ee074'}
2025-04-05 01:39:13,944 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:14,026 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000BTTUSDT HTTP/1.1" 200 320
2025-04-05 01:39:14,027 DEBUG: (1000BTTUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:39:14,028 DEBUG: (1000RATSUSDT) Starting analysis for 1h...
2025-04-05 01:39:14,028 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:14,028 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000RATSUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806354028', 'recv_window': '120000', 'sign': '47eeaf5c99d97de5afe86992f2495e9ab6eeed69a299e64b811fc14dd3b4f207'}
2025-04-05 01:39:14,030 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:14,035 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000CATSUSDT HTTP/1.1" 200 192
2025-04-05 01:39:14,035 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000LUNCUSDT HTTP/1.1" 200 196
2025-04-05 01:39:14,042 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000FLOKIUSDT HTTP/1.1" 200 198
2025-04-05 01:39:14,048 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000MUMUUSDT HTTP/1.1" 200 192
2025-04-05 01:39:14,050 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000BONKUSDT HTTP/1.1" 200 193
2025-04-05 01:39:14,050 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=10000WHYUSDT HTTP/1.1" 200 192
2025-04-05 01:39:14,058 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000APUUSDT HTTP/1.1" 200 314
2025-04-05 01:39:14,059 DEBUG: (1000APUUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:14,059 DEBUG: (1000TOSHIUSDT) Starting analysis for 1h...
2025-04-05 01:39:14,060 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:14,060 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000TOSHIUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806354060', 'recv_window': '120000', 'sign': 'da8269582e80c91007daadec2f43f713ce9e0b81dda9ad9859a941a4752ddb5c'}
2025-04-05 01:39:14,061 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:14,160 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000PEPEUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806353943&recv_window=120000&sign=d808bfaf2f782304e414a67fe43666410e73e7a5fa8bf8d21c1a2835bb6ee074 HTTP/1.1" 200 None
2025-04-05 01:39:14,161 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000NEIROCTOUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806353940&recv_window=120000&sign=2d831331991ba0ae6ba4b6b5f6bb77fb249ae1c2a8ea404dbaebcd77b87a09dc HTTP/1.1" 200 None
2025-04-05 01:39:14,244 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000LUNCUSDT HTTP/1.1" 200 317
2025-04-05 01:39:14,244 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000CATSUSDT HTTP/1.1" 200 317
2025-04-05 01:39:14,249 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=10000WHYUSDT HTTP/1.1" 200 321
2025-04-05 01:39:14,251 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000FLOKIUSDT HTTP/1.1" 200 320
2025-04-05 01:39:14,251 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000RATSUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806354028&recv_window=120000&sign=47eeaf5c99d97de5afe86992f2495e9ab6eeed69a299e64b811fc14dd3b4f207 HTTP/1.1" 200 None
2025-04-05 01:39:14,252 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000BONKUSDT HTTP/1.1" 200 321
2025-04-05 01:39:14,255 DEBUG: (1000LUNCUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:14,264 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000MUMUUSDT HTTP/1.1" 200 319
2025-04-05 01:39:14,266 DEBUG: (1000CATSUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:14,271 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 5.00/0.00 vs Threshold: 3.5
2025-04-05 01:39:14,271 DEBUG: (10000WHYUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:14,275 DEBUG: (1000FLOKIUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:14,278 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000TOSHIUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806354060&recv_window=120000&sign=da8269582e80c91007daadec2f43f713ce9e0b81dda9ad9859a941a4752ddb5c HTTP/1.1" 200 None
2025-04-05 01:39:14,279 DEBUG: (1000BONKUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:14,284 DEBUG: (1000TURBOUSDT) Starting analysis for 1h...
2025-04-05 01:39:14,292 DEBUG: (1000MUMUUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:14,303 DEBUG: (1000XECUSDT) Starting analysis for 1h...
2025-04-05 01:39:14,318 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:14,321 DEBUG: (1000XUSDT) Starting analysis for 1h...
2025-04-05 01:39:14,327 DEBUG: (1INCHUSDT) Starting analysis for 1h...
2025-04-05 01:39:14,335 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.00/0.00 vs Threshold: 3.5
2025-04-05 01:39:14,339 DEBUG: (A8USDT) Starting analysis for 1h...
2025-04-05 01:39:14,354 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:14,377 DEBUG: (AAVEUSDT) Starting analysis for 1h...
2025-04-05 01:39:14,382 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:14,391 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/0.50 vs Threshold: 3.5
2025-04-05 01:39:14,392 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:14,392 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:14,392 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:14,393 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:14,393 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:14,393 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000TURBOUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806354354', 'recv_window': '120000', 'sign': 'bdaba57be1786d306c43dcedffc7ea1b1a578c7892b21405005cc698939622eb'}
2025-04-05 01:39:14,393 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:14,394 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000XECUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806354382', 'recv_window': '120000', 'sign': '7a8692ce923034fe9181eec36f3c5bdd339eb6b8ce82ae37ae52009569258e7c'}
2025-04-05 01:39:14,394 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:39:14,394 DEBUG: (N/A) Last Close: 0.0072, Raw ATR: 0.0002, Valid ATR: 0.0002
2025-04-05 01:39:14,394 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000XUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806354392', 'recv_window': '120000', 'sign': 'e8ce768a29d7e4c234678fc803705d40ff344846390b9786c8a51de5473b6758'}
2025-04-05 01:39:14,394 DEBUG: Request Params: {'category': 'linear', 'symbol': '1INCHUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806354392', 'recv_window': '120000', 'sign': 'c66c02479fcaf579538e8e3e4d5f074b49bff7af206a78c82b7dd85370f742f5'}
2025-04-05 01:39:14,395 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:14,395 DEBUG: Request Params: {'category': 'linear', 'symbol': 'A8USDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806354393', 'recv_window': '120000', 'sign': '3ee0ac3a95915b0529aa14ed6f1a0fb01249e9edb1f033a9fece9fc9ae69d9bf'}
2025-04-05 01:39:14,396 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:14,396 DEBUG: Request Params: {'category': 'linear', 'symbol': 'AAVEUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806354393', 'recv_window': '120000', 'sign': '43d3d7262917e39e632efe8de589875645f0a8982370486c722d985ae73c20a7'}
2025-04-05 01:39:14,397 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:14,398 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:14,398 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:14,399 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:14,400 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:14,400 DEBUG: (N/A) Last Close: 0.0310, Raw ATR: 0.0008, Valid ATR: 0.0008
2025-04-05 01:39:14,401 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:14,403 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:14,404 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000TOSHIUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806354398', 'recv_window': '120000', 'sign': 'ee654ea74e4d0b5b60e9d27f826f35d5301fa949f57c8cee54a2a82f96f6d36a'}
2025-04-05 01:39:14,404 DEBUG: (N/A) Fib Levels: 0%=0.0067, 38.2%=0.0069, 61.8%=0.0070, 100%=0.0073, 161.8%=0.0077, -61.8%=0.0063
2025-04-05 01:39:14,405 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:14,407 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:14,407 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.00690402, 0.00704798), SL: 0.0066, TP: 0.0077
2025-04-05 01:39:14,408 DEBUG: (N/A) Fib Levels: 0%=0.0288, 38.2%=0.0301, 61.8%=0.0310, 100%=0.0323, 161.8%=0.0345, -61.8%=0.0266
2025-04-05 01:39:14,408 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:14,408 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.030127, 0.030952999999999998), SL: 0.0286, TP: 0.0345
2025-04-05 01:39:14,409 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.00690402, 0.00704798), SL=0.0066, TP=0.0077
2025-04-05 01:39:14,409 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:14,409 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:14,409 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.030127, 0.030952999999999998), SL=0.0286, TP=0.0345
2025-04-05 01:39:14,409 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:14,409 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:14,410 INFO: N/A 1h: BUY signal, but price (0.0072) is outside entry zone (0.0069-0.0070). Penalty currently disabled for test.
2025-04-05 01:39:14,410 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:14,410 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 5.00/9.0 | Final Confidence: 55.6% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:14,410 INFO: N/A 1h: BUY signal, but price (0.0310) is outside entry zone (0.0301-0.0310). Penalty currently disabled for test.
2025-04-05 01:39:14,411 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:14,411 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.00/9.0 | Final Confidence: 44.4% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:14,412 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000PEPEUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806354411', 'recv_window': '120000', 'sign': 'c72d107f440a81aa8abc8f85b5920c246534000ad902683c21f973fe52d0bbf6'}
2025-04-05 01:39:14,412 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:14,414 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:14,414 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000RATSUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806354412', 'recv_window': '120000', 'sign': '28ac40c1e15d9455a70ec7ae56a2fb4c7237e58af0f303bb5ec51b344a305e45'}
2025-04-05 01:39:14,415 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:14,470 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000NEIROCTOUSDT HTTP/1.1" 200 196
2025-04-05 01:39:14,619 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000TURBOUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806354354&recv_window=120000&sign=bdaba57be1786d306c43dcedffc7ea1b1a578c7892b21405005cc698939622eb HTTP/1.1" 200 None
2025-04-05 01:39:14,626 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000XECUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806354382&recv_window=120000&sign=7a8692ce923034fe9181eec36f3c5bdd339eb6b8ce82ae37ae52009569258e7c HTTP/1.1" 200 None
2025-04-05 01:39:14,627 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=A8USDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806354393&recv_window=120000&sign=3ee0ac3a95915b0529aa14ed6f1a0fb01249e9edb1f033a9fece9fc9ae69d9bf HTTP/1.1" 200 5768
2025-04-05 01:39:14,628 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000TOSHIUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806354398&recv_window=120000&sign=ee654ea74e4d0b5b60e9d27f826f35d5301fa949f57c8cee54a2a82f96f6d36a HTTP/1.1" 200 None
2025-04-05 01:39:14,629 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=AAVEUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806354393&recv_window=120000&sign=43d3d7262917e39e632efe8de589875645f0a8982370486c722d985ae73c20a7 HTTP/1.1" 200 None
2025-04-05 01:39:14,629 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000XUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806354392&recv_window=120000&sign=e8ce768a29d7e4c234678fc803705d40ff344846390b9786c8a51de5473b6758 HTTP/1.1" 200 None
2025-04-05 01:39:14,630 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1INCHUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806354392&recv_window=120000&sign=c66c02479fcaf579538e8e3e4d5f074b49bff7af206a78c82b7dd85370f742f5 HTTP/1.1" 200 None
2025-04-05 01:39:14,631 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000PEPEUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806354411&recv_window=120000&sign=c72d107f440a81aa8abc8f85b5920c246534000ad902683c21f973fe52d0bbf6 HTTP/1.1" 200 None
2025-04-05 01:39:14,673 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000NEIROCTOUSDT HTTP/1.1" 200 321
2025-04-05 01:39:14,686 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000RATSUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806354412&recv_window=120000&sign=28ac40c1e15d9455a70ec7ae56a2fb4c7237e58af0f303bb5ec51b344a305e45 HTTP/1.1" 200 None
2025-04-05 01:39:14,738 DEBUG: (1000NEIROCTOUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:14,806 DEBUG: (ACEUSDT) Starting analysis for 1h...
2025-04-05 01:39:14,831 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:14,851 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ACEUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806354831', 'recv_window': '120000', 'sign': 'bb5013c54bd5f5a4dde0b473c665b8f53c32d3a4836d2c09ea353b27d09118fd'}
2025-04-05 01:39:14,862 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/0.00 vs Threshold: 3.5
2025-04-05 01:39:14,868 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:14,869 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:39:15,012 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:15,052 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000TURBOUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806355012', 'recv_window': '120000', 'sign': '0bcbe7e92804a1b204b7d0a9c17e5cb028969f10775a6bd5a0cb4980a627802d'}
2025-04-05 01:39:15,066 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/1.00 vs Threshold: 3.5
2025-04-05 01:39:15,085 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/0.00 vs Threshold: 3.5
2025-04-05 01:39:15,089 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/0.00 vs Threshold: 3.5
2025-04-05 01:39:15,092 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/0.00 vs Threshold: 3.5
2025-04-05 01:39:15,094 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:15,099 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/0.00 vs Threshold: 3.5
2025-04-05 01:39:15,099 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:39:15,116 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:39:15,125 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:39:15,131 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:15,132 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:15,133 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:15,134 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:15,134 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:15,135 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:15,135 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:15,135 DEBUG: Request Params: {'category': 'linear', 'symbol': 'A8USDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806355133', 'recv_window': '120000', 'sign': '2cca5a5366bcedb059099d9a45cff308270047129ff714c88bfd2234a68ea9fe'}
2025-04-05 01:39:15,135 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000XUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806355134', 'recv_window': '120000', 'sign': '05fb264b069a438d8f277a2dd539db169bda640a2f0c126a3540485eb74702bf'}
2025-04-05 01:39:15,136 DEBUG: Request Params: {'category': 'linear', 'symbol': 'AAVEUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806355134', 'recv_window': '120000', 'sign': 'a5556ee0ab76dd71f58be2d4cca1bbeaa7a54274080421f6d16ef98ebb1058b0'}
2025-04-05 01:39:15,136 DEBUG: (N/A) Last Close: 0.0197, Raw ATR: 0.0002, Valid ATR: 0.0002
2025-04-05 01:39:15,136 DEBUG: (N/A) Last Close: 0.1879, Raw ATR: 0.0026, Valid ATR: 0.0026
2025-04-05 01:39:15,138 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:15,139 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:15,140 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:15,140 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:15,140 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:15,141 DEBUG: (N/A) Fib Levels: 0%=0.0189, 38.2%=0.0192, 61.8%=0.0194, 100%=0.0198, 161.8%=0.0203, -61.8%=0.0184
2025-04-05 01:39:15,141 DEBUG: (N/A) Fib Levels: 0%=0.1782, 38.2%=0.1821, 61.8%=0.1845, 100%=0.1884, 161.8%=0.1947, -61.8%=0.1719
2025-04-05 01:39:15,141 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.01922234, 0.01942766), SL: 0.0188, TP: 0.0203
2025-04-05 01:39:15,142 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.1820964, 0.18450360000000002), SL: 0.1776, TP: 0.1947
2025-04-05 01:39:15,142 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:15,142 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:15,142 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.01922234, 0.01942766), SL=0.0188, TP=0.0203
2025-04-05 01:39:15,142 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.1820964, 0.18450360000000002), SL=0.1776, TP=0.1947
2025-04-05 01:39:15,142 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:15,143 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:15,143 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:15,143 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:15,143 INFO: N/A 1h: BUY signal, but price (0.0197) is outside entry zone (0.0192-0.0194). Penalty currently disabled for test.
2025-04-05 01:39:15,144 INFO: N/A 1h: BUY signal, but price (0.1879) is outside entry zone (0.1821-0.1845). Penalty currently disabled for test.
2025-04-05 01:39:15,144 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:15,144 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:15,145 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:15,145 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:15,145 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000XECUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806355145', 'recv_window': '120000', 'sign': 'd91f1d21010ba961a424baf6475f079e774824cfa885354308bfd697ae538023'}
2025-04-05 01:39:15,146 DEBUG: Request Params: {'category': 'linear', 'symbol': '1INCHUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806355145', 'recv_window': '120000', 'sign': '3edf2c751c56d03b4423083ec4f1cef317142ae732596f1a32727686e05c9ac4'}
2025-04-05 01:39:15,147 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:15,148 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:15,245 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ACEUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806354831&recv_window=120000&sign=bb5013c54bd5f5a4dde0b473c665b8f53c32d3a4836d2c09ea353b27d09118fd HTTP/1.1" 200 None
2025-04-05 01:39:15,294 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/1.00 vs Threshold: 3.5
2025-04-05 01:39:15,295 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:39:15,296 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:15,297 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ACEUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806355296', 'recv_window': '120000', 'sign': '4c29700099e46580481b14678b30489d9f1d5793c484a4cdb40678b099c0d995'}
2025-04-05 01:39:15,298 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:15,321 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000PEPEUSDT HTTP/1.1" 200 197
2025-04-05 01:39:15,327 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000RATSUSDT HTTP/1.1" 200 193
2025-04-05 01:39:15,348 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000TURBOUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806355012&recv_window=120000&sign=0bcbe7e92804a1b204b7d0a9c17e5cb028969f10775a6bd5a0cb4980a627802d HTTP/1.1" 200 None
2025-04-05 01:39:15,358 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=A8USDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806355133&recv_window=120000&sign=2cca5a5366bcedb059099d9a45cff308270047129ff714c88bfd2234a68ea9fe HTTP/1.1" 200 None
2025-04-05 01:39:15,362 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=AAVEUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806355134&recv_window=120000&sign=a5556ee0ab76dd71f58be2d4cca1bbeaa7a54274080421f6d16ef98ebb1058b0 HTTP/1.1" 200 None
2025-04-05 01:39:15,367 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000XECUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806355145&recv_window=120000&sign=d91f1d21010ba961a424baf6475f079e774824cfa885354308bfd697ae538023 HTTP/1.1" 200 None
2025-04-05 01:39:15,368 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1INCHUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806355145&recv_window=120000&sign=3edf2c751c56d03b4423083ec4f1cef317142ae732596f1a32727686e05c9ac4 HTTP/1.1" 200 None
2025-04-05 01:39:15,519 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ACEUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806355296&recv_window=120000&sign=4c29700099e46580481b14678b30489d9f1d5793c484a4cdb40678b099c0d995 HTTP/1.1" 200 None
2025-04-05 01:39:15,520 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000PEPEUSDT HTTP/1.1" 200 323
2025-04-05 01:39:15,532 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000RATSUSDT HTTP/1.1" 200 320
2025-04-05 01:39:15,545 DEBUG: (1000PEPEUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:15,562 DEBUG: (1000RATSUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:15,581 DEBUG: (ACHUSDT) Starting analysis for 1h...
2025-04-05 01:39:15,602 DEBUG: (ACTUSDT) Starting analysis for 1h...
2025-04-05 01:39:15,607 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:15,619 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:15,642 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000TOSHIUSDT HTTP/1.1" 200 194
2025-04-05 01:39:15,646 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ACHUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806355607', 'recv_window': '120000', 'sign': 'bc7cb1c810e904cd99fca08776facbbe87b64dcf9abd6c1b6e3d1147955d293c'}
2025-04-05 01:39:15,648 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ACTUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806355619', 'recv_window': '120000', 'sign': '1cae21d903a43e1f40a99e51ee1ddce940aa599471de68ef5a2c661bd8c5bc3c'}
2025-04-05 01:39:15,681 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:15,687 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:15,716 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000XUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806355134&recv_window=120000&sign=05fb264b069a438d8f277a2dd539db169bda640a2f0c126a3540485eb74702bf HTTP/1.1" 200 5127
2025-04-05 01:39:15,717 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000TURBOUSDT HTTP/1.1" 200 193
2025-04-05 01:39:15,836 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=AAVEUSDT HTTP/1.1" 200 193
2025-04-05 01:39:15,844 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1INCHUSDT HTTP/1.1" 200 194
2025-04-05 01:39:15,849 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=A8USDT HTTP/1.1" 200 187
2025-04-05 01:39:15,868 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000TOSHIUSDT HTTP/1.1" 200 316
2025-04-05 01:39:15,869 DEBUG: (1000TOSHIUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:39:15,870 DEBUG: (ACXUSDT) Starting analysis for 1h...
2025-04-05 01:39:15,870 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:15,871 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ACXUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806355870', 'recv_window': '120000', 'sign': 'db76b9c22d3d3d312b16e99b7d9166e3d63e6d8284aacebcc4af4daae39b9b71'}
2025-04-05 01:39:15,873 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:15,884 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000XECUSDT HTTP/1.1" 200 196
2025-04-05 01:39:15,886 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=ACEUSDT HTTP/1.1" 200 188
2025-04-05 01:39:15,904 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ACHUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806355607&recv_window=120000&sign=bc7cb1c810e904cd99fca08776facbbe87b64dcf9abd6c1b6e3d1147955d293c HTTP/1.1" 200 6129
2025-04-05 01:39:15,910 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ACTUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806355619&recv_window=120000&sign=1cae21d903a43e1f40a99e51ee1ddce940aa599471de68ef5a2c661bd8c5bc3c HTTP/1.1" 200 None
2025-04-05 01:39:15,951 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000TURBOUSDT HTTP/1.1" 200 314
2025-04-05 01:39:15,958 DEBUG: (1000TURBOUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:39:15,961 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000XUSDT HTTP/1.1" 200 190
2025-04-05 01:39:15,961 DEBUG: (ADAUSDT) Starting analysis for 1h...
2025-04-05 01:39:16,001 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:16,014 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/1.00 vs Threshold: 3.5
2025-04-05 01:39:16,016 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ADAUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806356001', 'recv_window': '120000', 'sign': '7d9b0f3620afb9effd6af9de62e1bdae8c9982b6e328629584ff9d0ca764288f'}
2025-04-05 01:39:16,020 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.50/0.00 vs Threshold: 3.5
2025-04-05 01:39:16,020 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:39:16,022 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:16,022 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:16,022 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:16,023 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:16,023 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ACTUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806356022', 'recv_window': '120000', 'sign': '31f7a5d7095f762bd81e33757d135aa242035a95f07b196b12f32025841422fb'}
2025-04-05 01:39:16,023 DEBUG: (N/A) Last Close: 0.0213, Raw ATR: 0.0003, Valid ATR: 0.0003
2025-04-05 01:39:16,024 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:16,024 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:16,025 DEBUG: (N/A) Fib Levels: 0%=0.0203, 38.2%=0.0208, 61.8%=0.0211, 100%=0.0216, 161.8%=0.0224, -61.8%=0.0196
2025-04-05 01:39:16,025 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.020837106, 0.021139894), SL: 0.0203, TP: 0.0224
2025-04-05 01:39:16,025 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:16,026 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.020837106, 0.021139894), SL=0.0203, TP=0.0224
2025-04-05 01:39:16,026 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:16,026 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:16,027 INFO: N/A 1h: BUY signal, but price (0.0213) is outside entry zone (0.0208-0.0211). Penalty currently disabled for test.
2025-04-05 01:39:16,027 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.50/9.0 | Final Confidence: 50.0% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:16,028 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:16,028 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ACHUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806356028', 'recv_window': '120000', 'sign': '6be1c41a05c7b887fddfc80badf61efeee830b8f20a5eda94ec763b7751aea34'}
2025-04-05 01:39:16,030 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:16,037 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=AAVEUSDT HTTP/1.1" 200 309
2025-04-05 01:39:16,038 DEBUG: (AAVEUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:39:16,039 DEBUG: (AERGOUSDT) Starting analysis for 1h...
2025-04-05 01:39:16,039 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:16,039 DEBUG: Request Params: {'category': 'linear', 'symbol': 'AERGOUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806356039', 'recv_window': '120000', 'sign': '54a6ced0bf6da061c9c3a37aede9a6f979b47f59f5a98d9dec6a986dbeb6e94a'}
2025-04-05 01:39:16,040 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:16,046 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1INCHUSDT HTTP/1.1" 200 315
2025-04-05 01:39:16,047 DEBUG: (1INCHUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:16,047 DEBUG: (AEROUSDT) Starting analysis for 1h...
2025-04-05 01:39:16,048 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:16,048 DEBUG: Request Params: {'category': 'linear', 'symbol': 'AEROUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806356048', 'recv_window': '120000', 'sign': '0469c15b66478dd39fdf2c53c32c33dc9c8d9267b5981d3072c3840d7b6c701a'}
2025-04-05 01:39:16,049 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:16,053 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=A8USDT HTTP/1.1" 200 311
2025-04-05 01:39:16,053 DEBUG: (A8USDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:39:16,054 DEBUG: (AEVOUSDT) Starting analysis for 1h...
2025-04-05 01:39:16,054 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:16,055 DEBUG: Request Params: {'category': 'linear', 'symbol': 'AEVOUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806356054', 'recv_window': '120000', 'sign': '3ef9716d5162f4aedff81559b348c809fdf3b7aaa00b8912fb4c4f5dd22958cf'}
2025-04-05 01:39:16,056 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:16,092 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=ACEUSDT HTTP/1.1" 200 310
2025-04-05 01:39:16,093 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ACXUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806355870&recv_window=120000&sign=db76b9c22d3d3d312b16e99b7d9166e3d63e6d8284aacebcc4af4daae39b9b71 HTTP/1.1" 200 None
2025-04-05 01:39:16,095 DEBUG: (ACEUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:39:16,160 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/0.00 vs Threshold: 3.5
2025-04-05 01:39:16,161 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:39:16,162 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:16,162 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ACXUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806356162', 'recv_window': '120000', 'sign': 'b185ff5ed3d6599dde0d56aaca5cfc3b7088eaccea223233b2716f27bd82aff8'}
2025-04-05 01:39:16,163 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:16,190 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000XUSDT HTTP/1.1" 200 315
2025-04-05 01:39:16,193 DEBUG: (1000XUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:39:16,240 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ADAUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806356001&recv_window=120000&sign=7d9b0f3620afb9effd6af9de62e1bdae8c9982b6e328629584ff9d0ca764288f HTTP/1.1" 200 None
2025-04-05 01:39:16,241 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ACTUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806356022&recv_window=120000&sign=31f7a5d7095f762bd81e33757d135aa242035a95f07b196b12f32025841422fb HTTP/1.1" 200 None
2025-04-05 01:39:16,249 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ACHUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806356028&recv_window=120000&sign=6be1c41a05c7b887fddfc80badf61efeee830b8f20a5eda94ec763b7751aea34 HTTP/1.1" 200 5365
2025-04-05 01:39:16,265 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=AERGOUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806356039&recv_window=120000&sign=54a6ced0bf6da061c9c3a37aede9a6f979b47f59f5a98d9dec6a986dbeb6e94a HTTP/1.1" 200 None
2025-04-05 01:39:16,267 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=AEROUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806356048&recv_window=120000&sign=0469c15b66478dd39fdf2c53c32c33dc9c8d9267b5981d3072c3840d7b6c701a HTTP/1.1" 200 None
2025-04-05 01:39:16,273 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=AEVOUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806356054&recv_window=120000&sign=3ef9716d5162f4aedff81559b348c809fdf3b7aaa00b8912fb4c4f5dd22958cf HTTP/1.1" 200 5655
2025-04-05 01:39:16,385 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ACXUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806356162&recv_window=120000&sign=b185ff5ed3d6599dde0d56aaca5cfc3b7088eaccea223233b2716f27bd82aff8 HTTP/1.1" 200 4699
2025-04-05 01:39:16,404 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/0.00 vs Threshold: 3.5
2025-04-05 01:39:16,411 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:16,439 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:16,462 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000XECUSDT HTTP/1.1" 200 316
2025-04-05 01:39:16,466 DEBUG: (N/A) Last Close: 0.6625, Raw ATR: 0.0104, Valid ATR: 0.0104
2025-04-05 01:39:16,480 DEBUG: (1000XECUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:16,488 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:16,545 DEBUG: (N/A) Fib Levels: 0%=0.6271, 38.2%=0.6440, 61.8%=0.6545, 100%=0.6714, 161.8%=0.6988, -61.8%=0.5997
2025-04-05 01:39:16,561 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.6440226, 0.6544774), SL: 0.6245, TP: 0.6988
2025-04-05 01:39:16,570 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/0.00 vs Threshold: 3.5
2025-04-05 01:39:16,580 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:16,583 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:39:16,596 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/0.50 vs Threshold: 3.5
2025-04-05 01:39:16,608 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.6440226, 0.6544774), SL=0.6245, TP=0.6988
2025-04-05 01:39:16,611 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/0.50 vs Threshold: 3.5
2025-04-05 01:39:16,621 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:39:16,626 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:39:16,627 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:16,627 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:39:16,627 DEBUG: (N/A) Last Close: 0.1000, Raw ATR: 0.0018, Valid ATR: 0.0018
2025-04-05 01:39:16,628 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:16,628 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:16,629 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:16,629 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:39:16,629 DEBUG: Request Params: {'category': 'linear', 'symbol': 'AERGOUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806356628', 'recv_window': '120000', 'sign': '0add3f405ae5949441f2f164b4d26183b027e6cc1c3eb8f10e4ec1727286bdc6'}
2025-04-05 01:39:16,629 INFO: N/A 1h: BUY signal, but price (0.6625) is outside entry zone (0.6440-0.6545). Penalty currently disabled for test.
2025-04-05 01:39:16,630 DEBUG: Request Params: {'category': 'linear', 'symbol': 'AEROUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806356629', 'recv_window': '120000', 'sign': '614972c06ccdd09c775b903a5828b5a569b6d26d643b7bdae98eab47b566f561'}
2025-04-05 01:39:16,630 DEBUG: (N/A) Fib Levels: 0%=0.0952, 38.2%=0.0975, 61.8%=0.0989, 100%=0.1012, 161.8%=0.1049, -61.8%=0.0915
2025-04-05 01:39:16,631 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:16,631 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:16,632 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.09749200000000001, 0.098908), SL: 0.0948, TP: 0.1049
2025-04-05 01:39:16,633 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:16,634 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:16,634 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:39:16,634 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ADAUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806356634', 'recv_window': '120000', 'sign': '311fc62a99a703a2700651f237f6075b7b9ed3628917bae8646e40fc6bd60daf'}
2025-04-05 01:39:16,634 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.09749200000000001, 0.098908), SL=0.0948, TP=0.1049
2025-04-05 01:39:16,635 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:16,636 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:39:16,636 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:39:16,636 INFO: N/A 1h: BUY signal, but price (0.1000) is outside entry zone (0.0975-0.0989). Penalty currently disabled for test.
2025-04-05 01:39:16,637 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:39:16,637 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:39:16,638 DEBUG: Request Params: {'category': 'linear', 'symbol': 'AEVOUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806356637', 'recv_window': '120000', 'sign': '27ec4dff2855398b6fb8705e7a3e6511123afc300c15cfdd093d097767548339'}
2025-04-05 01:39:16,639 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:16,689 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=ACTUSDT HTTP/1.1" 200 188
2025-04-05 01:39:16,734 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=ACHUSDT HTTP/1.1" 200 191
2025-04-05 01:39:16,822 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=ACXUSDT HTTP/1.1" 200 188
2025-04-05 01:39:16,851 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=AEROUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806356629&recv_window=120000&sign=614972c06ccdd09c775b903a5828b5a569b6d26d643b7bdae98eab47b566f561 HTTP/1.1" 200 None
2025-04-05 01:39:16,854 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=AERGOUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806356628&recv_window=120000&sign=0add3f405ae5949441f2f164b4d26183b027e6cc1c3eb8f10e4ec1727286bdc6 HTTP/1.1" 200 None
2025-04-05 01:39:16,855 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ADAUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806356634&recv_window=120000&sign=311fc62a99a703a2700651f237f6075b7b9ed3628917bae8646e40fc6bd60daf HTTP/1.1" 200 None
2025-04-05 01:39:16,858 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=AEVOUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806356637&recv_window=120000&sign=27ec4dff2855398b6fb8705e7a3e6511123afc300c15cfdd093d097767548339 HTTP/1.1" 200 None
2025-04-05 01:39:16,887 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=ACTUSDT HTTP/1.1" 200 314
2025-04-05 01:39:16,894 DEBUG: (ACTUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:39:16,932 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=ACHUSDT HTTP/1.1" 200 312
2025-04-05 01:39:16,948 DEBUG: (ACHUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:17,033 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=ACXUSDT HTTP/1.1" 200 310
2025-04-05 01:39:17,042 DEBUG: (ACXUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:39:17,202 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=AEROUSDT HTTP/1.1" 200 189
2025-04-05 01:39:17,259 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=AERGOUSDT HTTP/1.1" 200 190
2025-04-05 01:39:17,267 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=ADAUSDT HTTP/1.1" 200 192
2025-04-05 01:39:17,269 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=AEVOUSDT HTTP/1.1" 200 193
2025-04-05 01:39:17,410 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=AEROUSDT HTTP/1.1" 200 313
2025-04-05 01:39:17,411 DEBUG: (AEROUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:39:17,459 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=AERGOUSDT HTTP/1.1" 200 315
2025-04-05 01:39:17,463 DEBUG: (AERGOUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:39:17,469 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=ADAUSDT HTTP/1.1" 200 315
2025-04-05 01:39:17,469 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=AEVOUSDT HTTP/1.1" 200 313
2025-04-05 01:39:17,471 DEBUG: (ADAUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:17,475 DEBUG: (AEVOUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:39:17,476 INFO: Status: Analysis complete - 38/38 processed.
2025-04-05 01:39:20,299 DEBUG: Filter 'WAVE_X' set to True
2025-04-05 01:39:20,300 DEBUG: apply_live_filters CALLED
2025-04-05 01:39:20,301 DEBUG: apply_live_filters: Found 38 valid results, 38 after sorting.
2025-04-05 01:39:20,301 DEBUG: Adding row for 10000QUBICUSDT
2025-04-05 01:39:20,302 DEBUG: apply_live_filters FINISHED
2025-04-05 01:39:25,741 INFO: Buy btn for: 10000QUBICUSDT
2025-04-05 01:39:25,742 INFO: Using direct Bybit client for order placement
2025-04-05 01:39:25,742 INFO: Using leverage 10x for 10000QUBICUSDT (persistent setting on Bybit)
2025-04-05 01:39:25,743 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:25,964 DEBUG: https://api.bybit.com:443 "GET /v5/market/instruments-info?category=linear&symbol=10000QUBICUSDT HTTP/1.1" 200 907
2025-04-05 01:39:25,965 INFO: Formatted quantity: 1020.0 (min: 10.0, step: 10.0)
2025-04-05 01:39:25,965 INFO: Placing Buy order for 10000QUBICUSDT: 1020.0 @ market price with TP: 0.01078984, SL: 0.009309671412845418
2025-04-05 01:39:25,966 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:26,181 DEBUG: https://api.bybit.com:443 "GET /v5/market/instruments-info?category=linear&symbol=10000QUBICUSDT HTTP/1.1" 200 907
2025-04-05 01:39:26,182 INFO: Symbol 10000QUBICUSDT found in linear futures category
2025-04-05 01:39:26,182 INFO: Minimum order quantity for 10000QUBICUSDT: 10
2025-04-05 01:39:26,183 INFO: Price tick size for 10000QUBICUSDT: 0.000001
2025-04-05 01:39:26,183 INFO: Using category 'linear' for 10000QUBICUSDT (USDT perpetual futures)
2025-04-05 01:39:26,183 INFO: Using default position mode for 10000QUBICUSDT (letting API determine positionIdx)
2025-04-05 01:39:26,183 INFO: Order parameters: {'category': 'linear', 'symbol': '10000QUBICUSDT', 'side': 'Buy', 'orderType': 'Market', 'qty': '1020.0', 'takeProfit': '0.01078984', 'stopLoss': '0.009309671412845418', 'timeInForce': 'GTC', 'reduceOnly': False}
2025-04-05 01:39:26,184 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:26,402 DEBUG: https://api.bybit.com:443 "GET /v5/market/time HTTP/1.1" 200 134
2025-04-05 01:39:26,403 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:26,625 DEBUG: https://api.bybit.com:443 "POST /v5/position/switch-mode HTTP/1.1" 200 108
2025-04-05 01:39:26,625 ERROR: Failed to set position mode: Position mode is not modified
2025-04-05 01:39:26,627 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:26,846 DEBUG: https://api.bybit.com:443 "GET /v5/market/instruments-info?category=linear&symbol=10000QUBICUSDT HTTP/1.1" 200 907
2025-04-05 01:39:26,848 INFO: Formatted quantity from 1020.0 to 1020.0 (min: 10.0, step: 10.0)
2025-04-05 01:39:26,849 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:27,066 DEBUG: https://api.bybit.com:443 "GET /v5/market/time HTTP/1.1" 200 134
2025-04-05 01:39:27,067 DEBUG: Request URL: https://api.bybit.com/v5/order/create
2025-04-05 01:39:27,067 DEBUG: Request Headers: {'X-BAPI-API-KEY': 'aMKaaFNd57yeENDYF1', 'X-BAPI-SIGN': '26e1d106938db9458212a768156b5f6abbeff077b239252b36d8c1198ea90831', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-TIMESTAMP': '1743806363000', 'X-BAPI-RECV-WINDOW': '120000', 'Content-Type': 'application/json'}
2025-04-05 01:39:27,067 DEBUG: Request Body: {"category": "linear", "symbol": "10000QUBICUSDT", "side": "Buy", "orderType": "Market", "qty": "1020.0", "takeProfit": "0.01078984", "stopLoss": "0.009309671412845418", "timeInForce": "GTC", "reduceOnly": false}
2025-04-05 01:39:27,068 DEBUG: Signature String: 1743806363000aMKaaFNd57yeENDYF1120000{"category": "linear", "symbol": "10000QUBICUSDT", "side": "Buy", "orderType": "Market", "qty": "1020.0", "takeProfit": "0.01078984", "stopLoss": "0.009309671412845418", "timeInForce": "GTC", "reduceOnly": false}
2025-04-05 01:39:27,069 DEBUG: Request URL: https://api.bybit.com/v5/order/create
2025-04-05 01:39:27,069 DEBUG: Request Headers: {'X-BAPI-API-KEY': 'aMKaaFNd57yeENDYF1', 'X-BAPI-SIGN': '26e1d106938db9458212a768156b5f6abbeff077b239252b36d8c1198ea90831', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-TIMESTAMP': '1743806363000', 'X-BAPI-RECV-WINDOW': '120000', 'Content-Type': 'application/json'}
2025-04-05 01:39:27,069 DEBUG: Request Body: {"category": "linear", "symbol": "10000QUBICUSDT", "side": "Buy", "orderType": "Market", "qty": "1020.0", "takeProfit": "0.01078984", "stopLoss": "0.009309671412845418", "timeInForce": "GTC", "reduceOnly": false}
2025-04-05 01:39:27,070 DEBUG: Signature String: 1743806363000aMKaaFNd57yeENDYF1120000{"category": "linear", "symbol": "10000QUBICUSDT", "side": "Buy", "orderType": "Market", "qty": "1020.0", "takeProfit": "0.01078984", "stopLoss": "0.009309671412845418", "timeInForce": "GTC", "reduceOnly": false}
2025-04-05 01:39:27,071 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:27,292 DEBUG: https://api.bybit.com:443 "POST /v5/order/create HTTP/1.1" 200 114
2025-04-05 01:39:27,293 WARNING: Position index None not matching position mode. Trying another index.
2025-04-05 01:39:27,293 INFO: Trying with positionIdx=0
2025-04-05 01:39:27,293 DEBUG: Request URL: https://api.bybit.com/v5/order/create
2025-04-05 01:39:27,294 DEBUG: Request Headers: {'X-BAPI-API-KEY': 'aMKaaFNd57yeENDYF1', 'X-BAPI-SIGN': 'cf6774bfbc4ede065734f89a1811f8a1cdfa2fc5d2754ea6b58a9525e891ef29', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-TIMESTAMP': '1743806363000', 'X-BAPI-RECV-WINDOW': '120000', 'Content-Type': 'application/json'}
2025-04-05 01:39:27,294 DEBUG: Request Body: {"category": "linear", "symbol": "10000QUBICUSDT", "side": "Buy", "orderType": "Market", "qty": "1020.0", "takeProfit": "0.01078984", "stopLoss": "0.009309671412845418", "timeInForce": "GTC", "reduceOnly": false, "positionIdx": 0}
2025-04-05 01:39:27,294 DEBUG: Signature String: 1743806363000aMKaaFNd57yeENDYF1120000{"category": "linear", "symbol": "10000QUBICUSDT", "side": "Buy", "orderType": "Market", "qty": "1020.0", "takeProfit": "0.01078984", "stopLoss": "0.009309671412845418", "timeInForce": "GTC", "reduceOnly": false, "positionIdx": 0}
2025-04-05 01:39:27,295 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:27,518 DEBUG: https://api.bybit.com:443 "POST /v5/order/create HTTP/1.1" 200 114
2025-04-05 01:39:27,519 WARNING: Position index 0 not matching position mode. Trying another index.
2025-04-05 01:39:27,519 INFO: Trying with positionIdx=1
2025-04-05 01:39:27,520 DEBUG: Request URL: https://api.bybit.com/v5/order/create
2025-04-05 01:39:27,520 DEBUG: Request Headers: {'X-BAPI-API-KEY': 'aMKaaFNd57yeENDYF1', 'X-BAPI-SIGN': 'e4dd195707fb4dee172c72d2d9016b2ef8f6f892b0e1c30571d04e8a2829662f', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-TIMESTAMP': '1743806363000', 'X-BAPI-RECV-WINDOW': '120000', 'Content-Type': 'application/json'}
2025-04-05 01:39:27,520 DEBUG: Request Body: {"category": "linear", "symbol": "10000QUBICUSDT", "side": "Buy", "orderType": "Market", "qty": "1020.0", "takeProfit": "0.01078984", "stopLoss": "0.009309671412845418", "timeInForce": "GTC", "reduceOnly": false, "positionIdx": 1}
2025-04-05 01:39:27,521 DEBUG: Signature String: 1743806363000aMKaaFNd57yeENDYF1120000{"category": "linear", "symbol": "10000QUBICUSDT", "side": "Buy", "orderType": "Market", "qty": "1020.0", "takeProfit": "0.01078984", "stopLoss": "0.009309671412845418", "timeInForce": "GTC", "reduceOnly": false, "positionIdx": 1}
2025-04-05 01:39:27,522 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:27,742 DEBUG: https://api.bybit.com:443 "POST /v5/order/create HTTP/1.1" 200 141
2025-04-05 01:39:27,743 INFO: Order placed successfully with positionIdx=1
2025-04-05 01:39:27,744 INFO: Order placed successfully for 10000QUBICUSDT: {'orderId': '37aeb153-71ff-43af-b890-18582e8c0f55', 'orderLinkId': ''}
2025-04-05 01:39:27,745 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:27,963 DEBUG: https://api.bybit.com:443 "GET /v5/market/tickers?category=linear&symbol=10000QUBICUSDT HTTP/1.1" 200 732
2025-04-05 01:39:27,964 INFO: Saved trade data to file for 10000QUBICUSDT
2025-04-05 01:39:27,965 INFO: Stored trade data for 10000QUBICUSDT: {'entry_price': '0.009805999999999999', 'size': '1020.0', 'side': 'Buy', 'leverage': '10', 'amount_usd': '100.0', 'risk_amount': '10.0', 'risk_percent': '10.0', 'take_profit': '0.01078984', 'stop_loss': '0.009309671412845418', 'tp_percent': '100.33', 'sl_percent': '50.61', 'created_time': 1743806367.744111, 'created_time_str': '2025-04-05 01:39:27', 'day_of_week': 'Saturday', 'hour_of_day': '01', 'order_id': '37aeb153-71ff-43af-b890-18582e8c0f55', 'order_link_id': '', 'market_data': {'last_price': '0.010057', 'mark_price': '0.010059', 'index_price': '0.010065', 'high_price_24h': '0.010246', 'low_price_24h': '0.009366', 'volume_24h': '64631370.0000', 'turnover_24h': '634855.3123', 'price_change_percent_24h': '0.032758'}}
2025-04-05 01:39:27,966 INFO: Trade executed for 10000QUBICUSDT:
Side: Buy
Entry: 0.0098
SL: 0.0093
TP: 0.0108
Leverage: 10x
Position: $100 (Cost: $10)
Order ID: 37aeb153-71ff-43af-b890-18582e8c0f55
2025-04-05 01:39:36,932 DEBUG: Fetching fresh positions data from API
2025-04-05 01:39:36,933 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:37,153 DEBUG: https://api.bybit.com:443 "GET /v5/market/time HTTP/1.1" 200 134
2025-04-05 01:39:37,154 DEBUG: Request URL: https://api.bybit.com/v5/position/list
2025-04-05 01:39:37,154 DEBUG: Request Params: {'category': 'linear', 'settleCoin': 'USDT', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': 1743806373000, 'recv_window': 120000, 'sign': '107dfd02d272b2f15969cf654607071f606ad9046b2b4ab1fc4f4bb1df0449d1'}
2025-04-05 01:39:37,156 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:39:37,383 DEBUG: https://api.bybit.com:443 "GET /v5/position/list?category=linear&settleCoin=USDT&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806373000&recv_window=120000&sign=107dfd02d272b2f15969cf654607071f606ad9046b2b4ab1fc4f4bb1df0449d1 HTTP/1.1" 200 880
2025-04-05 01:39:37,389 INFO: Saved 1 positions as last known positions
2025-04-05 01:39:37,390 DEBUG: Using cached positions data (age: 0.5s)
2025-04-05 01:39:37,393 DEBUG: Saved 1 positions to last_positions.json
2025-04-05 01:39:39,853 INFO: Filtered history to 7 trades for timeframe: All Time
2025-04-05 01:39:39,854 INFO: Calculated equity curve with 8 points
2025-04-05 01:39:50,315 INFO: DPG main loop ended
2025-04-05 01:39:50,316 INFO: Shutting down GUI...
2025-04-05 01:39:50,321 INFO: GUI run completed.
2025-04-05 01:39:50,325 INFO: Shutting down application...
2025-04-05 01:40:47,981 INFO: File logging configured successfully.
2025-04-05 01:40:47,983 INFO: Starting application...
2025-04-05 01:40:47,984 INFO: Initializing AdvancedTradingBot...
2025-04-05 01:40:47,987 INFO: Loaded 3 open trades from file
2025-04-05 01:40:47,994 INFO: Initializing direct Bybit client...
2025-04-05 01:40:47,997 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:40:48,367 DEBUG: https://api.bybit.com:443 "GET /v5/market/time HTTP/1.1" 200 134
2025-04-05 01:40:48,373 INFO: Time synchronized. Server: 1743806444000, Local: 1743806448373, Offset: -4373 ms
2025-04-05 01:40:48,375 INFO: Direct Bybit client initialized successfully with time offset: -4373 ms
2025-04-05 01:40:48,378 INFO: Initializing pybit client as fallback...
2025-04-05 01:40:48,380 DEBUG: Initializing HTTP session.
2025-04-05 01:40:48,384 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:40:48,627 DEBUG: https://api.bybit.com:443 "GET /v5/market/time HTTP/1.1" 200 134
2025-04-05 01:40:48,633 INFO: Pybit client initialized successfully. Server time: {'retCode': 0, 'retMsg': 'OK', 'result': {'timeSecond': '1743806444', 'timeNano': '1743806444700068040'}, 'retExtInfo': {}, 'time': 1743806444700}
2025-04-05 01:40:48,639 INFO: AdvancedTradingBot initialized.
2025-04-05 01:40:48,643 INFO: Creating AdvancedTradingGUI...
2025-04-05 01:40:48,647 INFO: AdvancedTradingGUI init started.
2025-04-05 01:40:48,652 INFO: Loaded 7 trades from history file
2025-04-05 01:40:48,657 INFO: Calculated equity curve with 8 points
2025-04-05 01:40:48,661 INFO: Loaded 38 symbols from watchlist.json
2025-04-05 01:40:48,665 INFO: AdvancedTradingGUI created successfully.
2025-04-05 01:40:48,669 INFO: Running GUI...
2025-04-05 01:40:48,672 INFO: Running GUI...
2025-04-05 01:40:48,675 INFO: DPG context created
2025-04-05 01:40:48,677 INFO: DPG viewport created
2025-04-05 01:40:48,682 INFO: Main window created
2025-04-05 01:40:48,799 INFO: Viewport shown
2025-04-05 01:40:48,815 DEBUG: Fetching fresh positions data from API
2025-04-05 01:40:48,822 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:40:49,052 DEBUG: https://api.bybit.com:443 "GET /v5/market/time HTTP/1.1" 200 134
2025-04-05 01:40:49,072 DEBUG: Request URL: https://api.bybit.com/v5/position/list
2025-04-05 01:40:49,095 DEBUG: Request Params: {'category': 'linear', 'settleCoin': 'USDT', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': 1743806445000, 'recv_window': 120000, 'sign': '83c2a084fe7713cd12c6b29f398aaf7c2a344f5d133fd04e58176128ad9d6cc0'}
2025-04-05 01:40:49,102 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:40:49,333 DEBUG: https://api.bybit.com:443 "GET /v5/position/list?category=linear&settleCoin=USDT&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806445000&recv_window=120000&sign=83c2a084fe7713cd12c6b29f398aaf7c2a344f5d133fd04e58176128ad9d6cc0 HTTP/1.1" 200 125
2025-04-05 01:40:49,346 ERROR: Error using pybit client: AdvancedTradingBot.initialize_api_clients.<locals>.patched_auth() got an unexpected keyword argument 'recv_window'
2025-04-05 01:40:49,545 DEBUG: https://api.bybit.com:443 "GET /v5/market/tickers?category=linear&symbol=10000QUBICUSDT HTTP/1.1" 200 731
2025-04-05 01:40:49,591 INFO: Saved 8 trades to history file
2025-04-05 01:40:49,595 INFO: Filtered history to 8 trades for timeframe: All Time
2025-04-05 01:40:49,598 INFO: Calculated equity curve with 9 points
2025-04-05 01:40:49,603 INFO: Added trade to history: 10000QUBICUSDT Sell P&L: 0.00172
2025-04-05 01:40:49,606 INFO: Added closed position to history: 10000QUBICUSDT (Exit: Unknown)
2025-04-05 01:40:49,609 INFO: Saved 0 positions as last known positions
2025-04-05 01:40:49,613 DEBUG: Using cached positions data (age: 0.8s)
2025-04-05 01:40:49,617 INFO: Saved 0 positions as last known positions
2025-04-05 01:40:49,620 DEBUG: Using cached positions data (age: 0.8s)
2025-04-05 01:40:49,623 DEBUG: Saved 0 positions to last_positions.json
2025-04-05 01:40:49,627 INFO: Starting DPG main loop
2025-04-05 01:41:24,172 DEBUG: Fetching fresh positions data from API
2025-04-05 01:41:24,177 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:24,399 DEBUG: https://api.bybit.com:443 "GET /v5/market/time HTTP/1.1" 200 134
2025-04-05 01:41:24,444 DEBUG: Request URL: https://api.bybit.com/v5/position/list
2025-04-05 01:41:24,452 DEBUG: Request Params: {'category': 'linear', 'settleCoin': 'USDT', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': 1743806480000, 'recv_window': 120000, 'sign': '98467f845b0b46ef99776e21734c48638017058450da7acf254c9209fabc23c7'}
2025-04-05 01:41:24,460 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:24,693 DEBUG: https://api.bybit.com:443 "GET /v5/position/list?category=linear&settleCoin=USDT&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806480000&recv_window=120000&sign=98467f845b0b46ef99776e21734c48638017058450da7acf254c9209fabc23c7 HTTP/1.1" 200 125
2025-04-05 01:41:24,728 ERROR: Error using pybit client: AdvancedTradingBot.initialize_api_clients.<locals>.patched_auth() got an unexpected keyword argument 'recv_window'
2025-04-05 01:41:24,737 INFO: Saved 0 positions as last known positions
2025-04-05 01:41:24,742 DEBUG: Using cached positions data (age: 0.6s)
2025-04-05 01:41:24,746 DEBUG: Saved 0 positions to last_positions.json
2025-04-05 01:41:27,854 INFO: Starting full manual analysis...
2025-04-05 01:41:27,859 DEBUG: (10000000AIDOGEUSDT) Starting analysis for 1h...
2025-04-05 01:41:27,860 DEBUG: (1000000BABYDOGEUSDT) Starting analysis for 1h...
2025-04-05 01:41:27,860 DEBUG: (1000000CHEEMSUSDT) Starting analysis for 1h...
2025-04-05 01:41:27,860 DEBUG: (1000000MOGUSDT) Starting analysis for 1h...
2025-04-05 01:41:27,861 DEBUG: (1000000PEIPEIUSDT) Starting analysis for 1h...
2025-04-05 01:41:27,862 DEBUG: (10000COQUSDT) Starting analysis for 1h...
2025-04-05 01:41:27,862 DEBUG: (10000ELONUSDT) Starting analysis for 1h...
2025-04-05 01:41:27,862 DEBUG: (10000LADYSUSDT) Starting analysis for 1h...
2025-04-05 01:41:27,863 DEBUG: (10000QUBICUSDT) Starting analysis for 1h...
2025-04-05 01:41:27,863 DEBUG: (10000SATSUSDT) Starting analysis for 1h...
2025-04-05 01:41:27,864 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:27,869 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:27,873 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:27,877 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:27,880 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:27,884 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:27,887 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:27,890 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:27,893 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:27,896 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:27,899 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000000AIDOGEUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806487864', 'recv_window': '120000', 'sign': '4720f4b420967efa37c73e5b7c923da47c9c688fdf976002acdeaa6c35bf3925'}
2025-04-05 01:41:27,902 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000000BABYDOGEUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806487869', 'recv_window': '120000', 'sign': '7b9b20701d493fb8cefdc8b8592ff800745dadfe68ca142c3f48caaf350933fc'}
2025-04-05 01:41:27,905 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000000CHEEMSUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806487873', 'recv_window': '120000', 'sign': '9a0cdd1134bb6aabe248df8f34999c2c93c948e0904afea6198b9e1f3fdd311f'}
2025-04-05 01:41:27,909 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000000MOGUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806487877', 'recv_window': '120000', 'sign': 'f226106b651d15e21ec0b558c374522f0f5b7d9dadab97e3b0f084b0f826a42c'}
2025-04-05 01:41:27,912 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000000PEIPEIUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806487880', 'recv_window': '120000', 'sign': 'd01d89bd953a391a2327abff9b577a14ed4853ee4511cba10e72cffa93ee55fd'}
2025-04-05 01:41:27,915 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000COQUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806487884', 'recv_window': '120000', 'sign': '0457cb0202f97d59d6e09babdfc5d9f80dba845c9ee7fd0baa58083c4df748f3'}
2025-04-05 01:41:27,918 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000ELONUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806487887', 'recv_window': '120000', 'sign': '625b6c580e1d6363e74f23bfc0df6ff81f238f986f1e83b6e384e29cee44c55b'}
2025-04-05 01:41:27,922 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000LADYSUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806487890', 'recv_window': '120000', 'sign': '62f8fd9fef4444b76488e3a7a85e0d83dd38b45860c05721ca55ec837562dd9d'}
2025-04-05 01:41:27,925 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000QUBICUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806487893', 'recv_window': '120000', 'sign': '2991a29f200b1a0658c3b165ff4359f07b3d7a0b6f0f30350e167b659367aecd'}
2025-04-05 01:41:27,929 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000SATSUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806487896', 'recv_window': '120000', 'sign': '9a7ea1ccb4b184bdeccbc1e61d0b8aea86a6f6843853fd0ae30da5f9625330d6'}
2025-04-05 01:41:27,933 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:27,937 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:27,941 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:27,945 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:27,950 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:27,954 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:27,957 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:27,962 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:27,967 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:27,969 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:28,193 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000000AIDOGEUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806487864&recv_window=120000&sign=4720f4b420967efa37c73e5b7c923da47c9c688fdf976002acdeaa6c35bf3925 HTTP/1.1" 200 None
2025-04-05 01:41:28,197 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000000CHEEMSUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806487873&recv_window=120000&sign=9a0cdd1134bb6aabe248df8f34999c2c93c948e0904afea6198b9e1f3fdd311f HTTP/1.1" 200 None
2025-04-05 01:41:28,201 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000000MOGUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806487877&recv_window=120000&sign=f226106b651d15e21ec0b558c374522f0f5b7d9dadab97e3b0f084b0f826a42c HTTP/1.1" 200 None
2025-04-05 01:41:28,213 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000COQUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806487884&recv_window=120000&sign=0457cb0202f97d59d6e09babdfc5d9f80dba845c9ee7fd0baa58083c4df748f3 HTTP/1.1" 200 6041
2025-04-05 01:41:28,226 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000LADYSUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806487890&recv_window=120000&sign=62f8fd9fef4444b76488e3a7a85e0d83dd38b45860c05721ca55ec837562dd9d HTTP/1.1" 200 None
2025-04-05 01:41:28,228 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000QUBICUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806487893&recv_window=120000&sign=2991a29f200b1a0658c3b165ff4359f07b3d7a0b6f0f30350e167b659367aecd HTTP/1.1" 200 6009
2025-04-05 01:41:28,229 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000ELONUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806487887&recv_window=120000&sign=625b6c580e1d6363e74f23bfc0df6ff81f238f986f1e83b6e384e29cee44c55b HTTP/1.1" 200 None
2025-04-05 01:41:28,413 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.00/0.00 vs Threshold: 3.5
2025-04-05 01:41:28,430 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000SATSUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806487896&recv_window=120000&sign=9a7ea1ccb4b184bdeccbc1e61d0b8aea86a6f6843853fd0ae30da5f9625330d6 HTTP/1.1" 200 None
2025-04-05 01:41:28,480 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:28,551 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000000BABYDOGEUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806487869&recv_window=120000&sign=7b9b20701d493fb8cefdc8b8592ff800745dadfe68ca142c3f48caaf350933fc HTTP/1.1" 200 6505
2025-04-05 01:41:28,558 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:28,572 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000000PEIPEIUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806487880&recv_window=120000&sign=d01d89bd953a391a2327abff9b577a14ed4853ee4511cba10e72cffa93ee55fd HTTP/1.1" 200 5778
2025-04-05 01:41:28,577 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.50/1.00 vs Threshold: 3.5
2025-04-05 01:41:28,580 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.00/0.00 vs Threshold: 3.5
2025-04-05 01:41:28,584 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/1.00 vs Threshold: 3.5
2025-04-05 01:41:28,608 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.50/0.00 vs Threshold: 3.5
2025-04-05 01:41:28,617 DEBUG: (N/A) Last Close: 0.0008, Raw ATR: 0.0000, Valid ATR: 0.0000
2025-04-05 01:41:28,657 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.50/1.00 vs Threshold: 3.5
2025-04-05 01:41:28,659 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/0.30 vs Threshold: 3.5
2025-04-05 01:41:28,660 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.50/9.0 | Final Confidence: 27.8% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:41:28,669 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:28,674 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:28,693 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:28,702 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:28,715 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:28,719 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/1.50 vs Threshold: 3.5
2025-04-05 01:41:28,739 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:28,744 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:28,747 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:28,751 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:28,785 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.00/0.00 vs Threshold: 3.5
2025-04-05 01:41:28,785 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:28,789 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/0.00 vs Threshold: 3.5
2025-04-05 01:41:28,789 DEBUG: (N/A) Fib Levels: 0%=0.0008, 38.2%=0.0008, 61.8%=0.0008, 100%=0.0008, 161.8%=0.0009, -61.8%=0.0007
2025-04-05 01:41:28,790 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:28,794 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:41:28,797 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:28,801 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000COQUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806488744', 'recv_window': '120000', 'sign': '8478182b41d0ccaaa4a6d1c48cf258e64fc61fd555a7922b9deeb50d63efd2fa'}
2025-04-05 01:41:28,804 DEBUG: (N/A) Last Close: 1.6403, Raw ATR: 0.0471, Valid ATR: 0.0471
2025-04-05 01:41:28,807 DEBUG: (N/A) Last Close: 0.0003, Raw ATR: 0.0000, Valid ATR: 0.0000
2025-04-05 01:41:28,810 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:28,815 DEBUG: (N/A) Last Close: 0.3836, Raw ATR: 0.0094, Valid ATR: 0.0094
2025-04-05 01:41:28,818 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:41:28,822 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.0007823774, 0.0007955226), SL: 0.0008, TP: 0.0009
2025-04-05 01:41:28,825 DEBUG: (N/A) Last Close: 0.0101, Raw ATR: 0.0002, Valid ATR: 0.0002
2025-04-05 01:41:28,833 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:28,836 DEBUG: (N/A) Last Close: 0.0013, Raw ATR: 0.0000, Valid ATR: 0.0000
2025-04-05 01:41:28,845 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:28,851 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:28,860 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:28,867 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:28,878 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:28,884 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:28,888 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:28,891 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:28,895 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000SATSUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806488833', 'recv_window': '120000', 'sign': '2b764cde7cd06b6b8ee96997454fe4e361f8b4480132b297f974fbc36d7fe65c'}
2025-04-05 01:41:28,899 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:28,906 DEBUG: (N/A) Fib Levels: 0%=1.4411, 38.2%=1.5223, 61.8%=1.5724, 100%=1.6536, 161.8%=1.7849, -61.8%=1.3098
2025-04-05 01:41:28,911 DEBUG: (N/A) Fib Levels: 0%=0.0003, 38.2%=0.0003, 61.8%=0.0003, 100%=0.0003, 161.8%=0.0003, -61.8%=0.0003
2025-04-05 01:41:28,915 DEBUG: (N/A) Last Close: 0.0012, Raw ATR: 0.0000, Valid ATR: 0.0000
2025-04-05 01:41:28,919 DEBUG: (N/A) Fib Levels: 0%=0.3567, 38.2%=0.3709, 61.8%=0.3797, 100%=0.3939, 161.8%=0.4169, -61.8%=0.3337
2025-04-05 01:41:28,923 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000000PEIPEIUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806488884', 'recv_window': '120000', 'sign': 'a55b64d05739ab9ff96f84d4fcfeb24de899e13c4ac892fb58b068845e3969d5'}
2025-04-05 01:41:28,926 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.0007823774, 0.0007955226), SL=0.0008, TP=0.0009
2025-04-05 01:41:28,929 DEBUG: (N/A) Fib Levels: 0%=0.0094, 38.2%=0.0097, 61.8%=0.0099, 100%=0.0102, 161.8%=0.0108, -61.8%=0.0088
2025-04-05 01:41:28,933 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:28,936 DEBUG: (N/A) Fib Levels: 0%=0.0012, 38.2%=0.0013, 61.8%=0.0013, 100%=0.0013, 161.8%=0.0013, -61.8%=0.0012
2025-04-05 01:41:28,940 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (1.522275, 1.572425), SL: 1.4293, TP: 1.7849
2025-04-05 01:41:28,943 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.0003187232, 0.0003228768), SL: 0.0003, TP: 0.0003
2025-04-05 01:41:28,946 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:28,950 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.3709104, 0.37968959999999996), SL: 0.3544, TP: 0.4169
2025-04-05 01:41:28,954 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:28,957 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:28,961 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.00970216, 0.00990984), SL: 0.0093, TP: 0.0108
2025-04-05 01:41:28,968 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.0012516839999999999, 0.001266316), SL: 0.0012, TP: 0.0013
2025-04-05 01:41:28,972 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:28,976 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:28,979 DEBUG: (N/A) Fib Levels: 0%=0.0011, 38.2%=0.0011, 61.8%=0.0012, 100%=0.0012, 161.8%=0.0012, -61.8%=0.0011
2025-04-05 01:41:28,983 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:28,991 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:28,994 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:28,997 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:29,001 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(1.522275, 1.572425), SL=1.4293, TP=1.7849
2025-04-05 01:41:29,005 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.0003187232, 0.0003228768), SL=0.0003, TP=0.0003
2025-04-05 01:41:29,008 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.0011400874, 0.0011544126), SL: 0.0011, TP: 0.0012
2025-04-05 01:41:29,011 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.3709104, 0.37968959999999996), SL=0.3544, TP=0.4169
2025-04-05 01:41:29,015 INFO: N/A 1h: BUY signal, but price (0.0008) is outside entry zone (0.0008-0.0008). Penalty currently disabled for test.
2025-04-05 01:41:29,018 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.00970216, 0.00990984), SL=0.0093, TP=0.0108
2025-04-05 01:41:29,021 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.0012516839999999999, 0.001266316), SL=0.0012, TP=0.0013
2025-04-05 01:41:29,024 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:29,028 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:29,031 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:29,034 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:29,037 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.00/9.0 | Final Confidence: 44.4% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:29,041 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:29,044 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:29,048 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:29,051 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:29,054 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.0011400874, 0.0011544126), SL=0.0011, TP=0.0012
2025-04-05 01:41:29,058 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:29,062 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:29,064 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:29,068 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:29,071 INFO: N/A 1h: BUY signal, but price (1.6403) is outside entry zone (1.5223-1.5724). Penalty currently disabled for test.
2025-04-05 01:41:29,074 INFO: N/A 1h: BUY signal, but price (0.0003) is outside entry zone (0.0003-0.0003). Penalty currently disabled for test.
2025-04-05 01:41:29,078 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:29,082 INFO: N/A 1h: BUY signal, but price (0.3836) is outside entry zone (0.3709-0.3797). Penalty currently disabled for test.
2025-04-05 01:41:29,085 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000000AIDOGEUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806489062', 'recv_window': '120000', 'sign': '4cfde41618291877a2ed6c9251298864bced8ef2b9e0094f31412efccadede65'}
2025-04-05 01:41:29,088 INFO: N/A 1h: BUY signal, but price (0.0101) is outside entry zone (0.0097-0.0099). Penalty currently disabled for test.
2025-04-05 01:41:29,091 INFO: N/A 1h: BUY signal, but price (0.0013) is outside entry zone (0.0013-0.0013). Penalty currently disabled for test.
2025-04-05 01:41:29,095 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.00/9.0 | Final Confidence: 44.4% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:29,098 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:29,102 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:29,105 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.50/9.0 | Final Confidence: 50.0% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:29,110 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:29,112 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.50/9.0 | Final Confidence: 50.0% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:29,116 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:29,120 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:29,124 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:29,127 INFO: N/A 1h: BUY signal, but price (0.0012) is outside entry zone (0.0011-0.0012). Penalty currently disabled for test.
2025-04-05 01:41:29,131 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:29,138 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:29,141 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:29,145 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000000CHEEMSUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806489120', 'recv_window': '120000', 'sign': '023944004a13f5eadcf2ec12e7e99f8ea962901d3dcdfe8ca2299afc186b61af'}
2025-04-05 01:41:29,149 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000LADYSUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806489123', 'recv_window': '120000', 'sign': 'a9b91283cda14434243bfc5ddfb7fceba023fad26fbab8d8450d42899e273531'}
2025-04-05 01:41:29,152 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.00/9.0 | Final Confidence: 44.4% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:29,156 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000000MOGUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806489131', 'recv_window': '120000', 'sign': '4ea2a463e0c400cbbf4bca8bc6c63429a6e000d118628cb3fb19629d3079a3cf'}
2025-04-05 01:41:29,159 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000QUBICUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806489138', 'recv_window': '120000', 'sign': 'e79dde5fbc8335e68a060c1f5ab29dd3315332b46964f629ab2dfa00662ade65'}
2025-04-05 01:41:29,163 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000ELONUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806489141', 'recv_window': '120000', 'sign': '49d8eaa1d078a6cfd5f6e991fe432d536b07c17440aa93780829b0983e11663b'}
2025-04-05 01:41:29,167 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:29,171 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:29,174 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:29,178 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:29,207 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000SATSUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806488833&recv_window=120000&sign=2b764cde7cd06b6b8ee96997454fe4e361f8b4480132b297f974fbc36d7fe65c HTTP/1.1" 200 None
2025-04-05 01:41:29,207 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000000PEIPEIUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806488884&recv_window=120000&sign=a55b64d05739ab9ff96f84d4fcfeb24de899e13c4ac892fb58b068845e3969d5 HTTP/1.1" 200 None
2025-04-05 01:41:29,209 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:29,209 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:29,220 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000000BABYDOGEUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806489174', 'recv_window': '120000', 'sign': '68d3b520e31a74c47012940151d8cf41313fcc15fc75085e2d531d7cc892d48c'}
2025-04-05 01:41:29,270 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:29,336 DEBUG: Starting new HTTPS connection (2): api.bybit.com:443
2025-04-05 01:41:29,356 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000000AIDOGEUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806489062&recv_window=120000&sign=4cfde41618291877a2ed6c9251298864bced8ef2b9e0094f31412efccadede65 HTTP/1.1" 200 None
2025-04-05 01:41:29,413 DEBUG: Starting new HTTPS connection (3): api.bybit.com:443
2025-04-05 01:41:29,432 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000LADYSUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806489123&recv_window=120000&sign=a9b91283cda14434243bfc5ddfb7fceba023fad26fbab8d8450d42899e273531 HTTP/1.1" 200 None
2025-04-05 01:41:29,433 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000000CHEEMSUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806489120&recv_window=120000&sign=023944004a13f5eadcf2ec12e7e99f8ea962901d3dcdfe8ca2299afc186b61af HTTP/1.1" 200 None
2025-04-05 01:41:29,445 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000000MOGUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806489131&recv_window=120000&sign=4ea2a463e0c400cbbf4bca8bc6c63429a6e000d118628cb3fb19629d3079a3cf HTTP/1.1" 200 None
2025-04-05 01:41:29,474 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000QUBICUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806489138&recv_window=120000&sign=e79dde5fbc8335e68a060c1f5ab29dd3315332b46964f629ab2dfa00662ade65 HTTP/1.1" 200 None
2025-04-05 01:41:29,486 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000COQUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806488744&recv_window=120000&sign=8478182b41d0ccaaa4a6d1c48cf258e64fc61fd555a7922b9deeb50d63efd2fa HTTP/1.1" 200 5283
2025-04-05 01:41:29,505 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000ELONUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806489141&recv_window=120000&sign=49d8eaa1d078a6cfd5f6e991fe432d536b07c17440aa93780829b0983e11663b HTTP/1.1" 200 None
2025-04-05 01:41:29,525 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=10000SATSUSDT HTTP/1.1" 200 197
2025-04-05 01:41:29,532 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000000BABYDOGEUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806489174&recv_window=120000&sign=68d3b520e31a74c47012940151d8cf41313fcc15fc75085e2d531d7cc892d48c HTTP/1.1" 200 5683
2025-04-05 01:41:29,538 DEBUG: Starting new HTTPS connection (4): api.bybit.com:443
2025-04-05 01:41:29,571 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000000PEIPEIUSDT HTTP/1.1" 200 197
2025-04-05 01:41:29,663 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=10000000AIDOGEUSDT HTTP/1.1" 200 198
2025-04-05 01:41:29,678 DEBUG: Starting new HTTPS connection (5): api.bybit.com:443
2025-04-05 01:41:29,755 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=10000SATSUSDT HTTP/1.1" 200 322
2025-04-05 01:41:29,761 DEBUG: Starting new HTTPS connection (6): api.bybit.com:443
2025-04-05 01:41:29,789 DEBUG: Starting new HTTPS connection (7): api.bybit.com:443
2025-04-05 01:41:29,794 DEBUG: Starting new HTTPS connection (8): api.bybit.com:443
2025-04-05 01:41:29,800 DEBUG: Starting new HTTPS connection (9): api.bybit.com:443
2025-04-05 01:41:29,804 DEBUG: Starting new HTTPS connection (10): api.bybit.com:443
2025-04-05 01:41:29,823 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000000PEIPEIUSDT HTTP/1.1" 200 320
2025-04-05 01:41:29,830 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=10000LADYSUSDT HTTP/1.1" 200 194
2025-04-05 01:41:29,834 DEBUG: (10000SATSUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:41:29,860 DEBUG: (1000000PEIPEIUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:41:29,867 DEBUG: (10000WENUSDT) Starting analysis for 1h...
2025-04-05 01:41:29,871 DEBUG: (10000WHYUSDT) Starting analysis for 1h...
2025-04-05 01:41:29,874 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:29,877 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:29,881 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000WENUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806489874', 'recv_window': '120000', 'sign': '23e4554cb0f8b94956fbab17fa25be0d39f4c0b296b4d04006e0442e74aea6bb'}
2025-04-05 01:41:29,884 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000WHYUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806489877', 'recv_window': '120000', 'sign': '2134a921626324cd5dd9356c962ff920031cb96211834c1061559ae6a6bad933'}
2025-04-05 01:41:29,889 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:29,893 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:29,950 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=10000000AIDOGEUSDT HTTP/1.1" 200 325
2025-04-05 01:41:29,964 DEBUG: (10000000AIDOGEUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:29,973 DEBUG: (1000APUUSDT) Starting analysis for 1h...
2025-04-05 01:41:29,979 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:29,985 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000APUUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806489979', 'recv_window': '120000', 'sign': '1bd5d423a31c7e0eaa4c3fd6e5265dea54175e6a20afce0d11cba183daf82104'}
2025-04-05 01:41:29,993 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:30,007 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000000CHEEMSUSDT HTTP/1.1" 200 197
2025-04-05 01:41:30,058 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000000MOGUSDT HTTP/1.1" 200 195
2025-04-05 01:41:30,061 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=10000LADYSUSDT HTTP/1.1" 200 323
2025-04-05 01:41:30,066 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000000BABYDOGEUSDT HTTP/1.1" 200 199
2025-04-05 01:41:30,067 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=10000QUBICUSDT HTTP/1.1" 200 194
2025-04-05 01:41:30,074 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=10000COQUSDT HTTP/1.1" 200 197
2025-04-05 01:41:30,075 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=10000ELONUSDT HTTP/1.1" 200 194
2025-04-05 01:41:30,083 DEBUG: (10000LADYSUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:30,104 DEBUG: (1000BONKUSDT) Starting analysis for 1h...
2025-04-05 01:41:30,107 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:30,111 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000BONKUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806490107', 'recv_window': '120000', 'sign': '1095fa98f23c89f6f921cf8d2a098e98e5dc76b3f38d078796fbd09a3e06d57e'}
2025-04-05 01:41:30,114 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000WENUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806489874&recv_window=120000&sign=23e4554cb0f8b94956fbab17fa25be0d39f4c0b296b4d04006e0442e74aea6bb HTTP/1.1" 200 None
2025-04-05 01:41:30,116 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:30,118 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000WHYUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806489877&recv_window=120000&sign=2134a921626324cd5dd9356c962ff920031cb96211834c1061559ae6a6bad933 HTTP/1.1" 200 None
2025-04-05 01:41:30,168 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/0.30 vs Threshold: 3.5
2025-04-05 01:41:30,185 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:30,209 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:30,212 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000000CHEEMSUSDT HTTP/1.1" 200 320
2025-04-05 01:41:30,214 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000APUUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806489979&recv_window=120000&sign=1bd5d423a31c7e0eaa4c3fd6e5265dea54175e6a20afce0d11cba183daf82104 HTTP/1.1" 200 None
2025-04-05 01:41:30,216 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.00/0.00 vs Threshold: 3.5
2025-04-05 01:41:30,217 DEBUG: (N/A) Last Close: 0.2117, Raw ATR: 0.0030, Valid ATR: 0.0030
2025-04-05 01:41:30,221 DEBUG: (1000000CHEEMSUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:30,244 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:30,258 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:30,268 DEBUG: (1000BTTUSDT) Starting analysis for 1h...
2025-04-05 01:41:30,275 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.00/0.00 vs Threshold: 3.5
2025-04-05 01:41:30,275 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:30,275 DEBUG: (N/A) Fib Levels: 0%=0.2012, 38.2%=0.2058, 61.8%=0.2086, 100%=0.2132, 161.8%=0.2206, -61.8%=0.1938
2025-04-05 01:41:30,276 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000000MOGUSDT HTTP/1.1" 200 317
2025-04-05 01:41:30,279 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:30,287 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000000BABYDOGEUSDT HTTP/1.1" 200 328
2025-04-05 01:41:30,289 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:30,291 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=10000QUBICUSDT HTTP/1.1" 200 319
2025-04-05 01:41:30,298 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=10000COQUSDT HTTP/1.1" 200 318
2025-04-05 01:41:30,299 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=10000ELONUSDT HTTP/1.1" 200 320
2025-04-05 01:41:30,300 DEBUG: (N/A) Last Close: 0.0006, Raw ATR: 0.0000, Valid ATR: 0.0000
2025-04-05 01:41:30,307 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.205784, 0.208616), SL: 0.2004, TP: 0.2206
2025-04-05 01:41:30,311 DEBUG: (1000000MOGUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:30,315 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000BTTUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806490279', 'recv_window': '120000', 'sign': '2bd3b3fb567393179b6372181b9dbd36e488ba222e5924f7e8a7973edc882456'}
2025-04-05 01:41:30,318 DEBUG: (1000000BABYDOGEUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:30,322 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:30,325 DEBUG: (10000QUBICUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:30,329 DEBUG: (10000COQUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:41:30,332 DEBUG: (10000ELONUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:30,336 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:30,339 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:30,342 DEBUG: (1000CATSUSDT) Starting analysis for 1h...
2025-04-05 01:41:30,347 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:30,350 DEBUG: (1000CATUSDT) Starting analysis for 1h...
2025-04-05 01:41:30,352 DEBUG: (N/A) Last Close: 0.1655, Raw ATR: 0.0046, Valid ATR: 0.0046
2025-04-05 01:41:30,356 DEBUG: (1000FLOKIUSDT) Starting analysis for 1h...
2025-04-05 01:41:30,359 DEBUG: (1000LUNCUSDT) Starting analysis for 1h...
2025-04-05 01:41:30,362 DEBUG: (1000MUMUUSDT) Starting analysis for 1h...
2025-04-05 01:41:30,364 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000BONKUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806490107&recv_window=120000&sign=1095fa98f23c89f6f921cf8d2a098e98e5dc76b3f38d078796fbd09a3e06d57e HTTP/1.1" 200 None
2025-04-05 01:41:30,366 DEBUG: (N/A) Fib Levels: 0%=0.0006, 38.2%=0.0006, 61.8%=0.0006, 100%=0.0006, 161.8%=0.0007, -61.8%=0.0006
2025-04-05 01:41:30,370 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.205784, 0.208616), SL=0.2004, TP=0.2206
2025-04-05 01:41:30,373 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:30,380 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:30,384 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:30,387 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:30,390 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:30,393 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:30,402 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.0006108384, 0.0006205616), SL: 0.0006, TP: 0.0007
2025-04-05 01:41:30,409 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:30,420 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000CATSUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806490373', 'recv_window': '120000', 'sign': '604c0ad044322fd6a47aa0f9ee00af511391f5837fa496295408356d6d83014b'}
2025-04-05 01:41:30,437 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000CATUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806490380', 'recv_window': '120000', 'sign': '0b6cd8f8adf27564987599d4357dca64e213e8859ca607c84a266dd88bb8e578'}
2025-04-05 01:41:30,448 DEBUG: (N/A) Fib Levels: 0%=0.1495, 38.2%=0.1565, 61.8%=0.1609, 100%=0.1679, 161.8%=0.1793, -61.8%=0.1381
2025-04-05 01:41:30,450 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/1.00 vs Threshold: 3.5
2025-04-05 01:41:30,451 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000FLOKIUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806490387', 'recv_window': '120000', 'sign': 'c61c5eb3aa6919b50615e730796ee7280427f2e92cd4e4ac7bc42e91f57029d3'}
2025-04-05 01:41:30,454 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000LUNCUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806490390', 'recv_window': '120000', 'sign': '43b887417daf86d195548365ee77e96aac4de0ca6eb3bd4e0638f1fdccd7f86c'}
2025-04-05 01:41:30,457 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000MUMUUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806490393', 'recv_window': '120000', 'sign': '8903f03930917a726f15c1f8d96734525fa643cc9bc446870aea768ffa8edf89'}
2025-04-05 01:41:30,461 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:30,464 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:30,469 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:30,473 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:30,475 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.1565288, 0.1608712), SL: 0.1484, TP: 0.1793
2025-04-05 01:41:30,479 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:30,484 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:30,488 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:30,491 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:30,495 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.0006108384, 0.0006205616), SL=0.0006, TP=0.0007
2025-04-05 01:41:30,498 INFO: N/A 1h: BUY signal, but price (0.2117) is outside entry zone (0.2058-0.2086). Penalty currently disabled for test.
2025-04-05 01:41:30,509 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:30,512 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:30,526 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:30,530 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:30,534 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.1565288, 0.1608712), SL=0.1484, TP=0.1793
2025-04-05 01:41:30,537 DEBUG: (N/A) Last Close: 0.0114, Raw ATR: 0.0002, Valid ATR: 0.0002
2025-04-05 01:41:30,540 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:30,545 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:30,549 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:30,552 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:30,556 INFO: N/A 1h: BUY signal, but price (0.0006) is outside entry zone (0.0006-0.0006). Penalty currently disabled for test.
2025-04-05 01:41:30,559 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000WENUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806490545', 'recv_window': '120000', 'sign': 'd39416433598129b7477c4aa5df39e8d1b77d2a580c0a1ae913f8731947029ef'}
2025-04-05 01:41:30,563 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:30,566 DEBUG: (N/A) Fib Levels: 0%=0.0107, 38.2%=0.0110, 61.8%=0.0112, 100%=0.0115, 161.8%=0.0120, -61.8%=0.0102
2025-04-05 01:41:30,569 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.00/9.0 | Final Confidence: 44.4% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:30,576 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:30,578 INFO: N/A 1h: BUY signal, but price (0.1655) is outside entry zone (0.1565-0.1609). Penalty currently disabled for test.
2025-04-05 01:41:30,581 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.010992914000000001, 0.011188086), SL: 0.0106, TP: 0.0120
2025-04-05 01:41:30,585 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:30,592 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.00/9.0 | Final Confidence: 44.4% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:30,595 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:30,597 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000BTTUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806490279&recv_window=120000&sign=2bd3b3fb567393179b6372181b9dbd36e488ba222e5924f7e8a7973edc882456 HTTP/1.1" 200 6420
2025-04-05 01:41:30,598 DEBUG: Request Params: {'category': 'linear', 'symbol': '10000WHYUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806490585', 'recv_window': '120000', 'sign': 'e778d056066ab92358bea7acce5818e6a4dbffdd1fedb7a7a321f4f594cfd3c3'}
2025-04-05 01:41:30,603 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:30,606 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.010992914000000001, 0.011188086), SL=0.0106, TP=0.0120
2025-04-05 01:41:30,621 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:30,628 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000APUUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806490603', 'recv_window': '120000', 'sign': '9cbfcd1956fcd1ba8dc245877679461d94809fe8dd6218771f3050cf6a25bc3b'}
2025-04-05 01:41:30,632 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:30,662 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 3.00/0.50 vs Threshold: 3.5
2025-04-05 01:41:30,663 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:30,663 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:30,667 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 3.00/9.0 | Final Confidence: 33.3% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:41:30,674 INFO: N/A 1h: BUY signal, but price (0.0114) is outside entry zone (0.0110-0.0112). Penalty currently disabled for test.
2025-04-05 01:41:30,678 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:30,681 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:30,684 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000BTTUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806490678', 'recv_window': '120000', 'sign': '400c6a0045070beef870e52095a308b51d3fc732920abf4300b6969937eac668'}
2025-04-05 01:41:30,688 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:30,692 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:30,695 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000BONKUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806490688', 'recv_window': '120000', 'sign': '9153386398745a81b60ff13af59e30ccebb836ff9c7cf434b9d7705704a4faac'}
2025-04-05 01:41:30,702 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:30,719 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000CATSUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806490373&recv_window=120000&sign=604c0ad044322fd6a47aa0f9ee00af511391f5837fa496295408356d6d83014b HTTP/1.1" 200 None
2025-04-05 01:41:30,721 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000CATUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806490380&recv_window=120000&sign=0b6cd8f8adf27564987599d4357dca64e213e8859ca607c84a266dd88bb8e578 HTTP/1.1" 200 None
2025-04-05 01:41:30,733 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000FLOKIUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806490387&recv_window=120000&sign=c61c5eb3aa6919b50615e730796ee7280427f2e92cd4e4ac7bc42e91f57029d3 HTTP/1.1" 200 None
2025-04-05 01:41:30,737 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000LUNCUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806490390&recv_window=120000&sign=43b887417daf86d195548365ee77e96aac4de0ca6eb3bd4e0638f1fdccd7f86c HTTP/1.1" 200 None
2025-04-05 01:41:30,750 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000MUMUUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806490393&recv_window=120000&sign=8903f03930917a726f15c1f8d96734525fa643cc9bc446870aea768ffa8edf89 HTTP/1.1" 200 None
2025-04-05 01:41:30,810 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000WENUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806490545&recv_window=120000&sign=d39416433598129b7477c4aa5df39e8d1b77d2a580c0a1ae913f8731947029ef HTTP/1.1" 200 None
2025-04-05 01:41:30,880 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=10000WHYUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806490585&recv_window=120000&sign=e778d056066ab92358bea7acce5818e6a4dbffdd1fedb7a7a321f4f594cfd3c3 HTTP/1.1" 200 5676
2025-04-05 01:41:30,890 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000APUUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806490603&recv_window=120000&sign=9cbfcd1956fcd1ba8dc245877679461d94809fe8dd6218771f3050cf6a25bc3b HTTP/1.1" 200 None
2025-04-05 01:41:30,924 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000BTTUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806490678&recv_window=120000&sign=400c6a0045070beef870e52095a308b51d3fc732920abf4300b6969937eac668 HTTP/1.1" 200 5585
2025-04-05 01:41:30,933 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000BONKUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806490688&recv_window=120000&sign=9153386398745a81b60ff13af59e30ccebb836ff9c7cf434b9d7705704a4faac HTTP/1.1" 200 None
2025-04-05 01:41:30,946 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.00/0.00 vs Threshold: 3.5
2025-04-05 01:41:30,972 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/0.00 vs Threshold: 3.5
2025-04-05 01:41:31,053 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/0.30 vs Threshold: 3.5
2025-04-05 01:41:31,092 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/1.00 vs Threshold: 3.5
2025-04-05 01:41:31,096 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:31,103 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:41:31,165 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.00/1.00 vs Threshold: 3.5
2025-04-05 01:41:31,165 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:31,174 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:31,183 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:31,211 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:31,224 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:31,256 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:31,284 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:31,302 DEBUG: (N/A) Last Close: 0.0072, Raw ATR: 0.0001, Valid ATR: 0.0001
2025-04-05 01:41:31,311 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000CATUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806491211', 'recv_window': '120000', 'sign': 'd1f7786a78988545d5f13ef37d0573c6bbb67f5de8dd04c0552cc0396c80d8d0'}
2025-04-05 01:41:31,316 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:31,316 DEBUG: (N/A) Last Close: 0.0589, Raw ATR: 0.0008, Valid ATR: 0.0008
2025-04-05 01:41:31,318 DEBUG: (N/A) Last Close: 0.0569, Raw ATR: 0.0009, Valid ATR: 0.0009
2025-04-05 01:41:31,323 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:31,327 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:31,330 DEBUG: (N/A) Last Close: 0.0028, Raw ATR: 0.0001, Valid ATR: 0.0001
2025-04-05 01:41:31,333 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:31,337 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:31,340 DEBUG: (N/A) Fib Levels: 0%=0.0067, 38.2%=0.0069, 61.8%=0.0070, 100%=0.0073, 161.8%=0.0076, -61.8%=0.0063
2025-04-05 01:41:31,347 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:31,350 DEBUG: (N/A) Fib Levels: 0%=0.0572, 38.2%=0.0582, 61.8%=0.0588, 100%=0.0598, 161.8%=0.0615, -61.8%=0.0555
2025-04-05 01:41:31,353 DEBUG: (N/A) Fib Levels: 0%=0.0534, 38.2%=0.0548, 61.8%=0.0557, 100%=0.0572, 161.8%=0.0595, -61.8%=0.0510
2025-04-05 01:41:31,357 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.0069127400000000006, 0.00704726), SL: 0.0067, TP: 0.0076
2025-04-05 01:41:31,360 DEBUG: (N/A) Fib Levels: 0%=0.0024, 38.2%=0.0026, 61.8%=0.0027, 100%=0.0029, 161.8%=0.0032, -61.8%=0.0021
2025-04-05 01:41:31,364 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.05818612, 0.05881388), SL: 0.0570, TP: 0.0615
2025-04-05 01:41:31,367 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.054835419999999996, 0.05573458), SL: 0.0531, TP: 0.0595
2025-04-05 01:41:31,371 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:31,374 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.002623382, 0.002741618), SL: 0.0024, TP: 0.0032
2025-04-05 01:41:31,378 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:31,382 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:31,385 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.0069127400000000006, 0.00704726), SL=0.0067, TP=0.0076
2025-04-05 01:41:31,388 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:31,392 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.05818612, 0.05881388), SL=0.0570, TP=0.0615
2025-04-05 01:41:31,395 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.054835419999999996, 0.05573458), SL=0.0531, TP=0.0595
2025-04-05 01:41:31,397 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:31,401 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.002623382, 0.002741618), SL=0.0024, TP=0.0032
2025-04-05 01:41:31,404 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:31,408 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:31,411 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:31,415 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:31,418 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:31,421 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:31,423 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=10000WENUSDT HTTP/1.1" 200 192
2025-04-05 01:41:31,425 INFO: N/A 1h: BUY signal, but price (0.0072) is outside entry zone (0.0069-0.0070). Penalty currently disabled for test.
2025-04-05 01:41:31,428 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:31,432 INFO: N/A 1h: BUY signal, but price (0.0589) is outside entry zone (0.0582-0.0588). Penalty currently disabled for test.
2025-04-05 01:41:31,435 INFO: N/A 1h: BUY signal, but price (0.0569) is outside entry zone (0.0548-0.0557). Penalty currently disabled for test.
2025-04-05 01:41:31,444 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.00/9.0 | Final Confidence: 44.4% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:31,446 INFO: N/A 1h: BUY signal, but price (0.0028) is outside entry zone (0.0026-0.0027). Penalty currently disabled for test.
2025-04-05 01:41:31,449 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:31,453 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:31,457 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:31,460 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.00/9.0 | Final Confidence: 44.4% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:31,464 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:31,468 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:31,471 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000CATSUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806491457', 'recv_window': '120000', 'sign': 'd3be0556bd1f104b6da9b7147588b2652ab8be17b200c6cf317c6c89b9b5d2a8'}
2025-04-05 01:41:31,475 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:31,477 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000LUNCUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806491464', 'recv_window': '120000', 'sign': '951ca6771917af2e36f35d197b2823758abb8ce3ced1337b7c93b8e2451d31ed'}
2025-04-05 01:41:31,481 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000FLOKIUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806491468', 'recv_window': '120000', 'sign': '340ef08a8ad78aaf6e00df5fb9074ecc2a749d1b3a631d3ab1222aef35210b2c'}
2025-04-05 01:41:31,485 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:31,488 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000MUMUUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806491475', 'recv_window': '120000', 'sign': '0c04c47f4e8d40d0f9db0cb00c7457ed0b1130afe134e133f8e68ea9770d0745'}
2025-04-05 01:41:31,490 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=10000WHYUSDT HTTP/1.1" 200 192
2025-04-05 01:41:31,492 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:31,496 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:31,503 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:31,508 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000APUUSDT HTTP/1.1" 200 191
2025-04-05 01:41:31,511 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000BONKUSDT HTTP/1.1" 200 193
2025-04-05 01:41:31,512 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000BTTUSDT HTTP/1.1" 200 191
2025-04-05 01:41:31,559 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000CATUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806491211&recv_window=120000&sign=d1f7786a78988545d5f13ef37d0573c6bbb67f5de8dd04c0552cc0396c80d8d0 HTTP/1.1" 200 None
2025-04-05 01:41:31,639 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=10000WENUSDT HTTP/1.1" 200 315
2025-04-05 01:41:31,711 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=10000WHYUSDT HTTP/1.1" 200 321
2025-04-05 01:41:31,719 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000CATSUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806491457&recv_window=120000&sign=d3be0556bd1f104b6da9b7147588b2652ab8be17b200c6cf317c6c89b9b5d2a8 HTTP/1.1" 200 5283
2025-04-05 01:41:31,722 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000APUUSDT HTTP/1.1" 200 314
2025-04-05 01:41:31,722 DEBUG: (10000WENUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:31,723 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000BTTUSDT HTTP/1.1" 200 320
2025-04-05 01:41:31,723 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000BONKUSDT HTTP/1.1" 200 321
2025-04-05 01:41:31,730 DEBUG: (10000WHYUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:31,735 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000LUNCUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806491464&recv_window=120000&sign=951ca6771917af2e36f35d197b2823758abb8ce3ced1337b7c93b8e2451d31ed HTTP/1.1" 200 None
2025-04-05 01:41:31,735 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000MUMUUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806491475&recv_window=120000&sign=0c04c47f4e8d40d0f9db0cb00c7457ed0b1130afe134e133f8e68ea9770d0745 HTTP/1.1" 200 None
2025-04-05 01:41:31,760 DEBUG: (1000APUUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:31,774 DEBUG: (1000NEIROCTOUSDT) Starting analysis for 1h...
2025-04-05 01:41:31,783 DEBUG: (1000BTTUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:41:31,788 DEBUG: (1000BONKUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:31,788 DEBUG: (1000PEPEUSDT) Starting analysis for 1h...
2025-04-05 01:41:31,808 DEBUG: (1000RATSUSDT) Starting analysis for 1h...
2025-04-05 01:41:31,815 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:31,834 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000CATUSDT HTTP/1.1" 200 196
2025-04-05 01:41:31,846 DEBUG: (1000TOSHIUSDT) Starting analysis for 1h...
2025-04-05 01:41:31,849 DEBUG: (1000TURBOUSDT) Starting analysis for 1h...
2025-04-05 01:41:31,856 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:31,865 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:31,879 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000NEIROCTOUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806491815', 'recv_window': '120000', 'sign': '91561e92c93a3f41e75c0df60f6c7d0d35c54954616c4100cc903823f40b8d09'}
2025-04-05 01:41:31,894 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:31,897 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:31,900 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000PEPEUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806491856', 'recv_window': '120000', 'sign': 'a06c79b9ce8bd954345da60965209296947f0c7e20f7400375ac3b12adc039d3'}
2025-04-05 01:41:31,903 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000RATSUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806491865', 'recv_window': '120000', 'sign': 'f3b033d8239707309472a367ef3f8e6d888bddd9633d155a9178bb90324cc80e'}
2025-04-05 01:41:31,908 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:31,911 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000TOSHIUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806491894', 'recv_window': '120000', 'sign': '877b0c6a31dc7c9ea33a4e1c0ddddfade7f98eb21c864378506e5cf9328740d6'}
2025-04-05 01:41:31,914 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000TURBOUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806491897', 'recv_window': '120000', 'sign': '443f143a1676dbe5718a0a07ee72b702eee192a0db19552094d7e4c7cb21a348'}
2025-04-05 01:41:31,919 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:31,923 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:31,930 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:31,935 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:31,985 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000CATSUSDT HTTP/1.1" 200 192
2025-04-05 01:41:32,074 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000LUNCUSDT HTTP/1.1" 200 196
2025-04-05 01:41:32,093 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000CATUSDT HTTP/1.1" 200 319
2025-04-05 01:41:32,094 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000MUMUUSDT HTTP/1.1" 200 192
2025-04-05 01:41:32,097 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000FLOKIUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806491468&recv_window=120000&sign=340ef08a8ad78aaf6e00df5fb9074ecc2a749d1b3a631d3ab1222aef35210b2c HTTP/1.1" 200 5175
2025-04-05 01:41:32,133 DEBUG: (1000CATUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:41:32,149 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000NEIROCTOUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806491815&recv_window=120000&sign=91561e92c93a3f41e75c0df60f6c7d0d35c54954616c4100cc903823f40b8d09 HTTP/1.1" 200 None
2025-04-05 01:41:32,160 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000PEPEUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806491856&recv_window=120000&sign=a06c79b9ce8bd954345da60965209296947f0c7e20f7400375ac3b12adc039d3 HTTP/1.1" 200 None
2025-04-05 01:41:32,164 DEBUG: (1000XECUSDT) Starting analysis for 1h...
2025-04-05 01:41:32,166 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000RATSUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806491865&recv_window=120000&sign=f3b033d8239707309472a367ef3f8e6d888bddd9633d155a9178bb90324cc80e HTTP/1.1" 200 None
2025-04-05 01:41:32,182 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000TURBOUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806491897&recv_window=120000&sign=443f143a1676dbe5718a0a07ee72b702eee192a0db19552094d7e4c7cb21a348 HTTP/1.1" 200 None
2025-04-05 01:41:32,182 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000TOSHIUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806491894&recv_window=120000&sign=877b0c6a31dc7c9ea33a4e1c0ddddfade7f98eb21c864378506e5cf9328740d6 HTTP/1.1" 200 5479
2025-04-05 01:41:32,200 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000CATSUSDT HTTP/1.1" 200 317
2025-04-05 01:41:32,206 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:32,276 DEBUG: (1000CATSUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:32,291 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000XECUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806492206', 'recv_window': '120000', 'sign': '91adcec7a6ba02eb2abedb57202123d517415a6f11f8dc1943a227010dab845b'}
2025-04-05 01:41:32,312 DEBUG: (1000XUSDT) Starting analysis for 1h...
2025-04-05 01:41:32,329 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000LUNCUSDT HTTP/1.1" 200 317
2025-04-05 01:41:32,336 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/0.00 vs Threshold: 3.5
2025-04-05 01:41:32,341 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000MUMUUSDT HTTP/1.1" 200 319
2025-04-05 01:41:32,373 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:32,374 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:32,376 DEBUG: (1000LUNCUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:32,380 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 5.00/0.00 vs Threshold: 3.5
2025-04-05 01:41:32,407 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000FLOKIUSDT HTTP/1.1" 200 198
2025-04-05 01:41:32,422 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:32,435 DEBUG: (1000MUMUUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:32,459 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/0.00 vs Threshold: 3.5
2025-04-05 01:41:32,460 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000XUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806492374', 'recv_window': '120000', 'sign': '12b1771445a8c60328a9ba16a117290d5c20a93ad0f3df714f0caf02d5982e5c'}
2025-04-05 01:41:32,462 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/0.50 vs Threshold: 3.5
2025-04-05 01:41:32,465 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.00/0.00 vs Threshold: 3.5
2025-04-05 01:41:32,466 DEBUG: (1INCHUSDT) Starting analysis for 1h...
2025-04-05 01:41:32,470 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:32,477 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:32,480 DEBUG: (A8USDT) Starting analysis for 1h...
2025-04-05 01:41:32,484 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:41:32,488 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:32,491 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:41:32,494 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:32,498 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:32,502 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:32,504 DEBUG: (N/A) Last Close: 0.1784, Raw ATR: 0.0033, Valid ATR: 0.0033
2025-04-05 01:41:32,507 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:32,512 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:32,519 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:32,522 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:32,525 DEBUG: Request Params: {'category': 'linear', 'symbol': '1INCHUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806492498', 'recv_window': '120000', 'sign': '32e23583f0723b7698779972fe40b60b879f293c604801189fa04e6ca7558d9c'}
2025-04-05 01:41:32,528 DEBUG: (N/A) Last Close: 0.0072, Raw ATR: 0.0002, Valid ATR: 0.0002
2025-04-05 01:41:32,532 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:32,536 DEBUG: Request Params: {'category': 'linear', 'symbol': 'A8USDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806492507', 'recv_window': '120000', 'sign': '9b1532c53485dae3f56c1863db72d093abeb3a2c23c8de54c5aba4f2b7dec44c'}
2025-04-05 01:41:32,540 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000TURBOUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806492512', 'recv_window': '120000', 'sign': 'ee260958a92cfc7ae02959601e7c8dfb061f25d09fdb727b4c73b9cfbf422e3b'}
2025-04-05 01:41:32,543 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000TOSHIUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806492518', 'recv_window': '120000', 'sign': '7e03c552e388c0e84bedfcc66738c74c4cb510669858c92cfd80e7bee9d504da'}
2025-04-05 01:41:32,546 DEBUG: (N/A) Last Close: 0.0310, Raw ATR: 0.0008, Valid ATR: 0.0008
2025-04-05 01:41:32,551 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:32,554 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:32,557 DEBUG: (N/A) Fib Levels: 0%=0.1676, 38.2%=0.1728, 61.8%=0.1761, 100%=0.1813, 161.8%=0.1898, -61.8%=0.1591
2025-04-05 01:41:32,562 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:32,566 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:32,570 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:32,572 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:32,580 DEBUG: (N/A) Fib Levels: 0%=0.0067, 38.2%=0.0069, 61.8%=0.0070, 100%=0.0073, 161.8%=0.0077, -61.8%=0.0063
2025-04-05 01:41:32,583 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.1728334, 0.1760666), SL: 0.1668, TP: 0.1898
2025-04-05 01:41:32,597 DEBUG: (N/A) Fib Levels: 0%=0.0288, 38.2%=0.0301, 61.8%=0.0310, 100%=0.0323, 161.8%=0.0345, -61.8%=0.0266
2025-04-05 01:41:32,601 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.00690402, 0.00704798), SL: 0.0066, TP: 0.0077
2025-04-05 01:41:32,605 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:32,608 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.030127, 0.030952999999999998), SL: 0.0286, TP: 0.0345
2025-04-05 01:41:32,612 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:32,615 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.1728334, 0.1760666), SL=0.1668, TP=0.1898
2025-04-05 01:41:32,619 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:32,622 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.00690402, 0.00704798), SL=0.0066, TP=0.0077
2025-04-05 01:41:32,625 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:32,629 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.030127, 0.030952999999999998), SL=0.0286, TP=0.0345
2025-04-05 01:41:32,632 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:32,635 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:32,638 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:32,642 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:32,645 INFO: N/A 1h: BUY signal, but price (0.1784) is outside entry zone (0.1728-0.1761). Penalty currently disabled for test.
2025-04-05 01:41:32,649 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:32,652 INFO: N/A 1h: BUY signal, but price (0.0072) is outside entry zone (0.0069-0.0070). Penalty currently disabled for test.
2025-04-05 01:41:32,655 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:32,659 INFO: N/A 1h: BUY signal, but price (0.0310) is outside entry zone (0.0301-0.0310). Penalty currently disabled for test.
2025-04-05 01:41:32,662 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 5.00/9.0 | Final Confidence: 55.6% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:32,666 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:32,669 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.00/9.0 | Final Confidence: 44.4% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:32,672 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000FLOKIUSDT HTTP/1.1" 200 320
2025-04-05 01:41:32,673 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000XECUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806492206&recv_window=120000&sign=91adcec7a6ba02eb2abedb57202123d517415a6f11f8dc1943a227010dab845b HTTP/1.1" 200 None
2025-04-05 01:41:32,674 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:32,676 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000NEIROCTOUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806492666', 'recv_window': '120000', 'sign': 'c40aac2acbd794fdc93ed470d1c1a44ff800c1e9d244009c8996742a1e00e4a0'}
2025-04-05 01:41:32,680 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:32,684 DEBUG: (1000FLOKIUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:32,706 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000PEPEUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806492674', 'recv_window': '120000', 'sign': '52f0b10f195522a565ee508a9480f0ec023e20f0cdefce23057d009db783f555'}
2025-04-05 01:41:32,712 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:32,734 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000XUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806492374&recv_window=120000&sign=12b1771445a8c60328a9ba16a117290d5c20a93ad0f3df714f0caf02d5982e5c HTTP/1.1" 200 None
2025-04-05 01:41:32,741 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/0.00 vs Threshold: 3.5
2025-04-05 01:41:32,742 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000RATSUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806492680', 'recv_window': '120000', 'sign': '71e0f4ecd015aee32385f38c1ae5b0bc8380ddb1bf3e01ab622c44652d782dc8'}
2025-04-05 01:41:32,742 DEBUG: (AAVEUSDT) Starting analysis for 1h...
2025-04-05 01:41:32,743 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:32,759 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:32,784 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:32,785 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:32,794 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1INCHUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806492498&recv_window=120000&sign=32e23583f0723b7698779972fe40b60b879f293c604801189fa04e6ca7558d9c HTTP/1.1" 200 None
2025-04-05 01:41:32,796 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/0.00 vs Threshold: 3.5
2025-04-05 01:41:32,797 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:32,800 DEBUG: Request Params: {'category': 'linear', 'symbol': 'AAVEUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806492784', 'recv_window': '120000', 'sign': 'c889ebc1d5deaebca8d58f1931c6dbef5576157006df5543bab7daec51fd91c6'}
2025-04-05 01:41:32,805 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=A8USDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806492507&recv_window=120000&sign=9b1532c53485dae3f56c1863db72d093abeb3a2c23c8de54c5aba4f2b7dec44c HTTP/1.1" 200 None
2025-04-05 01:41:32,811 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000TURBOUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806492512&recv_window=120000&sign=ee260958a92cfc7ae02959601e7c8dfb061f25d09fdb727b4c73b9cfbf422e3b HTTP/1.1" 200 None
2025-04-05 01:41:32,816 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000TOSHIUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806492518&recv_window=120000&sign=7e03c552e388c0e84bedfcc66738c74c4cb510669858c92cfd80e7bee9d504da HTTP/1.1" 200 None
2025-04-05 01:41:32,819 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:41:32,821 DEBUG: (N/A) Last Close: 0.0197, Raw ATR: 0.0002, Valid ATR: 0.0002
2025-04-05 01:41:32,833 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:32,866 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/0.00 vs Threshold: 3.5
2025-04-05 01:41:32,873 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:32,886 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:32,914 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:32,923 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000XUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806492873', 'recv_window': '120000', 'sign': 'dc5f8dc9eb65b1122f7459ae940f465d48abc0fea37b805d9d09c071029d6d2a'}
2025-04-05 01:41:32,931 DEBUG: (N/A) Fib Levels: 0%=0.0189, 38.2%=0.0192, 61.8%=0.0194, 100%=0.0198, 161.8%=0.0203, -61.8%=0.0184
2025-04-05 01:41:32,933 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:32,964 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:32,984 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000NEIROCTOUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806492666&recv_window=120000&sign=c40aac2acbd794fdc93ed470d1c1a44ff800c1e9d244009c8996742a1e00e4a0 HTTP/1.1" 200 4839
2025-04-05 01:41:32,985 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.01922234, 0.01942766), SL: 0.0188, TP: 0.0203
2025-04-05 01:41:33,002 DEBUG: (N/A) Last Close: 0.1879, Raw ATR: 0.0026, Valid ATR: 0.0026
2025-04-05 01:41:33,006 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/1.00 vs Threshold: 3.5
2025-04-05 01:41:33,016 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000PEPEUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806492674&recv_window=120000&sign=52f0b10f195522a565ee508a9480f0ec023e20f0cdefce23057d009db783f555 HTTP/1.1" 200 None
2025-04-05 01:41:33,021 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:33,024 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:33,030 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000RATSUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806492680&recv_window=120000&sign=71e0f4ecd015aee32385f38c1ae5b0bc8380ddb1bf3e01ab622c44652d782dc8 HTTP/1.1" 200 None
2025-04-05 01:41:33,031 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:41:33,046 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.01922234, 0.01942766), SL=0.0188, TP=0.0203
2025-04-05 01:41:33,075 DEBUG: (N/A) Fib Levels: 0%=0.1782, 38.2%=0.1821, 61.8%=0.1845, 100%=0.1884, 161.8%=0.1947, -61.8%=0.1719
2025-04-05 01:41:33,093 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:33,096 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:33,119 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.1820964, 0.18450360000000002), SL: 0.1776, TP: 0.1947
2025-04-05 01:41:33,124 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=AAVEUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806492784&recv_window=120000&sign=c889ebc1d5deaebca8d58f1931c6dbef5576157006df5543bab7daec51fd91c6 HTTP/1.1" 200 5650
2025-04-05 01:41:33,127 DEBUG: Request Params: {'category': 'linear', 'symbol': 'A8USDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806493092', 'recv_window': '120000', 'sign': '457a22d350fa9a0cf60290d66e2c5162c5a214cebd734e637abe17185a954e61'}
2025-04-05 01:41:33,135 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:33,142 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:33,170 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:33,171 INFO: N/A 1h: BUY signal, but price (0.0197) is outside entry zone (0.0192-0.0194). Penalty currently disabled for test.
2025-04-05 01:41:33,180 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.1820964, 0.18450360000000002), SL=0.1776, TP=0.1947
2025-04-05 01:41:33,194 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:33,211 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000TOSHIUSDT HTTP/1.1" 200 194
2025-04-05 01:41:33,216 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:33,220 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000TURBOUSDT HTTP/1.1" 200 193
2025-04-05 01:41:33,223 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/0.00 vs Threshold: 3.5
2025-04-05 01:41:33,223 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:33,231 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:33,233 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000XUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806492873&recv_window=120000&sign=dc5f8dc9eb65b1122f7459ae940f465d48abc0fea37b805d9d09c071029d6d2a HTTP/1.1" 200 None
2025-04-05 01:41:33,237 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:41:33,240 DEBUG: Request Params: {'category': 'linear', 'symbol': '1000XECUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806493223', 'recv_window': '120000', 'sign': 'f8f2e51e5dd33e3798b1badc5f539834c41a9b691c4b7b76bb586cd4e4101cd1'}
2025-04-05 01:41:33,244 INFO: N/A 1h: BUY signal, but price (0.1879) is outside entry zone (0.1821-0.1845). Penalty currently disabled for test.
2025-04-05 01:41:33,265 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:33,284 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:33,292 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:33,293 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000NEIROCTOUSDT HTTP/1.1" 200 196
2025-04-05 01:41:33,297 DEBUG: Request Params: {'category': 'linear', 'symbol': 'AAVEUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806493265', 'recv_window': '120000', 'sign': '5144a2e42afad17f8d5afa1a3b0bb049a2a86be0875f6d066059f67e9d4c007a'}
2025-04-05 01:41:33,301 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:33,312 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:33,314 DEBUG: Request Params: {'category': 'linear', 'symbol': '1INCHUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806493301', 'recv_window': '120000', 'sign': 'b0f97b55740a6ac05352c9fab9295319cb5761f8591f9e21662133222790ac8c'}
2025-04-05 01:41:33,323 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:33,328 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000PEPEUSDT HTTP/1.1" 200 197
2025-04-05 01:41:33,386 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000RATSUSDT HTTP/1.1" 200 193
2025-04-05 01:41:33,420 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=A8USDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806493092&recv_window=120000&sign=457a22d350fa9a0cf60290d66e2c5162c5a214cebd734e637abe17185a954e61 HTTP/1.1" 200 None
2025-04-05 01:41:33,424 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000TOSHIUSDT HTTP/1.1" 200 316
2025-04-05 01:41:33,434 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000TURBOUSDT HTTP/1.1" 200 314
2025-04-05 01:41:33,491 DEBUG: (1000TOSHIUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:41:33,498 DEBUG: (1000TURBOUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:41:33,504 DEBUG: (ACEUSDT) Starting analysis for 1h...
2025-04-05 01:41:33,506 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000NEIROCTOUSDT HTTP/1.1" 200 321
2025-04-05 01:41:33,517 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000XUSDT HTTP/1.1" 200 190
2025-04-05 01:41:33,524 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1000XECUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806493223&recv_window=120000&sign=f8f2e51e5dd33e3798b1badc5f539834c41a9b691c4b7b76bb586cd4e4101cd1 HTTP/1.1" 200 None
2025-04-05 01:41:33,524 DEBUG: (ACHUSDT) Starting analysis for 1h...
2025-04-05 01:41:33,529 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000PEPEUSDT HTTP/1.1" 200 323
2025-04-05 01:41:33,533 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:33,533 DEBUG: (1000NEIROCTOUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:33,547 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=AAVEUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806493265&recv_window=120000&sign=5144a2e42afad17f8d5afa1a3b0bb049a2a86be0875f6d066059f67e9d4c007a HTTP/1.1" 200 4905
2025-04-05 01:41:33,552 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=1INCHUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806493301&recv_window=120000&sign=b0f97b55740a6ac05352c9fab9295319cb5761f8591f9e21662133222790ac8c HTTP/1.1" 200 None
2025-04-05 01:41:33,559 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:33,586 DEBUG: (1000PEPEUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:33,591 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ACEUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806493532', 'recv_window': '120000', 'sign': 'ccedf3c013068a6f439569adcec9940362cadbcdbda15595f858802480c9be40'}
2025-04-05 01:41:33,591 DEBUG: (ACTUSDT) Starting analysis for 1h...
2025-04-05 01:41:33,636 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ACHUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806493559', 'recv_window': '120000', 'sign': '80da1fb8448ec63b24faac25f0534f6faef8c69603208017f946c340520f8dc5'}
2025-04-05 01:41:33,649 DEBUG: (ACXUSDT) Starting analysis for 1h...
2025-04-05 01:41:33,660 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:33,662 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000RATSUSDT HTTP/1.1" 200 320
2025-04-05 01:41:33,680 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:33,691 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:33,695 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:33,696 DEBUG: (1000RATSUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:33,698 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ACTUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806493680', 'recv_window': '120000', 'sign': '2ebc7dc069ffb4a6df1376d790304ddef495ca312078cd9b89193d9a151780a9'}
2025-04-05 01:41:33,704 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ACXUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806493695', 'recv_window': '120000', 'sign': 'cdabb31098563906fda5dcdfe711da44ac11d23a7bfabc07d7e95889bf74247e'}
2025-04-05 01:41:33,707 DEBUG: (ADAUSDT) Starting analysis for 1h...
2025-04-05 01:41:33,712 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:33,716 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:33,718 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:33,729 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ADAUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806493718', 'recv_window': '120000', 'sign': '6b9203be4a55a8e665a2c64ac345396aec1dc1fb32381d0ac368c6eb6d8ec393'}
2025-04-05 01:41:33,732 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=A8USDT HTTP/1.1" 200 187
2025-04-05 01:41:33,733 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:33,734 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000XUSDT HTTP/1.1" 200 315
2025-04-05 01:41:33,743 DEBUG: (1000XUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:41:33,747 DEBUG: (AERGOUSDT) Starting analysis for 1h...
2025-04-05 01:41:33,751 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:33,755 DEBUG: Request Params: {'category': 'linear', 'symbol': 'AERGOUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806493751', 'recv_window': '120000', 'sign': '908a1237dbfd6df814e1dd8769835e68b890ec34f867e3587b901875bced3628'}
2025-04-05 01:41:33,759 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:33,791 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1000XECUSDT HTTP/1.1" 200 196
2025-04-05 01:41:33,846 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=AAVEUSDT HTTP/1.1" 200 193
2025-04-05 01:41:33,891 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=1INCHUSDT HTTP/1.1" 200 194
2025-04-05 01:41:33,911 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ACEUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806493532&recv_window=120000&sign=ccedf3c013068a6f439569adcec9940362cadbcdbda15595f858802480c9be40 HTTP/1.1" 200 None
2025-04-05 01:41:33,921 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ACHUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806493559&recv_window=120000&sign=80da1fb8448ec63b24faac25f0534f6faef8c69603208017f946c340520f8dc5 HTTP/1.1" 200 6129
2025-04-05 01:41:33,934 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ACTUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806493680&recv_window=120000&sign=2ebc7dc069ffb4a6df1376d790304ddef495ca312078cd9b89193d9a151780a9 HTTP/1.1" 200 None
2025-04-05 01:41:33,936 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=A8USDT HTTP/1.1" 200 311
2025-04-05 01:41:33,943 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ACXUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806493695&recv_window=120000&sign=cdabb31098563906fda5dcdfe711da44ac11d23a7bfabc07d7e95889bf74247e HTTP/1.1" 200 None
2025-04-05 01:41:33,964 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ADAUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806493718&recv_window=120000&sign=6b9203be4a55a8e665a2c64ac345396aec1dc1fb32381d0ac368c6eb6d8ec393 HTTP/1.1" 200 None
2025-04-05 01:41:33,979 DEBUG: (A8USDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:41:33,988 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=AERGOUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806493751&recv_window=120000&sign=908a1237dbfd6df814e1dd8769835e68b890ec34f867e3587b901875bced3628 HTTP/1.1" 200 None
2025-04-05 01:41:34,001 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/1.00 vs Threshold: 3.5
2025-04-05 01:41:34,012 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1000XECUSDT HTTP/1.1" 200 316
2025-04-05 01:41:34,026 DEBUG: (AEROUSDT) Starting analysis for 1h...
2025-04-05 01:41:34,048 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=AAVEUSDT HTTP/1.1" 200 309
2025-04-05 01:41:34,050 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:41:34,074 DEBUG: (1000XECUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:34,086 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:34,110 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=1INCHUSDT HTTP/1.1" 200 315
2025-04-05 01:41:34,130 DEBUG: (AAVEUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:41:34,142 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:34,147 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 4.50/0.00 vs Threshold: 3.5
2025-04-05 01:41:34,152 DEBUG: (AEVOUSDT) Starting analysis for 1h...
2025-04-05 01:41:34,157 DEBUG: Request Params: {'category': 'linear', 'symbol': 'AEROUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806494086', 'recv_window': '120000', 'sign': '0fc824284d7d938edd6869661bf47ca4fb8b648fdb1430ebddae6b4047c16be7'}
2025-04-05 01:41:34,177 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/1.00 vs Threshold: 3.5
2025-04-05 01:41:34,178 DEBUG: (1INCHUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:34,213 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ACEUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806494142', 'recv_window': '120000', 'sign': '4732cf64d7ab92766d8dc15e3d1158e210e87e5e5f36a0bbe3485470b4a5342f'}
2025-04-05 01:41:34,224 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/0.00 vs Threshold: 3.5
2025-04-05 01:41:34,227 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:34,237 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:34,246 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/0.00 vs Threshold: 3.5
2025-04-05 01:41:34,250 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 1.50/1.50 vs Threshold: 3.5
2025-04-05 01:41:34,251 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:34,251 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:41:34,252 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:34,252 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:41:34,254 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:34,258 DEBUG: Request Params: {'category': 'linear', 'symbol': 'AEVOUSDT', 'interval': '60', 'limit': '70', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806494237', 'recv_window': '120000', 'sign': '573050716905a0435b11b8ef67f9557b5aeb9593184e310e99402b1ab726ed08'}
2025-04-05 01:41:34,262 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:34,265 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 1.50/9.0 | Final Confidence: 16.7% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:41:34,272 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:34,280 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:34,282 DEBUG: (N/A) Last Close: 0.0212, Raw ATR: 0.0003, Valid ATR: 0.0003
2025-04-05 01:41:34,286 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:34,289 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:34,293 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:34,297 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ACTUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806494272', 'recv_window': '120000', 'sign': '7a3dee48dee7a1566dae4cfe532bf32bc30d4e6570154560a152d0fbff7a40d5'}
2025-04-05 01:41:34,300 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ACXUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806494280', 'recv_window': '120000', 'sign': 'e70cdfd46dd92379a5c2406df628e04e0c818fb6de49d17b5392f1bf6cd7eb12'}
2025-04-05 01:41:34,303 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:34,310 DEBUG: (N/A) Last Close: 0.6622, Raw ATR: 0.0104, Valid ATR: 0.0104
2025-04-05 01:41:34,313 DEBUG: Request Params: {'category': 'linear', 'symbol': 'AERGOUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806494293', 'recv_window': '120000', 'sign': '9372614cf55d3c0a49cac73e99df0f1a00335eeb8080d9006e37393454cb956c'}
2025-04-05 01:41:34,318 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:34,322 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:34,325 DEBUG: (N/A) Fib Levels: 0%=0.0203, 38.2%=0.0208, 61.8%=0.0211, 100%=0.0216, 161.8%=0.0224, -61.8%=0.0196
2025-04-05 01:41:34,328 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:34,333 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:34,342 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.020837106, 0.021139894), SL: 0.0203, TP: 0.0224
2025-04-05 01:41:34,346 DEBUG: (N/A) Fib Levels: 0%=0.6271, 38.2%=0.6440, 61.8%=0.6545, 100%=0.6714, 161.8%=0.6988, -61.8%=0.5997
2025-04-05 01:41:34,353 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:34,357 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.6440226, 0.6544774), SL: 0.6245, TP: 0.6988
2025-04-05 01:41:34,361 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.020837106, 0.021139894), SL=0.0203, TP=0.0224
2025-04-05 01:41:34,366 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:34,369 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:34,373 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.6440226, 0.6544774), SL=0.6245, TP=0.6988
2025-04-05 01:41:34,377 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:34,380 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:34,383 INFO: N/A 1h: BUY signal, but price (0.0212) is outside entry zone (0.0208-0.0211). Penalty currently disabled for test.
2025-04-05 01:41:34,386 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:34,390 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 4.50/9.0 | Final Confidence: 50.0% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:34,394 INFO: N/A 1h: BUY signal, but price (0.6622) is outside entry zone (0.6440-0.6545). Penalty currently disabled for test.
2025-04-05 01:41:34,398 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:34,401 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:34,405 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ACHUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806494398', 'recv_window': '120000', 'sign': 'ff174827a43978a7daaee2116814c243846da6c783df0c9180db4be75f08ba27'}
2025-04-05 01:41:34,409 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:34,413 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:34,416 DEBUG: Request Params: {'category': 'linear', 'symbol': 'ADAUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806494409', 'recv_window': '120000', 'sign': '1755182118eeb145ae20f40eacb20e17115a1036fd767e1684f58be41136e511'}
2025-04-05 01:41:34,424 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:34,486 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=AEROUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806494086&recv_window=120000&sign=0fc824284d7d938edd6869661bf47ca4fb8b648fdb1430ebddae6b4047c16be7 HTTP/1.1" 200 None
2025-04-05 01:41:34,492 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ACEUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806494142&recv_window=120000&sign=4732cf64d7ab92766d8dc15e3d1158e210e87e5e5f36a0bbe3485470b4a5342f HTTP/1.1" 200 None
2025-04-05 01:41:34,524 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=AEVOUSDT&interval=60&limit=70&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806494237&recv_window=120000&sign=573050716905a0435b11b8ef67f9557b5aeb9593184e310e99402b1ab726ed08 HTTP/1.1" 200 None
2025-04-05 01:41:34,553 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ACTUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806494272&recv_window=120000&sign=7a3dee48dee7a1566dae4cfe532bf32bc30d4e6570154560a152d0fbff7a40d5 HTTP/1.1" 200 4867
2025-04-05 01:41:34,560 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ACXUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806494280&recv_window=120000&sign=e70cdfd46dd92379a5c2406df628e04e0c818fb6de49d17b5392f1bf6cd7eb12 HTTP/1.1" 200 None
2025-04-05 01:41:34,569 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=AERGOUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806494293&recv_window=120000&sign=9372614cf55d3c0a49cac73e99df0f1a00335eeb8080d9006e37393454cb956c HTTP/1.1" 200 None
2025-04-05 01:41:34,642 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ACHUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806494398&recv_window=120000&sign=ff174827a43978a7daaee2116814c243846da6c783df0c9180db4be75f08ba27 HTTP/1.1" 200 None
2025-04-05 01:41:34,650 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=ADAUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806494409&recv_window=120000&sign=1755182118eeb145ae20f40eacb20e17115a1036fd767e1684f58be41136e511 HTTP/1.1" 200 None
2025-04-05 01:41:34,708 DEBUG: (N/A) Post-Score Check | Initial Signal: HOLD | Score B/S: 2.00/0.50 vs Threshold: 3.5
2025-04-05 01:41:34,729 DEBUG: N/A 1h: Final Check | Initial Signal: HOLD, Final Signal: HOLD | Raw Score: 2.00/9.0 | Final Confidence: 22.2% | Trade Plan Exists: False | Zone Note: ''
2025-04-05 01:41:34,775 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:34,804 DEBUG: Request Params: {'category': 'linear', 'symbol': 'AEROUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806494774', 'recv_window': '120000', 'sign': 'c5dd3c1957648865773e4816ee77aa72ecb960e680016abd7097dff130686a72'}
2025-04-05 01:41:34,886 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:34,943 DEBUG: (N/A) Post-Score Check | Initial Signal: BUY | Score B/S: 3.50/0.00 vs Threshold: 3.5
2025-04-05 01:41:34,973 DEBUG: (N/A) Initial signal is BUY. Attempting trade plan calculation...
2025-04-05 01:41:34,981 DEBUG: --- Calculating Trade Plan for N/A 1h BUY ---
2025-04-05 01:41:34,984 DEBUG: (N/A) Last Close: 0.1000, Raw ATR: 0.0018, Valid ATR: 0.0018
2025-04-05 01:41:34,987 DEBUG: (N/A) Fibs Available: True
2025-04-05 01:41:34,991 DEBUG: (N/A) Fib Levels: 0%=0.0952, 38.2%=0.0975, 61.8%=0.0989, 100%=0.1012, 161.8%=0.1049, -61.8%=0.0915
2025-04-05 01:41:34,995 DEBUG: (N/A) Attempting Fib Plan. Signal: BUY, Zone Tuple: (0.09749200000000001, 0.098908), SL: 0.0948, TP: 0.1049
2025-04-05 01:41:34,998 DEBUG: (N/A) Fib plan validated. Method: Fibonacci Zone
2025-04-05 01:41:35,002 DEBUG: (N/A) Final Plan Output: Method=Fibonacci Zone, Zone=(0.09749200000000001, 0.098908), SL=0.0948, TP=0.1049
2025-04-05 01:41:35,005 DEBUG: --- End Trade Plan Calculation for N/A (Success) ---
2025-04-05 01:41:35,009 DEBUG: (N/A) Trade plan calculation SUCCEEDED. Method: Fibonacci Zone
2025-04-05 01:41:35,012 INFO: N/A 1h: BUY signal, but price (0.1000) is outside entry zone (0.0975-0.0989). Penalty currently disabled for test.
2025-04-05 01:41:35,016 DEBUG: N/A 1h: Final Check | Initial Signal: BUY, Final Signal: BUY | Raw Score: 3.50/9.0 | Final Confidence: 38.9% | Trade Plan Exists: True | Zone Note: '(Pullback Suggested)'
2025-04-05 01:41:35,021 DEBUG: Request URL: https://api.bybit.com/v5/market/kline
2025-04-05 01:41:35,024 DEBUG: Request Params: {'category': 'linear', 'symbol': 'AEVOUSDT', 'interval': '240', 'limit': '60', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': '1743806495021', 'recv_window': '120000', 'sign': '25c6343c5a32df9dcaff617539dbf4de41df527e2919cd5ae7778087e86192f8'}
2025-04-05 01:41:35,029 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:35,047 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=ACEUSDT HTTP/1.1" 200 188
2025-04-05 01:41:35,157 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=ACXUSDT HTTP/1.1" 200 188
2025-04-05 01:41:35,168 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=AERGOUSDT HTTP/1.1" 200 190
2025-04-05 01:41:35,169 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=ACHUSDT HTTP/1.1" 200 191
2025-04-05 01:41:35,185 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=ADAUSDT HTTP/1.1" 200 192
2025-04-05 01:41:35,187 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=AEROUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806494774&recv_window=120000&sign=c5dd3c1957648865773e4816ee77aa72ecb960e680016abd7097dff130686a72 HTTP/1.1" 200 None
2025-04-05 01:41:35,248 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=ACEUSDT HTTP/1.1" 200 310
2025-04-05 01:41:35,249 DEBUG: https://api.bybit.com:443 "GET /v5/market/kline?category=linear&symbol=AEVOUSDT&interval=240&limit=60&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806495021&recv_window=120000&sign=25c6343c5a32df9dcaff617539dbf4de41df527e2919cd5ae7778087e86192f8 HTTP/1.1" 200 None
2025-04-05 01:41:35,256 DEBUG: (ACEUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:41:35,379 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=ACXUSDT HTTP/1.1" 200 310
2025-04-05 01:41:35,386 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=AERGOUSDT HTTP/1.1" 200 315
2025-04-05 01:41:35,393 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=ACHUSDT HTTP/1.1" 200 312
2025-04-05 01:41:35,395 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=ADAUSDT HTTP/1.1" 200 315
2025-04-05 01:41:35,404 DEBUG: (ACXUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:41:35,414 DEBUG: (AERGOUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:41:35,420 DEBUG: (ACHUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:35,425 DEBUG: (ADAUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:35,450 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=AEROUSDT HTTP/1.1" 200 189
2025-04-05 01:41:35,483 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=ACTUSDT HTTP/1.1" 200 188
2025-04-05 01:41:35,665 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=AEROUSDT HTTP/1.1" 200 313
2025-04-05 01:41:35,685 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=ACTUSDT HTTP/1.1" 200 314
2025-04-05 01:41:35,744 DEBUG: (AEROUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:41:35,756 DEBUG: (ACTUSDT) analyze_symbol returning: Signal=HOLD
2025-04-05 01:41:35,863 DEBUG: https://api.bybit.com:443 "GET /v5/market/funding/history?category=linear&limit=1&symbol=AEVOUSDT HTTP/1.1" 200 193
2025-04-05 01:41:36,098 DEBUG: https://api.bybit.com:443 "GET /v5/market/open-interest?category=linear&intervalTime=1h&limit=2&symbol=AEVOUSDT HTTP/1.1" 200 313
2025-04-05 01:41:36,164 DEBUG: (AEVOUSDT) analyze_symbol returning: Signal=BUY
2025-04-05 01:41:36,173 INFO: Status: Analysis complete - 38/38 processed.
2025-04-05 01:41:37,497 DEBUG: Filter 'WAVE_X' set to True
2025-04-05 01:41:37,568 DEBUG: apply_live_filters CALLED
2025-04-05 01:41:37,579 DEBUG: apply_live_filters: Found 38 valid results, 38 after sorting.
2025-04-05 01:41:37,587 DEBUG: Adding row for 10000QUBICUSDT
2025-04-05 01:41:37,595 DEBUG: apply_live_filters FINISHED
2025-04-05 01:41:38,591 DEBUG: Filter 'WAVE_X' set to False
2025-04-05 01:41:38,601 DEBUG: apply_live_filters CALLED
2025-04-05 01:41:38,607 DEBUG: apply_live_filters: Found 38 valid results, 38 after sorting.
2025-04-05 01:41:38,612 DEBUG: Adding row for 1000PEPEUSDT
2025-04-05 01:41:38,618 DEBUG: Adding row for 1000000MOGUSDT
2025-04-05 01:41:38,624 DEBUG: Adding row for 10000QUBICUSDT
2025-04-05 01:41:38,628 DEBUG: Adding row for ACHUSDT
2025-04-05 01:41:38,632 DEBUG: Adding row for 10000000AIDOGEUSDT
2025-04-05 01:41:38,636 DEBUG: Adding row for 1000000BABYDOGEUSDT
2025-04-05 01:41:38,639 DEBUG: Adding row for 1000000CHEEMSUSDT
2025-04-05 01:41:38,643 DEBUG: Adding row for 10000WHYUSDT
2025-04-05 01:41:38,648 DEBUG: Adding row for 1000APUUSDT
2025-04-05 01:41:38,651 DEBUG: Adding row for 1000CATSUSDT
2025-04-05 01:41:38,655 DEBUG: Adding row for 1000MUMUUSDT
2025-04-05 01:41:38,660 DEBUG: Adding row for 1000RATSUSDT
2025-04-05 01:41:38,663 DEBUG: Adding row for 10000ELONUSDT
2025-04-05 01:41:38,667 DEBUG: Adding row for 10000LADYSUSDT
2025-04-05 01:41:38,671 DEBUG: Adding row for 10000WENUSDT
2025-04-05 01:41:38,675 DEBUG: Adding row for 1000BONKUSDT
2025-04-05 01:41:38,679 DEBUG: Adding row for 1000FLOKIUSDT
2025-04-05 01:41:38,684 DEBUG: Adding row for 1000LUNCUSDT
2025-04-05 01:41:38,687 DEBUG: Adding row for 1000NEIROCTOUSDT
2025-04-05 01:41:38,691 DEBUG: Adding row for 1000XECUSDT
2025-04-05 01:41:38,694 DEBUG: Adding row for 1INCHUSDT
2025-04-05 01:41:38,699 DEBUG: Adding row for ADAUSDT
2025-04-05 01:41:38,702 DEBUG: Adding row for AEVOUSDT
2025-04-05 01:41:38,706 DEBUG: Adding row for 1000BTTUSDT
2025-04-05 01:41:38,711 DEBUG: Adding row for 10000COQUSDT
2025-04-05 01:41:38,714 DEBUG: Adding row for 10000SATSUSDT
2025-04-05 01:41:38,718 DEBUG: Adding row for 1000TOSHIUSDT
2025-04-05 01:41:38,721 DEBUG: Adding row for AEROUSDT
2025-04-05 01:41:38,726 DEBUG: Adding row for 1000000PEIPEIUSDT
2025-04-05 01:41:38,730 DEBUG: Adding row for 1000CATUSDT
2025-04-05 01:41:38,733 DEBUG: Adding row for 1000TURBOUSDT
2025-04-05 01:41:38,737 DEBUG: Adding row for 1000XUSDT
2025-04-05 01:41:38,741 DEBUG: Adding row for A8USDT
2025-04-05 01:41:38,745 DEBUG: Adding row for AAVEUSDT
2025-04-05 01:41:38,749 DEBUG: Adding row for ACEUSDT
2025-04-05 01:41:38,754 DEBUG: Adding row for ACTUSDT
2025-04-05 01:41:38,757 DEBUG: Adding row for ACXUSDT
2025-04-05 01:41:38,761 DEBUG: Adding row for AERGOUSDT
2025-04-05 01:41:38,765 DEBUG: apply_live_filters FINISHED
2025-04-05 01:41:39,392 DEBUG: Filter 'SENT_CONF' set to True
2025-04-05 01:41:39,401 DEBUG: apply_live_filters CALLED
2025-04-05 01:41:39,407 DEBUG: apply_live_filters: Found 38 valid results, 38 after sorting.
2025-04-05 01:41:39,412 DEBUG: Adding row for 1INCHUSDT
2025-04-05 01:41:39,416 DEBUG: apply_live_filters FINISHED
2025-04-05 01:41:41,859 INFO: Buy btn for: 1INCHUSDT
2025-04-05 01:41:41,895 INFO: Using direct Bybit client for order placement
2025-04-05 01:41:41,900 INFO: Using leverage 10x for 1INCHUSDT (persistent setting on Bybit)
2025-04-05 01:41:41,906 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:42,136 DEBUG: https://api.bybit.com:443 "GET /v5/market/instruments-info?category=linear&symbol=1INCHUSDT HTTP/1.1" 200 905
2025-04-05 01:41:42,166 INFO: Formatted quantity: 54.6 (min: 0.1, step: 0.1)
2025-04-05 01:41:42,172 INFO: Placing Buy order for 1INCHUSDT: 54.6 @ market price with TP: 0.19470360000000003, SL: 0.17755507330646061
2025-04-05 01:41:42,179 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:42,406 DEBUG: https://api.bybit.com:443 "GET /v5/market/instruments-info?category=linear&symbol=1INCHUSDT HTTP/1.1" 200 905
2025-04-05 01:41:42,441 INFO: Symbol 1INCHUSDT found in linear futures category
2025-04-05 01:41:42,454 INFO: Minimum order quantity for 1INCHUSDT: 0.1
2025-04-05 01:41:42,462 INFO: Price tick size for 1INCHUSDT: 0.0001
2025-04-05 01:41:42,468 INFO: Using category 'linear' for 1INCHUSDT (USDT perpetual futures)
2025-04-05 01:41:42,473 INFO: Using default position mode for 1INCHUSDT (letting API determine positionIdx)
2025-04-05 01:41:42,477 INFO: Order parameters: {'category': 'linear', 'symbol': '1INCHUSDT', 'side': 'Buy', 'orderType': 'Market', 'qty': '54.6', 'takeProfit': '0.19470360000000003', 'stopLoss': '0.17755507330646061', 'timeInForce': 'GTC', 'reduceOnly': False}
2025-04-05 01:41:42,482 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:42,718 DEBUG: https://api.bybit.com:443 "GET /v5/market/time HTTP/1.1" 200 134
2025-04-05 01:41:42,753 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:42,987 DEBUG: https://api.bybit.com:443 "POST /v5/position/switch-mode HTTP/1.1" 200 108
2025-04-05 01:41:43,033 ERROR: Failed to set position mode: Position mode is not modified
2025-04-05 01:41:43,042 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:43,274 DEBUG: https://api.bybit.com:443 "GET /v5/market/instruments-info?category=linear&symbol=1INCHUSDT HTTP/1.1" 200 905
2025-04-05 01:41:43,313 INFO: Formatted quantity from 54.6 to 54.6 (min: 0.1, step: 0.1)
2025-04-05 01:41:43,323 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:43,556 DEBUG: https://api.bybit.com:443 "GET /v5/market/time HTTP/1.1" 200 134
2025-04-05 01:41:43,593 DEBUG: Request URL: https://api.bybit.com/v5/order/create
2025-04-05 01:41:43,602 DEBUG: Request Headers: {'X-BAPI-API-KEY': 'aMKaaFNd57yeENDYF1', 'X-BAPI-SIGN': '0bffd63bd88941cc8ca23a1cfd9e5c2d439c74b46977a1810cc86b00a6dc8eec', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-TIMESTAMP': '1743806499000', 'X-BAPI-RECV-WINDOW': '120000', 'Content-Type': 'application/json'}
2025-04-05 01:41:43,610 DEBUG: Request Body: {"category": "linear", "symbol": "1INCHUSDT", "side": "Buy", "orderType": "Market", "qty": "54.6", "takeProfit": "0.19470360000000003", "stopLoss": "0.17755507330646061", "timeInForce": "GTC", "reduceOnly": false}
2025-04-05 01:41:43,616 DEBUG: Signature String: 1743806499000aMKaaFNd57yeENDYF1120000{"category": "linear", "symbol": "1INCHUSDT", "side": "Buy", "orderType": "Market", "qty": "54.6", "takeProfit": "0.19470360000000003", "stopLoss": "0.17755507330646061", "timeInForce": "GTC", "reduceOnly": false}
2025-04-05 01:41:43,622 DEBUG: Request URL: https://api.bybit.com/v5/order/create
2025-04-05 01:41:43,626 DEBUG: Request Headers: {'X-BAPI-API-KEY': 'aMKaaFNd57yeENDYF1', 'X-BAPI-SIGN': '0bffd63bd88941cc8ca23a1cfd9e5c2d439c74b46977a1810cc86b00a6dc8eec', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-TIMESTAMP': '1743806499000', 'X-BAPI-RECV-WINDOW': '120000', 'Content-Type': 'application/json'}
2025-04-05 01:41:43,630 DEBUG: Request Body: {"category": "linear", "symbol": "1INCHUSDT", "side": "Buy", "orderType": "Market", "qty": "54.6", "takeProfit": "0.19470360000000003", "stopLoss": "0.17755507330646061", "timeInForce": "GTC", "reduceOnly": false}
2025-04-05 01:41:43,634 DEBUG: Signature String: 1743806499000aMKaaFNd57yeENDYF1120000{"category": "linear", "symbol": "1INCHUSDT", "side": "Buy", "orderType": "Market", "qty": "54.6", "takeProfit": "0.19470360000000003", "stopLoss": "0.17755507330646061", "timeInForce": "GTC", "reduceOnly": false}
2025-04-05 01:41:43,638 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:43,865 DEBUG: https://api.bybit.com:443 "POST /v5/order/create HTTP/1.1" 200 141
2025-04-05 01:41:43,900 INFO: Order placed successfully with positionIdx=None
2025-04-05 01:41:43,906 INFO: Order placed successfully for 1INCHUSDT: {'orderId': 'e7eca52e-fff8-4e65-973d-24eb178f67ed', 'orderLinkId': ''}
2025-04-05 01:41:43,914 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:44,496 DEBUG: https://api.bybit.com:443 "GET /v5/market/tickers?category=linear&symbol=1INCHUSDT HTTP/1.1" 200 721
2025-04-05 01:41:44,565 INFO: Saved trade data to file for 1INCHUSDT
2025-04-05 01:41:44,583 INFO: Stored trade data for 1INCHUSDT: {'entry_price': '0.18330000000000002', 'size': '54.6', 'side': 'Buy', 'leverage': '10', 'amount_usd': '100.0', 'risk_amount': '10.0', 'risk_percent': '10.0', 'take_profit': '0.19470360000000003', 'stop_loss': '0.17755507330646061', 'tp_percent': '62.21', 'sl_percent': '31.34', 'created_time': 1743806503.912176, 'created_time_str': '2025-04-05 01:41:43', 'day_of_week': 'Saturday', 'hour_of_day': '01', 'order_id': 'e7eca52e-fff8-4e65-973d-24eb178f67ed', 'order_link_id': '', 'market_data': {'last_price': '0.1879', 'mark_price': '0.1879', 'index_price': '0.1882', 'high_price_24h': '0.1884', 'low_price_24h': '0.1782', 'volume_24h': '9450646.8000', 'turnover_24h': '1742166.4003', 'price_change_percent_24h': '0.012392'}}
2025-04-05 01:41:44,595 INFO: Trade executed for 1INCHUSDT:
Side: Buy
Entry: 0.1833
SL: 0.1776
TP: 0.1947
Leverage: 10x
Position: $100 (Cost: $10)
Order ID: e7eca52e-fff8-4e65-973d-24eb178f67ed
2025-04-05 01:41:55,016 DEBUG: Fetching fresh positions data from API
2025-04-05 01:41:55,097 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:55,370 DEBUG: https://api.bybit.com:443 "GET /v5/market/time HTTP/1.1" 200 134
2025-04-05 01:41:55,378 DEBUG: Request URL: https://api.bybit.com/v5/position/list
2025-04-05 01:41:55,382 DEBUG: Request Params: {'category': 'linear', 'settleCoin': 'USDT', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': 1743806511000, 'recv_window': 120000, 'sign': 'bbee3af227aee1433e36e430a91be35caa5ddde52927809de9dc9bf4a72bcc72'}
2025-04-05 01:41:55,387 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:41:55,615 DEBUG: https://api.bybit.com:443 "GET /v5/position/list?category=linear&settleCoin=USDT&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806511000&recv_window=120000&sign=bbee3af227aee1433e36e430a91be35caa5ddde52927809de9dc9bf4a72bcc72 HTTP/1.1" ************-04-05 01:41:55,657 INFO: Saved 1 positions as last known positions
2025-04-05 01:41:55,666 DEBUG: Using cached positions data (age: 0.7s)
2025-04-05 01:41:55,675 DEBUG: Saved 1 positions to last_positions.json
2025-04-05 01:42:53,019 INFO: Send btn for: 1INCHUSDT
2025-04-05 01:42:53,045 DEBUG: Starting new HTTPS connection (1): api.telegram.org:443
2025-04-05 01:42:53,321 DEBUG: https://api.telegram.org:443 "POST /bot7822469302:AAHPbVln36NE1f0XhBwQMinnkbHENl_zfcw/sendMessage HTTP/1.1" 200 1488
2025-04-05 01:42:53,330 INFO: Sent TG msg for 1INCHUSDT.
2025-04-05 01:43:04,784 DEBUG: Filter 'SENT_CONF' set to False
2025-04-05 01:43:04,790 DEBUG: apply_live_filters CALLED
2025-04-05 01:43:04,795 DEBUG: apply_live_filters: Found 38 valid results, 38 after sorting.
2025-04-05 01:43:04,803 DEBUG: Adding row for 1000PEPEUSDT
2025-04-05 01:43:04,807 DEBUG: Adding row for 1000000MOGUSDT
2025-04-05 01:43:04,813 DEBUG: Adding row for 10000QUBICUSDT
2025-04-05 01:43:04,816 DEBUG: Adding row for ACHUSDT
2025-04-05 01:43:04,819 DEBUG: Adding row for 10000000AIDOGEUSDT
2025-04-05 01:43:04,822 DEBUG: Adding row for 1000000BABYDOGEUSDT
2025-04-05 01:43:04,826 DEBUG: Adding row for 1000000CHEEMSUSDT
2025-04-05 01:43:04,829 DEBUG: Adding row for 10000WHYUSDT
2025-04-05 01:43:04,832 DEBUG: Adding row for 1000APUUSDT
2025-04-05 01:43:04,835 DEBUG: Adding row for 1000CATSUSDT
2025-04-05 01:43:04,839 DEBUG: Adding row for 1000MUMUUSDT
2025-04-05 01:43:04,841 DEBUG: Adding row for 1000RATSUSDT
2025-04-05 01:43:04,845 DEBUG: Adding row for 10000ELONUSDT
2025-04-05 01:43:04,848 DEBUG: Adding row for 10000LADYSUSDT
2025-04-05 01:43:04,852 DEBUG: Adding row for 10000WENUSDT
2025-04-05 01:43:04,855 DEBUG: Adding row for 1000BONKUSDT
2025-04-05 01:43:04,858 DEBUG: Adding row for 1000FLOKIUSDT
2025-04-05 01:43:04,861 DEBUG: Adding row for 1000LUNCUSDT
2025-04-05 01:43:04,865 DEBUG: Adding row for 1000NEIROCTOUSDT
2025-04-05 01:43:04,868 DEBUG: Adding row for 1000XECUSDT
2025-04-05 01:43:04,871 DEBUG: Adding row for 1INCHUSDT
2025-04-05 01:43:04,874 DEBUG: Adding row for ADAUSDT
2025-04-05 01:43:04,878 DEBUG: Adding row for AEVOUSDT
2025-04-05 01:43:04,881 DEBUG: Adding row for 1000BTTUSDT
2025-04-05 01:43:04,884 DEBUG: Adding row for 10000COQUSDT
2025-04-05 01:43:04,887 DEBUG: Adding row for 10000SATSUSDT
2025-04-05 01:43:04,891 DEBUG: Adding row for 1000TOSHIUSDT
2025-04-05 01:43:04,894 DEBUG: Adding row for AEROUSDT
2025-04-05 01:43:04,898 DEBUG: Adding row for 1000000PEIPEIUSDT
2025-04-05 01:43:04,900 DEBUG: Adding row for 1000CATUSDT
2025-04-05 01:43:04,904 DEBUG: Adding row for 1000TURBOUSDT
2025-04-05 01:43:04,907 DEBUG: Adding row for 1000XUSDT
2025-04-05 01:43:04,910 DEBUG: Adding row for A8USDT
2025-04-05 01:43:04,913 DEBUG: Adding row for AAVEUSDT
2025-04-05 01:43:04,917 DEBUG: Adding row for ACEUSDT
2025-04-05 01:43:04,920 DEBUG: Adding row for ACTUSDT
2025-04-05 01:43:04,924 DEBUG: Adding row for ACXUSDT
2025-04-05 01:43:04,926 DEBUG: Adding row for AERGOUSDT
2025-04-05 01:43:04,930 DEBUG: apply_live_filters FINISHED
2025-04-05 01:43:12,773 DEBUG: Filter 'SENT_CONF' set to True
2025-04-05 01:43:12,796 DEBUG: apply_live_filters CALLED
2025-04-05 01:43:12,800 DEBUG: apply_live_filters: Found 38 valid results, 38 after sorting.
2025-04-05 01:43:12,805 DEBUG: Adding row for 1INCHUSDT
2025-04-05 01:43:12,808 DEBUG: apply_live_filters FINISHED
2025-04-05 01:43:15,414 DEBUG: Filter 'SENT_CONF' set to False
2025-04-05 01:43:15,436 DEBUG: apply_live_filters CALLED
2025-04-05 01:43:15,442 DEBUG: apply_live_filters: Found 38 valid results, 38 after sorting.
2025-04-05 01:43:15,446 DEBUG: Adding row for 1000PEPEUSDT
2025-04-05 01:43:15,450 DEBUG: Adding row for 1000000MOGUSDT
2025-04-05 01:43:15,455 DEBUG: Adding row for 10000QUBICUSDT
2025-04-05 01:43:15,458 DEBUG: Adding row for ACHUSDT
2025-04-05 01:43:15,461 DEBUG: Adding row for 10000000AIDOGEUSDT
2025-04-05 01:43:15,464 DEBUG: Adding row for 1000000BABYDOGEUSDT
2025-04-05 01:43:15,469 DEBUG: Adding row for 1000000CHEEMSUSDT
2025-04-05 01:43:15,471 DEBUG: Adding row for 10000WHYUSDT
2025-04-05 01:43:15,475 DEBUG: Adding row for 1000APUUSDT
2025-04-05 01:43:15,477 DEBUG: Adding row for 1000CATSUSDT
2025-04-05 01:43:15,482 DEBUG: Adding row for 1000MUMUUSDT
2025-04-05 01:43:15,484 DEBUG: Adding row for 1000RATSUSDT
2025-04-05 01:43:15,488 DEBUG: Adding row for 10000ELONUSDT
2025-04-05 01:43:15,491 DEBUG: Adding row for 10000LADYSUSDT
2025-04-05 01:43:15,495 DEBUG: Adding row for 10000WENUSDT
2025-04-05 01:43:15,497 DEBUG: Adding row for 1000BONKUSDT
2025-04-05 01:43:15,501 DEBUG: Adding row for 1000FLOKIUSDT
2025-04-05 01:43:15,504 DEBUG: Adding row for 1000LUNCUSDT
2025-04-05 01:43:15,508 DEBUG: Adding row for 1000NEIROCTOUSDT
2025-04-05 01:43:15,511 DEBUG: Adding row for 1000XECUSDT
2025-04-05 01:43:15,514 DEBUG: Adding row for 1INCHUSDT
2025-04-05 01:43:15,517 DEBUG: Adding row for ADAUSDT
2025-04-05 01:43:15,521 DEBUG: Adding row for AEVOUSDT
2025-04-05 01:43:15,523 DEBUG: Adding row for 1000BTTUSDT
2025-04-05 01:43:15,528 DEBUG: Adding row for 10000COQUSDT
2025-04-05 01:43:15,530 DEBUG: Adding row for 10000SATSUSDT
2025-04-05 01:43:15,535 DEBUG: Adding row for 1000TOSHIUSDT
2025-04-05 01:43:15,537 DEBUG: Adding row for AEROUSDT
2025-04-05 01:43:15,541 DEBUG: Adding row for 1000000PEIPEIUSDT
2025-04-05 01:43:15,544 DEBUG: Adding row for 1000CATUSDT
2025-04-05 01:43:15,547 DEBUG: Adding row for 1000TURBOUSDT
2025-04-05 01:43:15,550 DEBUG: Adding row for 1000XUSDT
2025-04-05 01:43:15,554 DEBUG: Adding row for A8USDT
2025-04-05 01:43:15,556 DEBUG: Adding row for AAVEUSDT
2025-04-05 01:43:15,561 DEBUG: Adding row for ACEUSDT
2025-04-05 01:43:15,563 DEBUG: Adding row for ACTUSDT
2025-04-05 01:43:15,567 DEBUG: Adding row for ACXUSDT
2025-04-05 01:43:15,570 DEBUG: Adding row for AERGOUSDT
2025-04-05 01:43:15,574 DEBUG: apply_live_filters FINISHED
2025-04-05 01:43:27,699 ERROR: Error confirming cache clear: 'AdvancedTradingBot' object has no attribute 'data_cache'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\SCri\huinav2.pyw", line 3704, in confirm_clear_cache
    self.bot.data_cache.clear()
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'AdvancedTradingBot' object has no attribute 'data_cache'
2025-04-05 01:43:42,358 INFO: Compact mode enabled
2025-04-05 01:44:01,698 DEBUG: Fetching fresh positions data from API
2025-04-05 01:44:01,704 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:44:01,976 DEBUG: https://api.bybit.com:443 "GET /v5/market/time HTTP/1.1" 200 134
2025-04-05 01:44:01,992 DEBUG: Request URL: https://api.bybit.com/v5/position/list
2025-04-05 01:44:02,000 DEBUG: Request Params: {'category': 'linear', 'settleCoin': 'USDT', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': 1743806638000, 'recv_window': 120000, 'sign': '61e8853b69e4079e5fbc685f098e9167f070aa1a8f30ccc8bebd7cafbc7fc86a'}
2025-04-05 01:44:02,008 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:44:02,251 DEBUG: https://api.bybit.com:443 "GET /v5/position/list?category=linear&settleCoin=USDT&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806638000&recv_window=120000&sign=61e8853b69e4079e5fbc685f098e9167f070aa1a8f30ccc8bebd7cafbc7fc86a HTTP/1.1" ************-04-05 01:44:02,280 INFO: Saved 1 positions as last known positions
2025-04-05 01:44:02,285 DEBUG: Using cached positions data (age: 0.6s)
2025-04-05 01:44:02,292 DEBUG: Saved 1 positions to last_positions.json
2025-04-05 01:44:55,158 DEBUG: Fetching fresh positions data from API
2025-04-05 01:44:55,164 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:44:55,385 DEBUG: https://api.bybit.com:443 "GET /v5/market/time HTTP/1.1" 200 134
2025-04-05 01:44:55,427 DEBUG: Request URL: https://api.bybit.com/v5/position/list
2025-04-05 01:44:55,430 DEBUG: Request Params: {'category': 'linear', 'settleCoin': 'USDT', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': 1743806691000, 'recv_window': 120000, 'sign': '88ce31cfb8c16212429ee9ba3c7f5a509bb9d550521d9b991ee7fa18f5f592fa'}
2025-04-05 01:44:55,435 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:44:55,667 DEBUG: https://api.bybit.com:443 "GET /v5/position/list?category=linear&settleCoin=USDT&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806691000&recv_window=120000&sign=88ce31cfb8c16212429ee9ba3c7f5a509bb9d550521d9b991ee7fa18f5f592fa HTTP/1.1" ************-04-05 01:44:55,697 INFO: Saved 1 positions as last known positions
2025-04-05 01:44:55,703 DEBUG: Using cached positions data (age: 0.5s)
2025-04-05 01:44:55,713 DEBUG: Saved 1 positions to last_positions.json
2025-04-05 01:45:11,725 INFO: Started auto-refresh positions thread
2025-04-05 01:45:29,305 INFO: Stopped auto-refresh positions
2025-04-05 01:45:30,092 INFO: Started auto-refresh positions thread
2025-04-05 01:45:30,972 DEBUG: Fetching fresh positions data from API
2025-04-05 01:45:30,976 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:45:31,277 DEBUG: https://api.bybit.com:443 "GET /v5/market/time HTTP/1.1" 200 134
2025-04-05 01:45:31,295 DEBUG: Request URL: https://api.bybit.com/v5/position/list
2025-04-05 01:45:31,303 DEBUG: Request Params: {'category': 'linear', 'settleCoin': 'USDT', 'api_key': 'aMKaaFNd57yeENDYF1', 'timestamp': 1743806727000, 'recv_window': 120000, 'sign': '229e01b3f3a3d6e2aa942592a5db8b14b45a2930bd0bbdfe9bc590e4dcebe907'}
2025-04-05 01:45:31,311 DEBUG: Starting new HTTPS connection (1): api.bybit.com:443
2025-04-05 01:45:31,545 DEBUG: https://api.bybit.com:443 "GET /v5/position/list?category=linear&settleCoin=USDT&api_key=aMKaaFNd57yeENDYF1&timestamp=1743806727000&recv_window=120000&sign=229e01b3f3a3d6e2aa942592a5db8b14b45a2930bd0bbdfe9bc590e4dcebe907 HTTP/1.1" ************-04-05 01:45:31,588 INFO: Saved 1 positions as last known positions
2025-04-05 01:45:31,594 DEBUG: Using cached positions data (age: 0.6s)
2025-04-05 01:45:31,601 DEBUG: Saved 1 positions to last_positions.json
2025-04-05 01:45:35,933 INFO: Stopped auto-refresh positions
2025-04-05 01:45:36,481 INFO: Started auto-refresh positions thread
2025-04-05 01:46:09,012 INFO: Stopped auto-refresh positions
2025-04-05 01:46:17,669 DEBUG: Filter 'DIV_CONF' set to True
2025-04-05 01:46:17,685 DEBUG: apply_live_filters CALLED
2025-04-05 01:46:17,689 DEBUG: apply_live_filters: Found 38 valid results, 38 after sorting.
2025-04-05 01:46:17,693 DEBUG: apply_live_filters FINISHED
2025-04-05 01:46:18,257 DEBUG: Filter 'DIV_CONF' set to False
2025-04-05 01:46:18,334 DEBUG: apply_live_filters CALLED
2025-04-05 01:46:18,340 DEBUG: apply_live_filters: Found 38 valid results, 38 after sorting.
2025-04-05 01:46:18,344 DEBUG: Adding row for 1000PEPEUSDT
2025-04-05 01:46:18,346 DEBUG: Adding row for 1000000MOGUSDT
2025-04-05 01:46:18,351 DEBUG: Adding row for 10000QUBICUSDT
2025-04-05 01:46:18,353 DEBUG: Adding row for ACHUSDT
2025-04-05 01:46:18,357 DEBUG: Adding row for 10000000AIDOGEUSDT
2025-04-05 01:46:18,360 DEBUG: Adding row for 1000000BABYDOGEUSDT
2025-04-05 01:46:18,364 DEBUG: Adding row for 1000000CHEEMSUSDT
2025-04-05 01:46:18,367 DEBUG: Adding row for 10000WHYUSDT
2025-04-05 01:46:18,371 DEBUG: Adding row for 1000APUUSDT
2025-04-05 01:46:18,373 DEBUG: Adding row for 1000CATSUSDT
2025-04-05 01:46:18,377 DEBUG: Adding row for 1000MUMUUSDT
2025-04-05 01:46:18,381 DEBUG: Adding row for 1000RATSUSDT
2025-04-05 01:46:18,384 DEBUG: Adding row for 10000ELONUSDT
2025-04-05 01:46:18,387 DEBUG: Adding row for 10000LADYSUSDT
2025-04-05 01:46:18,391 DEBUG: Adding row for 10000WENUSDT
2025-04-05 01:46:18,394 DEBUG: Adding row for 1000BONKUSDT
2025-04-05 01:46:18,397 DEBUG: Adding row for 1000FLOKIUSDT
2025-04-05 01:46:18,401 DEBUG: Adding row for 1000LUNCUSDT
2025-04-05 01:46:18,405 DEBUG: Adding row for 1000NEIROCTOUSDT
2025-04-05 01:46:18,407 DEBUG: Adding row for 1000XECUSDT
2025-04-05 01:46:18,411 DEBUG: Adding row for 1INCHUSDT
2025-04-05 01:46:18,412 DEBUG: Adding row for ADAUSDT
2025-04-05 01:46:18,418 DEBUG: Adding row for AEVOUSDT
2025-04-05 01:46:18,420 DEBUG: Adding row for 1000BTTUSDT
2025-04-05 01:46:18,424 DEBUG: Adding row for 10000COQUSDT
2025-04-05 01:46:18,425 DEBUG: Adding row for 10000SATSUSDT
2025-04-05 01:46:18,431 DEBUG: Adding row for 1000TOSHIUSDT
2025-04-05 01:46:18,432 DEBUG: Adding row for AEROUSDT
2025-04-05 01:46:18,437 DEBUG: Adding row for 1000000PEIPEIUSDT
2025-04-05 01:46:18,439 DEBUG: Adding row for 1000CATUSDT
2025-04-05 01:46:18,444 DEBUG: Adding row for 1000TURBOUSDT
2025-04-05 01:46:18,446 DEBUG: Adding row for 1000XUSDT
2025-04-05 01:46:18,449 DEBUG: Adding row for A8USDT
2025-04-05 01:46:18,453 DEBUG: Adding row for AAVEUSDT
2025-04-05 01:46:18,457 DEBUG: Adding row for ACEUSDT
2025-04-05 01:46:18,460 DEBUG: Adding row for ACTUSDT
2025-04-05 01:46:18,463 DEBUG: Adding row for ACXUSDT
2025-04-05 01:46:18,465 DEBUG: Adding row for AERGOUSDT
2025-04-05 01:46:18,470 DEBUG: apply_live_filters FINISHED
2025-04-05 01:46:34,001 INFO: DPG main loop ended
2025-04-05 01:46:34,050 INFO: Shutting down GUI...
2025-04-05 01:46:34,056 INFO: GUI run completed.
2025-04-05 01:46:34,061 INFO: Shutting down application...
