2025-07-09 15:41:01,468 - root - ERROR - Error in bot polling: There is no current event loop in thread 'Thread-1 (_run_bot)'.
2025-07-09 15:41:01,468 - root - INFO - Telegram bot listener started
2025-07-09 15:41:01,818 - root - INFO - Trading Dashboard initialized successfully
2025-07-09 15:41:02,894 - root - ERROR - Error fetching kline data for BTCUSDT: Python int too large to convert to C long
2025-07-09 15:41:03,115 - root - ERROR - Error fetching kline data for BTCUSDT: Python int too large to convert to C long
2025-07-09 15:41:04,093 - root - ERROR - Error fetching kline data for ETHUSDT: Python int too large to convert to C long
2025-07-09 15:41:04,314 - root - ERROR - Error fetching kline data for ETHUSDT: Python int too large to convert to C long
2025-07-09 15:41:05,301 - root - ERROR - Error fetching kline data for SOLUSDT: Python int too large to convert to C long
2025-07-09 15:41:05,521 - root - ERROR - Error fetching kline data for SOLUSDT: Python int too large to convert to C long
2025-07-09 15:41:06,503 - root - ERROR - Error fetching kline data for ADAUSDT: Python int too large to convert to C long
2025-07-09 15:41:06,727 - root - ERROR - Error fetching kline data for ADAUSDT: Python int too large to convert to C long
2025-07-09 15:41:07,741 - root - ERROR - Error fetching kline data for DOTUSDT: Python int too large to convert to C long
2025-07-09 15:41:07,957 - root - ERROR - Error fetching kline data for DOTUSDT: Python int too large to convert to C long
2025-07-09 15:41:38,939 - root - ERROR - Error fetching kline data for BTCUSDT: Python int too large to convert to C long
2025-07-09 15:41:39,158 - root - ERROR - Error fetching kline data for BTCUSDT: Python int too large to convert to C long
2025-07-09 15:41:40,203 - root - ERROR - Error fetching kline data for ETHUSDT: Python int too large to convert to C long
2025-07-09 15:41:40,425 - root - ERROR - Error fetching kline data for ETHUSDT: Python int too large to convert to C long
2025-07-09 15:41:41,538 - root - ERROR - Error fetching kline data for SOLUSDT: Python int too large to convert to C long
2025-07-09 15:41:41,765 - root - ERROR - Error fetching kline data for SOLUSDT: Python int too large to convert to C long
2025-07-09 15:41:42,740 - root - ERROR - Error fetching kline data for ADAUSDT: Python int too large to convert to C long
2025-07-09 15:41:42,961 - root - ERROR - Error fetching kline data for ADAUSDT: Python int too large to convert to C long
2025-07-09 15:41:43,963 - root - ERROR - Error fetching kline data for DOTUSDT: Python int too large to convert to C long
2025-07-09 15:41:44,182 - root - ERROR - Error fetching kline data for DOTUSDT: Python int too large to convert to C long
2025-07-09 15:44:24,923 - root - ERROR - Error in bot polling: There is no current event loop in thread 'Thread-1 (_run_bot)'.
2025-07-09 15:44:24,923 - root - INFO - Telegram bot listener started
2025-07-09 15:44:25,143 - root - INFO - Trading Dashboard initialized successfully
2025-07-09 15:44:31,808 - root - INFO - Telegram message sent successfully
2025-07-09 15:44:31,951 - root - INFO - Telegram message sent successfully
2025-07-09 15:45:38,650 - root - INFO - Telegram bot listener stopped
2025-07-09 15:49:24,099 - root - INFO - Telegram bot connected: signal_lv_bot
2025-07-09 15:49:24,100 - root - INFO - Telegram notification system started (send-only mode)
2025-07-09 15:49:24,341 - root - INFO - Trading Dashboard initialized successfully
2025-07-09 15:49:30,932 - root - INFO - Telegram message sent successfully
2025-07-09 15:49:31,093 - root - INFO - Telegram message sent successfully
2025-07-09 15:49:31,238 - root - INFO - Telegram message sent successfully
2025-07-09 15:51:34,478 - root - INFO - Telegram message sent successfully
2025-07-09 15:52:58,247 - root - INFO - Telegram message sent successfully
2025-07-09 15:54:52,805 - root - INFO - Telegram message sent successfully
2025-07-09 15:54:53,003 - root - INFO - Telegram message sent successfully
2025-07-09 15:56:49,461 - root - INFO - Telegram message sent successfully
2025-07-09 15:58:48,310 - root - INFO - Telegram message sent successfully
2025-07-09 15:59:18,135 - root - INFO - Telegram notification system stopped
2025-07-09 16:00:06,479 - root - INFO - Telegram bot connected: signal_lv_bot
2025-07-09 16:00:06,480 - root - INFO - Telegram notification system started (send-only mode)
2025-07-09 16:00:06,788 - root - INFO - Trading Dashboard initialized successfully
2025-07-09 16:03:35,754 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:35,961 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:36,129 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:36,327 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:36,476 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:36,650 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:36,825 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:36,988 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:37,144 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:37,279 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:37,438 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:37,628 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:37,797 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:38,085 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:38,290 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:38,481 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:38,684 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:38,872 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:39,035 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:39,192 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:39,340 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:39,526 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:39,675 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:39,838 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:40,008 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:40,160 - root - INFO - Telegram message sent successfully
2025-07-09 16:03:40,400 - root - INFO - Telegram message sent successfully
2025-07-09 16:04:33,168 - root - INFO - Telegram message sent successfully
2025-07-09 16:04:33,321 - root - INFO - Telegram message sent successfully
2025-07-09 16:04:33,474 - root - INFO - Telegram message sent successfully
2025-07-09 16:05:29,544 - root - INFO - Telegram message sent successfully
2025-07-09 16:05:29,792 - root - INFO - Telegram message sent successfully
2025-07-09 16:05:30,126 - root - INFO - Telegram message sent successfully
2025-07-09 16:06:26,124 - root - INFO - Telegram message sent successfully
2025-07-09 16:06:26,572 - root - INFO - Telegram message sent successfully
2025-07-09 16:06:26,970 - root - INFO - Telegram message sent successfully
2025-07-09 16:07:22,295 - root - INFO - Telegram message sent successfully
2025-07-09 16:21:53,727 - root - INFO - Telegram bot connected: signal_lv_bot
2025-07-09 16:21:53,728 - root - INFO - Telegram notification system started (send-only mode)
2025-07-09 16:21:53,966 - root - INFO - Funding Rate & OI Checker initialized successfully
2025-07-09 16:22:18,708 - root - INFO - Baseline data reset for new scan
2025-07-09 16:22:18,708 - root - INFO - Manual scan started - baseline data reset
2025-07-09 16:22:18,709 - root - INFO - Manual refresh starting for 20 symbols
2025-07-09 16:22:18,710 - root - INFO - Fetching data for 20 symbols in batches of 25...
2025-07-09 16:22:18,710 - root - INFO - Processing batch 1/1: symbols 1-20
2025-07-09 16:22:45,377 - root - INFO - Data fetch completed: 20 successful, 0 failed
2025-07-09 16:22:45,377 - root - INFO - Manual refresh completed successfully for 20 symbols
2025-07-09 16:22:45,379 - root - INFO - Initial baseline scan completed for 20 symbols
2025-07-09 16:22:45,379 - root - INFO - Manual scan completed: {'initial_scan_completed': True, 'auto_refresh_enabled': False, 'baseline_symbols_count': 20, 'scan_count': 20, 'should_evaluate_alerts': False}
2025-07-09 16:22:57,165 - root - INFO - Alert timing: Auto-refresh enabled
2025-07-09 16:22:57,165 - root - INFO - Auto-refresh enabled - alert evaluation activated
2025-07-09 16:22:57,165 - root - INFO - Fetching data for 20 symbols in batches of 50...
2025-07-09 16:22:57,165 - root - INFO - Processing batch 1/1: symbols 1-20
2025-07-09 16:23:23,693 - root - INFO - Data fetch completed: 20 successful, 0 failed
2025-07-09 16:23:31,839 - root - INFO - Alert timing: Auto-refresh disabled
2025-07-09 16:23:53,694 - root - INFO - Auto-refresh disabled - alert evaluation paused
2025-07-09 16:24:17,656 - root - INFO - Loaded 447 assets
2025-07-09 16:24:38,305 - root - INFO - Alert timing: Auto-refresh enabled
2025-07-09 16:24:38,305 - root - INFO - Auto-refresh enabled - alert evaluation activated
2025-07-09 16:24:38,306 - root - INFO - Fetching data for 50 symbols in batches of 50...
2025-07-09 16:24:38,306 - root - INFO - Processing batch 1/1: symbols 1-50
2025-07-09 16:25:44,674 - root - WARNING - Invalid data received for ALEOUSDT... (+397 MORE)
2025-07-09 16:25:44,775 - root - INFO - Data fetch completed: 49 successful, 1 failed
2025-07-09 16:25:44,775 - root - WARNING - Failed symbols: ALEOUSDT... (+397 MORE)
2025-07-09 16:25:44,776 - root - INFO - Evaluating alerts for ETHUSDT (significant changes detected)
2025-07-09 16:25:44,776 - root - INFO - Evaluating alerts for ETHWUSDT (significant changes detected)
2025-07-09 16:25:44,776 - root - INFO - Evaluating alerts for 1000000BABYDOGEUSDT (significant changes detected)
2025-07-09 16:25:44,776 - root - INFO - Evaluating alerts for 1000000CHEEMSUSDT (significant changes detected)
2025-07-09 16:25:44,777 - root - INFO - Evaluating alerts for 10000ELONUSDT (significant changes detected)
2025-07-09 16:25:44,777 - root - INFO - Evaluating alerts for 10000SATSUSDT (significant changes detected)
2025-07-09 16:25:44,777 - root - INFO - Evaluating alerts for 10000WENUSDT (significant changes detected)
2025-07-09 16:25:44,778 - root - INFO - Evaluating alerts for 1000BONKUSDT (significant changes detected)
2025-07-09 16:25:44,778 - root - INFO - Evaluating alerts for 1000CATUSDT (significant changes detected)
2025-07-09 16:26:14,776 - root - INFO - Fetching data for 50 symbols in batches of 50...
2025-07-09 16:26:14,776 - root - INFO - Processing batch 1/1: symbols 1-50
2025-07-09 16:27:21,891 - root - WARNING - Invalid data received for ALEOUSDT... (+397 MORE)
2025-07-09 16:27:21,992 - root - INFO - Data fetch completed: 49 successful, 1 failed
2025-07-09 16:27:21,992 - root - WARNING - Failed symbols: ALEOUSDT... (+397 MORE)
2025-07-09 16:27:21,993 - root - INFO - Evaluating alerts for BTCUSDT (significant changes detected)
2025-07-09 16:27:21,993 - root - INFO - Evaluating alerts for ETHFIUSDT (significant changes detected)
2025-07-09 16:27:21,993 - root - INFO - Evaluating alerts for ETHUSDT (significant changes detected)
2025-07-09 16:27:21,994 - root - INFO - Evaluating alerts for ETHWUSDT (significant changes detected)
2025-07-09 16:27:21,994 - root - INFO - Evaluating alerts for 1000000CHEEMSUSDT (significant changes detected)
2025-07-09 16:27:21,994 - root - INFO - Evaluating alerts for 1000000MOGUSDT (significant changes detected)
2025-07-09 16:27:21,995 - root - INFO - Evaluating alerts for 10000ELONUSDT (significant changes detected)
2025-07-09 16:27:21,995 - root - INFO - Evaluating alerts for 10000SATSUSDT (significant changes detected)
2025-07-09 16:27:21,995 - root - INFO - Evaluating alerts for 10000WENUSDT (significant changes detected)
2025-07-09 16:27:21,995 - root - INFO - Evaluating alerts for 10000WHYUSDT (significant changes detected)
2025-07-09 16:27:21,996 - root - INFO - Evaluating alerts for 1000BONKUSDT (significant changes detected)
2025-07-09 16:27:21,996 - root - INFO - Evaluating alerts for 1000BTTUSDT (significant changes detected)
2025-07-09 16:27:51,993 - root - INFO - Fetching data for 50 symbols in batches of 50...
2025-07-09 16:27:51,993 - root - INFO - Processing batch 1/1: symbols 1-50
2025-07-09 16:42:39,696 - root - INFO - Using hybrid WebSocket + REST data provider
2025-07-09 16:42:39,808 - root - INFO - Telegram bot connected: signal_lv_bot
2025-07-09 16:42:39,808 - root - INFO - Telegram notification system started (send-only mode)
2025-07-09 16:42:40,065 - root - INFO - Funding Rate & OI Checker initialized successfully
2025-07-09 16:42:48,641 - root - ERROR - Error loading symbols: 'HybridDataProvider' object has no attribute 'fetch_asset_list'
