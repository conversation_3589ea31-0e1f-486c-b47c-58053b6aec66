import time
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from pybit.unified_trading import HTTP
from config import api_key, api_secret
import requests

class DataProvider:
    """Handles all data fetching and processing for the trading dashboard"""
    
    def __init__(self):
        self.session = HTTP(
            testnet=False,
            api_key=api_key,
            api_secret=api_secret,
        )
        self.cache = {}
        self.cache_duration = 30  # seconds
        
    def fetch_funding_rate(self, symbol: str) -> Tuple[Optional[float], Optional[str]]:
        """Fetch current funding rate for a symbol"""
        cache_key = f"{symbol}_funding"
        
        # Check cache
        if cache_key in self.cache:
            data, timestamp = self.cache[cache_key]
            if time.time() - timestamp < self.cache_duration:
                return data
        
        try:
            response = self.session.get_funding_rate_history(
                category="linear",
                symbol=symbol,
                limit=1
            )
            
            if response['retCode'] == 0 and response['result']['list']:
                data = response['result']['list'][0]
                funding_rate = float(data['fundingRate'])
                timestamp_str = datetime.fromtimestamp(
                    int(data['fundingRateTimestamp']) / 1000
                ).strftime('%H:%M UTC')
                
                result = (funding_rate, timestamp_str)
                self.cache[cache_key] = (result, time.time())
                return result
                
        except Exception as e:
            logging.error(f"Error fetching funding rate for {symbol}: {e}")
        
        return None, None
    
    def fetch_open_interest(self, symbol: str) -> Tuple[Optional[float], Optional[float]]:
        """Fetch open interest and calculate change"""
        cache_key = f"{symbol}_oi"
        
        # Check cache
        if cache_key in self.cache:
            data, timestamp = self.cache[cache_key]
            if time.time() - timestamp < self.cache_duration:
                return data
        
        try:
            response = self.session.get_open_interest(
                category="linear",
                symbol=symbol,
                intervalTime="1h",
                limit=2
            )
            
            if response['retCode'] == 0 and response['result']['list']:
                data = response['result']['list']
                if len(data) >= 2:
                    current_oi = float(data[0]['openInterest'])
                    previous_oi = float(data[1]['openInterest'])
                    
                    oi_change = (current_oi - previous_oi) / previous_oi if previous_oi > 0 else 0
                    
                    result = (current_oi, oi_change)
                    self.cache[cache_key] = (result, time.time())
                    return result
                    
        except Exception as e:
            logging.error(f"Error fetching open interest for {symbol}: {e}")
        
        return None, None
    
    def fetch_kline_data(self, symbol: str, interval: str = "60", limit: int = 100) -> pd.DataFrame:
        """Fetch kline data for CVD and volume analysis"""
        cache_key = f"{symbol}_kline_{interval}_{limit}"
        
        # Check cache
        if cache_key in self.cache:
            data, timestamp = self.cache[cache_key]
            if time.time() - timestamp < self.cache_duration:
                return data
        
        try:
            response = self.session.get_kline(
                category="linear",
                symbol=symbol,
                interval=interval,
                limit=limit
            )
            
            if response['retCode'] == 0 and response['result']['list']:
                data = response['result']['list']
                
                # Convert to DataFrame
                df = pd.DataFrame(data, columns=[
                    'timestamp', 'open', 'high', 'low', 'close', 'volume', 'turnover'
                ])
                
                # Convert data types
                df['timestamp'] = pd.to_datetime(df['timestamp'].astype(int), unit='ms')
                for col in ['open', 'high', 'low', 'close', 'volume', 'turnover']:
                    df[col] = df[col].astype(float)
                
                # Sort by timestamp
                df = df.sort_values('timestamp').reset_index(drop=True)
                
                self.cache[cache_key] = (df, time.time())
                return df
                
        except Exception as e:
            logging.error(f"Error fetching kline data for {symbol}: {e}")
        
        return pd.DataFrame()
    
    def calculate_cvd(self, symbol: str) -> Tuple[Optional[float], Optional[str]]:
        """Calculate Cumulative Volume Delta"""
        try:
            # Get recent trades data for CVD calculation
            # This is a simplified version - in reality you'd need tick data
            df = self.fetch_kline_data(symbol, "1", 50)  # 1-minute data
            
            if df.empty:
                return None, "N/A"
            
            # Simplified CVD calculation using price movement and volume
            df['price_change'] = df['close'].diff()
            df['volume_delta'] = np.where(
                df['price_change'] > 0, 
                df['volume'], 
                -df['volume']
            )
            
            # Calculate cumulative volume delta
            cvd = df['volume_delta'].cumsum().iloc[-1]
            
            # Determine trend
            recent_cvd = df['volume_delta'].tail(10).sum()
            trend = "📈 Bullish" if recent_cvd > 0 else "📉 Bearish" if recent_cvd < 0 else "➖ Neutral"
            
            return cvd, trend
            
        except Exception as e:
            logging.error(f"Error calculating CVD for {symbol}: {e}")
            return None, "N/A"
    
    def get_volume_analysis(self, symbol: str) -> Dict[str, any]:
        """Get volume analysis including spikes and averages"""
        try:
            df = self.fetch_kline_data(symbol, "60", 24)  # 24 hours of hourly data
            
            if df.empty:
                return {
                    'current_volume': 0,
                    'avg_volume': 0,
                    'volume_ratio': 0,
                    'is_spike': False
                }
            
            current_volume = df['volume'].iloc[-1]
            avg_volume = df['volume'].mean()
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 0
            
            # Consider it a spike if volume is 2x average
            is_spike = volume_ratio > 2.0
            
            return {
                'current_volume': current_volume,
                'avg_volume': avg_volume,
                'volume_ratio': volume_ratio,
                'is_spike': is_spike
            }
            
        except Exception as e:
            logging.error(f"Error analyzing volume for {symbol}: {e}")
            return {
                'current_volume': 0,
                'avg_volume': 0,
                'volume_ratio': 0,
                'is_spike': False
            }
    
    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for a symbol"""
        cache_key = f"{symbol}_price"
        
        # Check cache
        if cache_key in self.cache:
            data, timestamp = self.cache[cache_key]
            if time.time() - timestamp < 10:  # Price cache for 10 seconds
                return data
        
        try:
            response = self.session.get_tickers(
                category="linear",
                symbol=symbol
            )
            
            if response['retCode'] == 0 and response['result']['list']:
                price = float(response['result']['list'][0]['lastPrice'])
                self.cache[cache_key] = (price, time.time())
                return price
                
        except Exception as e:
            logging.error(f"Error fetching price for {symbol}: {e}")
        
        return None
    
    def get_market_data(self, symbol: str) -> Dict[str, any]:
        """Get comprehensive market data for a symbol"""
        try:
            # Get all data
            price = self.get_current_price(symbol)
            funding_rate, funding_time = self.fetch_funding_rate(symbol)
            oi_value, oi_change = self.fetch_open_interest(symbol)
            cvd_value, cvd_trend = self.calculate_cvd(symbol)
            volume_data = self.get_volume_analysis(symbol)
            
            # Calculate sentiment score
            sentiment_score = self.calculate_sentiment_score(
                funding_rate, oi_change, cvd_value, volume_data['volume_ratio']
            )
            
            return {
                'symbol': symbol,
                'price': price or 0,
                'funding_rate': funding_rate or 0,
                'funding_time': funding_time or "N/A",
                'oi_value': oi_value or 0,
                'oi_change': oi_change or 0,
                'cvd_value': cvd_value or 0,
                'cvd_trend': cvd_trend or "N/A",
                'volume': volume_data['current_volume'],
                'volume_avg': volume_data['avg_volume'],
                'volume_ratio': volume_data['volume_ratio'],
                'volume_spike': volume_data['is_spike'],
                'sentiment_score': sentiment_score,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logging.error(f"Error getting market data for {symbol}: {e}")
            return {
                'symbol': symbol,
                'price': 0,
                'funding_rate': 0,
                'funding_time': "N/A",
                'oi_value': 0,
                'oi_change': 0,
                'cvd_value': 0,
                'cvd_trend': "N/A",
                'volume': 0,
                'volume_avg': 0,
                'volume_ratio': 0,
                'volume_spike': False,
                'sentiment_score': 0,
                'timestamp': datetime.now()
            }
    
    def calculate_sentiment_score(self, funding_rate: float, oi_change: float, 
                                cvd_value: float, volume_ratio: float) -> float:
        """Calculate sentiment score from -1 (bearish) to +1 (bullish)"""
        score = 0.0
        
        # Funding rate component (30% weight)
        if funding_rate:
            if funding_rate > 0.01:  # Very positive funding
                score -= 0.2  # Slightly bearish (overheated longs)
            elif funding_rate > 0.005:
                score += 0.1  # Mildly bullish
            elif funding_rate < -0.01:  # Very negative funding
                score += 0.3  # Bullish (shorts paying, potential squeeze)
            elif funding_rate < -0.005:
                score += 0.1  # Mildly bullish
        
        # Open Interest component (25% weight)
        if oi_change:
            if oi_change > 0.05:  # Strong OI increase
                if funding_rate and funding_rate > 0:
                    score += 0.15  # New longs entering
                else:
                    score -= 0.1   # New shorts entering
            elif oi_change < -0.05:  # Strong OI decrease
                score -= 0.1  # Positions closing
        
        # CVD component (25% weight)
        if cvd_value:
            if cvd_value > 0:
                score += 0.2
            else:
                score -= 0.2
        
        # Volume component (20% weight)
        if volume_ratio > 2.0:  # High volume
            score += 0.1
        elif volume_ratio < 0.5:  # Low volume
            score -= 0.05
        
        return max(-1.0, min(1.0, score))
    
    def get_multiple_symbols_data(self, symbols: List[str]) -> Dict[str, Dict]:
        """Get market data for multiple symbols"""
        results = {}
        
        for symbol in symbols:
            try:
                results[symbol] = self.get_market_data(symbol)
                time.sleep(0.1)  # Small delay to avoid rate limits
            except Exception as e:
                logging.error(f"Error getting data for {symbol}: {e}")
                continue
        
        return results
