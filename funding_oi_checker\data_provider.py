import time
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from pybit.unified_trading import HTTP
from config import api_key, api_secret
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

class DataProvider:
    """Handles all data fetching and processing for the trading dashboard"""

    def __init__(self):
        self.session = HTTP(
            testnet=False,
            api_key=api_key,
            api_secret=api_secret,
        )
        self.cache = {}
        self.cache_duration = 30  # seconds
        self.api_timeout = 10  # API timeout in seconds
        
    def fetch_funding_rate(self, symbol: str) -> Tuple[Optional[float], Optional[str]]:
        """Fetch current funding rate for a symbol"""
        cache_key = f"{symbol}_funding"
        
        # Check cache
        if cache_key in self.cache:
            data, timestamp = self.cache[cache_key]
            if time.time() - timestamp < self.cache_duration:
                return data
        
        try:
            response = self.session.get_funding_rate_history(
                category="linear",
                symbol=symbol,
                limit=1
            )
            
            if response['retCode'] == 0 and response['result']['list']:
                data = response['result']['list'][0]
                funding_rate = float(data['fundingRate'])
                timestamp_str = datetime.fromtimestamp(
                    int(data['fundingRateTimestamp']) / 1000
                ).strftime('%H:%M UTC')
                
                result = (funding_rate, timestamp_str)
                self.cache[cache_key] = (result, time.time())
                return result
                
        except Exception as e:
            logging.error(f"Error fetching funding rate for {symbol}: {e}")
        
        return None, None

    def fetch_asset_list(self) -> List[str]:
        """Fetch list of available trading symbols"""
        cache_key = "asset_list"

        # Check cache (cache for 1 hour)
        if cache_key in self.cache:
            data, timestamp = self.cache[cache_key]
            if time.time() - timestamp < 3600:  # 1 hour cache
                return data

        try:
            response = self.session.get_instruments_info(category="linear")

            if response['retCode'] == 0 and response['result']['list']:
                symbols = []
                for instrument in response['result']['list']:
                    symbol = instrument['symbol']
                    status = instrument['status']
                    # Only include active trading symbols
                    if status == 'Trading' and symbol.endswith('USDT'):
                        symbols.append(symbol)

                # Sort symbols by popularity (BTC, ETH first, then alphabetically)
                def sort_key(symbol):
                    if symbol.startswith('BTC'):
                        return '0' + symbol
                    elif symbol.startswith('ETH'):
                        return '1' + symbol
                    else:
                        return '2' + symbol

                symbols.sort(key=sort_key)

                self.cache[cache_key] = (symbols, time.time())
                return symbols

        except Exception as e:
            logging.error(f"Error fetching asset list: {e}")

        # Return default symbols if fetch fails
        return [
            "BTCUSDT", "ETHUSDT", "SOLUSDT", "ADAUSDT", "DOTUSDT",
            "LINKUSDT", "AVAXUSDT", "MATICUSDT", "ATOMUSDT", "NEARUSDT"
        ]

    def fetch_open_interest(self, symbol: str) -> Tuple[Optional[float], Optional[float]]:
        """Fetch open interest and calculate change"""
        cache_key = f"{symbol}_oi"
        
        # Check cache
        if cache_key in self.cache:
            data, timestamp = self.cache[cache_key]
            if time.time() - timestamp < self.cache_duration:
                return data
        
        try:
            response = self.session.get_open_interest(
                category="linear",
                symbol=symbol,
                intervalTime="1h",
                limit=2
            )
            
            if response['retCode'] == 0 and response['result']['list']:
                data = response['result']['list']
                if len(data) >= 2:
                    current_oi = float(data[0]['openInterest'])
                    previous_oi = float(data[1]['openInterest'])
                    
                    oi_change = (current_oi - previous_oi) / previous_oi if previous_oi > 0 else 0
                    
                    result = (current_oi, oi_change)
                    self.cache[cache_key] = (result, time.time())
                    return result
                    
        except Exception as e:
            # Clean error message to avoid Unicode issues
            error_msg = str(e).replace('→', '->').replace('\u2192', '->')
            logging.error(f"Error fetching open interest for {symbol}: {error_msg}")

        return None, None
    
    def fetch_kline_data(self, symbol: str, interval: str = "60", limit: int = 100) -> pd.DataFrame:
        """Fetch kline data for CVD and volume analysis"""
        cache_key = f"{symbol}_kline_{interval}_{limit}"
        
        # Check cache
        if cache_key in self.cache:
            data, timestamp = self.cache[cache_key]
            if time.time() - timestamp < self.cache_duration:
                return data
        
        try:
            response = self.session.get_kline(
                category="linear",
                symbol=symbol,
                interval=interval,
                limit=limit
            )
            
            if response['retCode'] == 0 and response['result']['list']:
                data = response['result']['list']
                
                # Convert to DataFrame
                df = pd.DataFrame(data, columns=[
                    'timestamp', 'open', 'high', 'low', 'close', 'volume', 'turnover'
                ])

                # Convert data types - fix timestamp conversion
                # Handle large timestamp values by converting to string first
                timestamps = []
                for ts in df['timestamp']:
                    try:
                        # Convert to int, handling large numbers
                        ts_int = int(str(ts))
                        timestamps.append(pd.to_datetime(ts_int, unit='ms'))
                    except (ValueError, OverflowError):
                        # Fallback to current time if conversion fails
                        timestamps.append(pd.Timestamp.now())

                df['timestamp'] = timestamps

                for col in ['open', 'high', 'low', 'close', 'volume', 'turnover']:
                    df[col] = df[col].astype(float)
                
                # Sort by timestamp
                df = df.sort_values('timestamp').reset_index(drop=True)
                
                self.cache[cache_key] = (df, time.time())
                return df
                
        except Exception as e:
            # Clean error message to avoid Unicode issues
            error_msg = str(e).replace('→', '->').replace('\u2192', '->')
            logging.error(f"Error fetching kline data for {symbol}: {error_msg}")

        return pd.DataFrame()
    
    def calculate_cvd(self, symbol: str) -> Tuple[Optional[float], Optional[str]]:
        """Calculate Cumulative Volume Delta"""
        try:
            # Get recent trades data for CVD calculation
            # This is a simplified version - in reality you'd need tick data
            df = self.fetch_kline_data(symbol, "1", 50)  # 1-minute data
            
            if df.empty:
                return None, "N/A"
            
            # Simplified CVD calculation using price movement and volume
            df['price_change'] = df['close'].diff()
            df['volume_delta'] = np.where(
                df['price_change'] > 0, 
                df['volume'], 
                -df['volume']
            )
            
            # Calculate cumulative volume delta
            cvd = df['volume_delta'].cumsum().iloc[-1]
            
            # Determine trend
            recent_cvd = df['volume_delta'].tail(10).sum()
            trend = "📈 Bullish" if recent_cvd > 0 else "📉 Bearish" if recent_cvd < 0 else "➖ Neutral"
            
            return cvd, trend
            
        except Exception as e:
            logging.error(f"Error calculating CVD for {symbol}: {e}")
            return None, "N/A"
    
    def get_volume_analysis(self, symbol: str) -> Dict[str, any]:
        """Get volume analysis including spikes and averages"""
        try:
            df = self.fetch_kline_data(symbol, "60", 24)  # 24 hours of hourly data
            
            if df.empty:
                return {
                    'current_volume': 0,
                    'avg_volume': 0,
                    'volume_ratio': 0,
                    'is_spike': False
                }
            
            current_volume = df['volume'].iloc[-1]
            avg_volume = df['volume'].mean()
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 0
            
            # Consider it a spike if volume is 2x average
            is_spike = volume_ratio > 2.0
            
            return {
                'current_volume': current_volume,
                'avg_volume': avg_volume,
                'volume_ratio': volume_ratio,
                'is_spike': is_spike
            }
            
        except Exception as e:
            logging.error(f"Error analyzing volume for {symbol}: {e}")
            return {
                'current_volume': 0,
                'avg_volume': 0,
                'volume_ratio': 0,
                'is_spike': False
            }
    
    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for a symbol"""
        cache_key = f"{symbol}_price"
        
        # Check cache
        if cache_key in self.cache:
            data, timestamp = self.cache[cache_key]
            if time.time() - timestamp < 10:  # Price cache for 10 seconds
                return data
        
        try:
            response = self.session.get_tickers(
                category="linear",
                symbol=symbol
            )
            
            if response['retCode'] == 0 and response['result']['list']:
                price = float(response['result']['list'][0]['lastPrice'])
                self.cache[cache_key] = (price, time.time())
                return price
                
        except Exception as e:
            logging.error(f"Error fetching price for {symbol}: {e}")
        
        return None
    
    def get_market_data(self, symbol: str) -> Dict[str, any]:
        """Get comprehensive market data for a symbol with robust error handling"""
        try:
            # Initialize with defaults
            result = {
                'symbol': symbol,
                'price': 0,
                'funding_rate': 0,
                'funding_time': "N/A",
                'oi_value': 0,
                'oi_change': 0,
                'cvd_value': 0,
                'cvd_trend': "N/A",
                'volume': 0,
                'volume_avg': 0,
                'volume_ratio': 0,
                'volume_spike': False,
                'sentiment_score': 0,
                'timestamp': datetime.now()
            }

            # Get price (most critical)
            try:
                price = self.get_current_price(symbol)
                if price:
                    result['price'] = price
            except Exception as e:
                logging.warning(f"Failed to get price for {symbol}: {e}")

            # Small delay to avoid overwhelming API
            time.sleep(0.05)

            # Get funding rate
            try:
                funding_rate, funding_time = self.fetch_funding_rate(symbol)
                if funding_rate is not None:
                    result['funding_rate'] = funding_rate
                if funding_time:
                    result['funding_time'] = funding_time
            except Exception as e:
                logging.warning(f"Failed to get funding rate for {symbol}: {e}")

            time.sleep(0.05)

            # Get open interest (can be slow/fail)
            try:
                oi_value, oi_change = self.fetch_open_interest(symbol)
                if oi_value is not None:
                    result['oi_value'] = oi_value
                if oi_change is not None:
                    result['oi_change'] = oi_change
            except Exception as e:
                logging.warning(f"Failed to get OI for {symbol}: {e}")

            # Get CVD (can be slow/fail)
            try:
                cvd_value, cvd_trend = self.calculate_cvd(symbol)
                if cvd_value is not None:
                    result['cvd_value'] = cvd_value
                if cvd_trend:
                    result['cvd_trend'] = cvd_trend
            except Exception as e:
                logging.warning(f"Failed to get CVD for {symbol}: {e}")

            # Get volume analysis (can be slow/fail)
            try:
                volume_data = self.get_volume_analysis(symbol)
                if volume_data:
                    result['volume'] = volume_data.get('current_volume', 0)
                    result['volume_avg'] = volume_data.get('avg_volume', 0)
                    result['volume_ratio'] = volume_data.get('volume_ratio', 0)
                    result['volume_spike'] = volume_data.get('is_spike', False)
            except Exception as e:
                logging.warning(f"Failed to get volume for {symbol}: {e}")

            # Calculate sentiment score with available data
            try:
                result['sentiment_score'] = self.calculate_sentiment_score(
                    result['funding_rate'],
                    result['oi_change'],
                    result['cvd_value'],
                    result['volume_ratio']
                )
            except Exception as e:
                logging.warning(f"Failed to calculate sentiment for {symbol}: {e}")

            return result
            
        except Exception as e:
            logging.error(f"Error getting market data for {symbol}: {e}")
            return {
                'symbol': symbol,
                'price': 0,
                'funding_rate': 0,
                'funding_time': "N/A",
                'oi_value': 0,
                'oi_change': 0,
                'cvd_value': 0,
                'cvd_trend': "N/A",
                'volume': 0,
                'volume_avg': 0,
                'volume_ratio': 0,
                'volume_spike': False,
                'sentiment_score': 0,
                'timestamp': datetime.now()
            }
    
    def calculate_sentiment_score(self, funding_rate: float, oi_change: float, 
                                cvd_value: float, volume_ratio: float) -> float:
        """Calculate sentiment score from -1 (bearish) to +1 (bullish)"""
        score = 0.0
        
        # Funding rate component (30% weight)
        if funding_rate:
            if funding_rate > 0.01:  # Very positive funding
                score -= 0.2  # Slightly bearish (overheated longs)
            elif funding_rate > 0.005:
                score += 0.1  # Mildly bullish
            elif funding_rate < -0.01:  # Very negative funding
                score += 0.3  # Bullish (shorts paying, potential squeeze)
            elif funding_rate < -0.005:
                score += 0.1  # Mildly bullish
        
        # Open Interest component (25% weight)
        if oi_change:
            if oi_change > 0.05:  # Strong OI increase
                if funding_rate and funding_rate > 0:
                    score += 0.15  # New longs entering
                else:
                    score -= 0.1   # New shorts entering
            elif oi_change < -0.05:  # Strong OI decrease
                score -= 0.1  # Positions closing
        
        # CVD component (25% weight)
        if cvd_value:
            if cvd_value > 0:
                score += 0.2
            else:
                score -= 0.2
        
        # Volume component (20% weight)
        if volume_ratio > 2.0:  # High volume
            score += 0.1
        elif volume_ratio < 0.5:  # Low volume
            score -= 0.05
        
        return max(-1.0, min(1.0, score))
    
    def get_multiple_symbols_data(self, symbols: List[str], batch_size: int = 50) -> Dict[str, Dict]:
        """Get market data for multiple symbols with batching for large lists"""
        results = {}
        failed_symbols = []

        logging.info(f"Fetching data for {len(symbols)} symbols in batches of {batch_size}...")

        # Process symbols in batches
        for batch_start in range(0, len(symbols), batch_size):
            batch_end = min(batch_start + batch_size, len(symbols))
            batch_symbols = symbols[batch_start:batch_end]

            logging.info(f"Processing batch {batch_start//batch_size + 1}/{(len(symbols)-1)//batch_size + 1}: symbols {batch_start+1}-{batch_end}")

            batch_results = self._process_symbol_batch(batch_symbols, batch_start)
            results.update(batch_results['successful'])
            failed_symbols.extend(batch_results['failed'])

            # Longer pause between batches to avoid rate limits
            if batch_end < len(symbols):
                time.sleep(1.0)

        logging.info(f"Data fetch completed: {len(results)} successful, {len(failed_symbols)} failed")

        if failed_symbols:
            logging.warning(f"Failed symbols: {', '.join(failed_symbols[:10])}")

        return results

    def _process_symbol_batch(self, symbols: List[str], batch_offset: int = 0) -> Dict[str, any]:
        """Process a batch of symbols"""
        successful = {}
        failed = []

        for i, symbol in enumerate(symbols):
            try:
                # Progress logging every 10 symbols within batch
                if i % 10 == 0:
                    global_index = batch_offset + i + 1
                    logging.debug(f"Processing symbol {global_index}: {symbol}")

                # Get data
                data = self.get_market_data(symbol)

                # Validate data
                if data and data.get('price', 0) > 0:
                    successful[symbol] = data
                else:
                    logging.warning(f"Invalid data received for {symbol}")
                    failed.append(symbol)

                # Rate limiting within batch
                time.sleep(0.1)

            except Exception as e:
                logging.error(f"Error getting data for {symbol}: {e}")
                failed.append(symbol)
                continue

        return {'successful': successful, 'failed': failed}

    def get_multiple_symbols_data_concurrent(self, symbols: List[str], max_workers: int = 10, progress_callback=None) -> Dict[str, Dict]:
        """Get market data using concurrent processing - MUCH faster and non-blocking"""
        results = {}
        failed_symbols = []

        # Filter out invalid symbols
        valid_symbols = self._filter_valid_symbols(symbols)
        total_symbols = len(valid_symbols)
        completed = 0

        if len(valid_symbols) != len(symbols):
            logging.warning(f"Filtered out {len(symbols) - len(valid_symbols)} invalid symbols")

        logging.info(f"Fetching data for {total_symbols} symbols using {max_workers} concurrent workers...")

        if progress_callback:
            progress_callback("Starting concurrent data fetch...", 0)

        # Use ThreadPoolExecutor for concurrent API calls
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks for valid symbols only
            future_to_symbol = {
                executor.submit(self._get_symbol_data_safe, symbol): symbol
                for symbol in valid_symbols
            }

            # Process completed tasks as they finish
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                completed += 1

                try:
                    data = future.result(timeout=10)  # 10 second timeout per symbol

                    if data and data.get('price', 0) > 0:
                        results[symbol] = data
                    else:
                        failed_symbols.append(symbol)

                except Exception as e:
                    logging.error(f"Error getting data for {symbol}: {e}")
                    failed_symbols.append(symbol)

                # Update progress
                if progress_callback and completed % 10 == 0:  # Update every 10 completions
                    progress_pct = int((completed / total_symbols) * 100)
                    progress_callback(f"Processed {completed}/{total_symbols} symbols...", progress_pct)

        if progress_callback:
            progress_callback(f"Concurrent fetch completed: {len(results)} successful, {len(failed_symbols)} failed", 100)

        logging.info(f"Concurrent fetch completed: {len(results)} successful, {len(failed_symbols)} failed")

        if failed_symbols:
            logging.warning(f"Failed symbols: {', '.join(failed_symbols[:10])}")

        return results

    def _get_symbol_data_safe(self, symbol: str) -> Optional[Dict]:
        """Thread-safe wrapper for getting single symbol data"""
        try:
            return self.get_market_data(symbol)
        except Exception as e:
            logging.error(f"Error in thread for {symbol}: {e}")
            return None

    def get_multiple_symbols_data_with_progress(self, symbols: List[str], batch_size: int = 25, progress_callback=None) -> Dict[str, Dict]:
        """Get market data with real-time progress updates to prevent UI freezing"""
        results = {}
        failed_symbols = []
        total_symbols = len(symbols)

        logging.info(f"Fetching data for {total_symbols} symbols with progress updates...")

        if progress_callback:
            progress_callback("Initializing data fetch...", 0)

        # Process symbols in smaller batches with progress updates
        for batch_start in range(0, total_symbols, batch_size):
            batch_end = min(batch_start + batch_size, total_symbols)
            batch_symbols = symbols[batch_start:batch_end]

            # Calculate progress
            progress_pct = int((batch_start / total_symbols) * 100)
            batch_num = (batch_start // batch_size) + 1
            total_batches = (total_symbols - 1) // batch_size + 1

            if progress_callback:
                progress_callback(f"Processing batch {batch_num}/{total_batches} ({len(batch_symbols)} symbols)...", progress_pct)

            logging.info(f"Processing batch {batch_num}/{total_batches}: symbols {batch_start+1}-{batch_end}")

            # Process batch with individual symbol progress
            batch_results = self._process_symbol_batch_with_progress(
                batch_symbols, batch_start, progress_callback, total_symbols
            )

            results.update(batch_results['successful'])
            failed_symbols.extend(batch_results['failed'])

            # Update progress after batch completion
            if progress_callback:
                progress_pct = int((batch_end / total_symbols) * 100)
                progress_callback(f"Batch {batch_num} completed ({len(batch_results['successful'])} successful)", progress_pct)

            # Shorter pause between batches for better responsiveness
            if batch_end < total_symbols:
                time.sleep(0.5)

        if progress_callback:
            progress_callback(f"Data fetch completed: {len(results)} successful, {len(failed_symbols)} failed", 100)

        logging.info(f"Data fetch completed: {len(results)} successful, {len(failed_symbols)} failed")

        if failed_symbols:
            logging.warning(f"Failed symbols: {', '.join(failed_symbols[:10])}")

        return results

    def _process_symbol_batch_with_progress(self, symbols: List[str], batch_offset: int, progress_callback, total_symbols: int) -> Dict[str, any]:
        """Process a batch of symbols with individual progress updates"""
        successful = {}
        failed = []

        for i, symbol in enumerate(symbols):
            try:
                # Update progress for individual symbols within batch
                if progress_callback and i % 5 == 0:  # Update every 5 symbols
                    global_index = batch_offset + i + 1
                    progress_pct = int((global_index / total_symbols) * 100)
                    progress_callback(f"Processing {symbol} ({global_index}/{total_symbols})...", progress_pct)

                # Get data with shorter timeout for responsiveness
                data = self.get_market_data(symbol)

                # Validate data
                if data and data.get('price', 0) > 0:
                    successful[symbol] = data
                else:
                    logging.warning(f"Invalid data received for {symbol}")
                    failed.append(symbol)

                # Very short delay for responsiveness
                time.sleep(0.05)

            except Exception as e:
                logging.error(f"Error getting data for {symbol}: {e}")
                failed.append(symbol)
                continue

        return {'successful': successful, 'failed': failed}

    def _filter_valid_symbols(self, symbols: List[str]) -> List[str]:
        """Filter out invalid symbols"""
        valid_symbols = []

        for symbol in symbols:
            # Clean and validate symbol
            clean_symbol = str(symbol).strip().upper()

            # Skip invalid symbols
            if (len(clean_symbol) < 3 or
                len(clean_symbol) > 20 or
                '...' in clean_symbol or
                '(' in clean_symbol or
                ')' in clean_symbol or
                '+' in clean_symbol or
                not clean_symbol.replace('USDT', '').replace('PERP', '').isalnum()):
                logging.warning(f"Skipping invalid symbol: {symbol}")
                continue

            valid_symbols.append(clean_symbol)

        return valid_symbols
